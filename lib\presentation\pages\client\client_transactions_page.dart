import 'package:flutter/material.dart';
import 'package:untitled/data/datasources/irrigation_datasource.dart';
import 'package:untitled/data/datasources/payment_datasource.dart';
import 'package:untitled/data/models/irrigation_model.dart';
import 'package:untitled/data/models/payment_model.dart';
import 'package:untitled/data/datasources/client_transfer_datasource.dart';
import 'package:untitled/data/models/client_transfer_model.dart';
import 'package:untitled/data/datasources/client_datasource.dart';
import 'package:untitled/data/datasources/farm_datasource.dart';
import 'package:untitled/data/datasources/cashbox_datasource.dart';
import 'package:untitled/data/models/client_model.dart';
import 'package:untitled/data/models/farm_model.dart';
import 'package:untitled/data/models/cashbox_model.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'dart:typed_data';
import 'package:pdf/pdf.dart';
// TODO: استيراد DataSource الخاص بالتحويلات عند توفره

class TransactionViewModel {
  final String type; // نوع العملية (دفع، تسقية، تحويل...)
  final DateTime date;
  final double cashChange; // التغير النقدي
  final double dieselChange; // التغير في الديزل
  final String description;
  final double cashBefore;
  final double dieselBefore;
  final double cashAfter;
  final double dieselAfter;

  TransactionViewModel({
    required this.type,
    required this.date,
    required this.cashChange,
    required this.dieselChange,
    required this.description,
    required this.cashBefore,
    required this.dieselBefore,
    required this.cashAfter,
    required this.dieselAfter,
  });
}

class ClientTransactionsPage extends StatefulWidget {
  final int? clientId;
  final String? clientName;
  final DateTime? startDate;
  final DateTime? endDate;

  const ClientTransactionsPage({
    Key? key,
    this.clientId,
    this.clientName,
    this.startDate,
    this.endDate,
  }) : super(key: key);

  @override
  State<ClientTransactionsPage> createState() => _ClientTransactionsPageState();
}

class _ClientTransactionsPageState extends State<ClientTransactionsPage> {
  final IrrigationDataSource _irrigationDataSource = IrrigationDataSource();
  final PaymentDataSource _paymentDataSource = PaymentDataSource();
  final ClientTransferDataSource _transferDataSource =
      ClientTransferDataSource();
  final ClientDataSource _clientDataSource = ClientDataSource();
  final FarmDataSource _farmDataSource = FarmDataSource();
  final CashboxDataSource _cashboxDataSource = CashboxDataSource();
  // TODO: إضافة DataSource الخاص بالتحويلات
  String _selectedFilter = 'all'; // all, payments, irrigations, transfers
  String _directionFilter = 'all'; // all, sent, received

  @override
  void initState() {
    super.initState();
    // لم يعد هناك حاجة لتهيئة DataSource هنا
  }

  Future<List<TransactionViewModel>> _fetchAllTransactions() async {
    final List<TransactionViewModel> txs = [];
    double cash = 0;
    double diesel = 0;
    // جلب الأسماء
    final clients = await _clientDataSource.getAllClients();
    final farms = await _farmDataSource.getAllFarms();
    final cashboxes = await _cashboxDataSource.getAllCashboxes();
    final clientNames = {for (var c in clients) c.id: c.name};
    final farmNames = {for (var f in farms) f.id: f.name};
    final cashboxNames = {for (var c in cashboxes) c.id: c.name};
    // 1. جلب المدفوعات
    final payments = widget.clientId != null
        ? await _paymentDataSource.getPaymentsByClientId(widget.clientId!)
        : <PaymentModel>[];
    for (final p in payments) {
      final isCash = p.type == 'cash';
      final isDiesel = p.type == 'diesel';
      final cashChange = isCash ? p.amount : 0.0;
      final dieselChange = isDiesel ? p.amount : 0.0;
      final cashboxName = cashboxNames[p.cashboxId] ?? 'غير معروف';
      String desc = isCash
          ? 'دفعة نقدية من الصندوق: $cashboxName'
          : 'دفعة ديزل من الصندوق: $cashboxName';
      if ((p.notes ?? '').isNotEmpty) desc += ' (${p.notes})';
      txs.add(TransactionViewModel(
        type: 'دفعة',
        date: p.createdAt,
        cashChange: cashChange,
        dieselChange: dieselChange,
        description: desc,
        cashBefore: cash,
        dieselBefore: diesel,
        cashAfter: cash + cashChange,
        dieselAfter: diesel + dieselChange,
      ));
      cash += cashChange;
      diesel += dieselChange;
    }
    // 2. جلب التسقيات
    final irrigations = widget.clientId != null
        ? await _irrigationDataSource.getIrrigationsByClientId(widget.clientId!)
        : <IrrigationModel>[];
    for (final i in irrigations) {
      final farmName = farmNames[i.farmId] ?? 'غير معروف';
      txs.add(TransactionViewModel(
        type: 'تسقية',
        date: i.startTime,
        cashChange: -i.cost,
        dieselChange: -i.dieselConsumption,
        description: 'مقابل تسقية المزرعة: $farmName',
        cashBefore: cash,
        dieselBefore: diesel,
        cashAfter: cash - i.cost,
        dieselAfter: diesel - i.dieselConsumption,
      ));
      cash -= i.cost;
      diesel -= i.dieselConsumption;
    }
    // 3. جلب التحويلات
    final transfers = widget.clientId != null
        ? await _transferDataSource.getTransfersByClientId(widget.clientId!)
        : <ClientTransferModel>[];
    for (final t in transfers) {
      if (t.fromClientId == widget.clientId) {
        // العميل هو المرسل
        String desc;
        if (t.toClientId == -1) {
          // تحويل إلى صندوق
          final cashboxName = cashboxNames[-1] ?? 'الصندوق';
          if (t.cashAmount > 0 && t.dieselAmount > 0) {
            desc = 'تحويل نقد وديزل إلى الصندوق: ' +
                '$cashboxName (${t.cashAmount.toStringAsFixed(2)} ريال و${t.dieselAmount.toStringAsFixed(2)} لتر)';
          } else if (t.cashAmount > 0) {
            desc = 'تحويل نقد إلى الصندوق: ' +
                '$cashboxName (${t.cashAmount.toStringAsFixed(2)} ريال)';
          } else if (t.dieselAmount > 0) {
            desc = 'تحويل ديزل إلى الصندوق: ' +
                '$cashboxName (${t.dieselAmount.toStringAsFixed(2)} لتر)';
          } else {
            desc = 'تحويل إلى الصندوق: $cashboxName';
          }
        } else {
          // تحويل إلى عميل آخر
          final toName = clientNames[t.toClientId] ?? 'غير معروف';
          if (t.cashAmount > 0 && t.dieselAmount > 0) {
            desc = 'تحويل نقد وديزل إلى عميل: ' +
                '$toName (${t.cashAmount.toStringAsFixed(2)} ريال و${t.dieselAmount.toStringAsFixed(2)} لتر)';
          } else if (t.cashAmount > 0) {
            desc = 'تحويل نقد إلى عميل: ' +
                '$toName (${t.cashAmount.toStringAsFixed(2)} ريال)';
          } else if (t.dieselAmount > 0) {
            desc = 'تحويل ديزل إلى عميل: ' +
                '$toName (${t.dieselAmount.toStringAsFixed(2)} لتر)';
          } else {
            desc = 'تحويل إلى عميل: $toName';
          }
        }
        if ((t.notes ?? '').isNotEmpty) desc += ' (${t.notes})';
        txs.add(TransactionViewModel(
          type: 'تحويل مرسل',
          date: t.createdAt,
          cashChange: -t.cashAmount,
          dieselChange: -t.dieselAmount,
          description: desc,
          cashBefore: cash,
          dieselBefore: diesel,
          cashAfter: cash - t.cashAmount,
          dieselAfter: diesel - t.dieselAmount,
        ));
        cash -= t.cashAmount;
        diesel -= t.dieselAmount;
      } else if (t.toClientId == widget.clientId) {
        // العميل هو المستقبل
        String desc;
        if (t.fromClientId == -1) {
          // تحويل من صندوق
          final cashboxName = cashboxNames[-1] ?? 'الصندوق';
          if (t.cashAmount > 0 && t.dieselAmount > 0) {
            desc = 'استلام نقد وديزل من الصندوق: ' +
                '$cashboxName (${t.cashAmount.toStringAsFixed(2)} ريال و${t.dieselAmount.toStringAsFixed(2)} لتر)';
          } else if (t.cashAmount > 0) {
            desc = 'استلام نقد من الصندوق: ' +
                '$cashboxName (${t.cashAmount.toStringAsFixed(2)} ريال)';
          } else if (t.dieselAmount > 0) {
            desc = 'استلام ديزل من الصندوق: ' +
                '$cashboxName (${t.dieselAmount.toStringAsFixed(2)} لتر)';
          } else {
            desc = 'استلام من الصندوق: $cashboxName';
          }
        } else {
          // تحويل من عميل آخر
          final fromName = clientNames[t.fromClientId] ?? 'غير معروف';
          if (t.cashAmount > 0 && t.dieselAmount > 0) {
            desc = 'استلام نقد وديزل من عميل: ' +
                '$fromName (${t.cashAmount.toStringAsFixed(2)} ريال و${t.dieselAmount.toStringAsFixed(2)} لتر)';
          } else if (t.cashAmount > 0) {
            desc = 'استلام نقد من عميل: ' +
                '$fromName (${t.cashAmount.toStringAsFixed(2)} ريال)';
          } else if (t.dieselAmount > 0) {
            desc = 'استلام ديزل من عميل: ' +
                '$fromName (${t.dieselAmount.toStringAsFixed(2)} لتر)';
          } else {
            desc = 'استلام من عميل: $fromName';
          }
        }
        if ((t.notes ?? '').isNotEmpty) desc += ' (${t.notes})';
        txs.add(TransactionViewModel(
          type: 'تحويل مستقبل',
          date: t.createdAt,
          cashChange: t.cashAmount,
          dieselChange: t.dieselAmount,
          description: desc,
          cashBefore: cash,
          dieselBefore: diesel,
          cashAfter: cash + t.cashAmount,
          dieselAfter: diesel + t.dieselAmount,
        ));
        cash += t.cashAmount;
        diesel += t.dieselAmount;
      }
    }
    // 4. دمج وترتيب العمليات حسب التاريخ
    txs.sort((a, b) => a.date.compareTo(b.date));
    // 5. إعادة احتساب الرصيد قبل وبعد لكل عملية (لضمان التسلسل)
    double runningCash = 0;
    double runningDiesel = 0;
    for (int i = 0; i < txs.length; i++) {
      final tx = txs[i];
      final beforeCash = runningCash;
      final beforeDiesel = runningDiesel;
      runningCash += tx.cashChange;
      runningDiesel += tx.dieselChange;
      txs[i] = TransactionViewModel(
        type: tx.type,
        date: tx.date,
        cashChange: tx.cashChange,
        dieselChange: tx.dieselChange,
        description: tx.description,
        cashBefore: beforeCash,
        dieselBefore: beforeDiesel,
        cashAfter: runningCash,
        dieselAfter: runningDiesel,
      );
    }
    return txs;
  }

  void _showPreviewDialog(List<TransactionViewModel> txs) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('معاينة كشف الحساب'),
        content: SizedBox(
          width: double.maxFinite,
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: DataTable(
              columns: const [
                DataColumn(label: Text('التاريخ')),
                DataColumn(label: Text('النوع')),
                DataColumn(label: Text('الوصف')),
                DataColumn(label: Text('نقدي')),
                DataColumn(label: Text('ديزل')),
                DataColumn(label: Text('الرصيد بعد')),
              ],
              rows: txs.map((tx) {
                Color rowColor;
                IconData icon;
                switch (tx.type) {
                  case 'دفعة':
                    rowColor = Colors.green.shade50;
                    icon = Icons.attach_money;
                    break;
                  case 'تسقية':
                    rowColor = Colors.blue.shade50;
                    icon = Icons.water_drop;
                    break;
                  case 'تحويل مرسل':
                    rowColor = Colors.orange.shade50;
                    icon = Icons.arrow_upward;
                    break;
                  case 'تحويل مستقبل':
                    rowColor = Colors.purple.shade50;
                    icon = Icons.arrow_downward;
                    break;
                  default:
                    rowColor = Colors.grey.shade50;
                    icon = Icons.swap_horiz;
                }
                return DataRow(
                  color: MaterialStateProperty.all(rowColor),
                  cells: [
                    DataCell(Text(tx.date.toString().split(" ")[0],
                        style: const TextStyle(fontFamily: 'Cairo'))),
                    DataCell(Row(
                      children: [
                        Icon(icon, size: 18),
                        const SizedBox(width: 4),
                        Text(tx.type),
                      ],
                    )),
                    DataCell(Text(_getSimpleDescription(tx.description))),
                    DataCell(Text(
                      (tx.cashChange > 0 ? '+' : '') +
                          tx.cashChange.toStringAsFixed(2),
                      style: TextStyle(
                          color: tx.cashChange > 0
                              ? Colors.green
                              : (tx.cashChange < 0
                                  ? Colors.red
                                  : Colors.black)),
                    )),
                    DataCell(Text(
                      (tx.dieselChange > 0 ? '+' : '') +
                          tx.dieselChange.toStringAsFixed(2),
                      style: TextStyle(
                          color: tx.dieselChange > 0
                              ? Colors.blue
                              : (tx.dieselChange < 0
                                  ? Colors.red
                                  : Colors.black)),
                    )),
                    DataCell(Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('نقدي: ${tx.cashAfter.toStringAsFixed(2)}'),
                        Text('ديزل: ${tx.dieselAfter.toStringAsFixed(2)}'),
                      ],
                    )),
                  ],
                );
              }).toList(),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  // دالة تبسيط الشرح
  String _getSimpleDescription(String desc) {
    // أمثلة على التبسيط
    if (desc.contains('تحويل نقد إلى عميل:')) return desc;
    if (desc.contains('تحويل ديزل إلى عميل:')) return desc;
    if (desc.contains('تحويل نقد إلى الصندوق:')) return desc;
    if (desc.contains('تحويل ديزل إلى الصندوق:')) return desc;
    if (desc.contains('استلام نقد من عميل:')) return desc;
    if (desc.contains('استلام ديزل من عميل:')) return desc;
    if (desc.contains('استلام نقد من الصندوق:')) return desc;
    if (desc.contains('استلام ديزل من الصندوق:')) return desc;
    if (desc.contains('دفعة نقدية')) return 'دفعة نقدية من الصندوق';
    if (desc.contains('دفعة ديزل')) return 'دفعة ديزل من الصندوق';
    if (desc.contains('مقابل تسقية')) return 'تسقية مزرعة';
    return desc.length > 30 ? desc.substring(0, 30) + '...' : desc;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('معاملات العميل: ${widget.clientName ?? ''}'),
        actions: [
          IconButton(
            icon: const Icon(Icons.picture_as_pdf),
            tooltip: 'تصدير PDF',
            onPressed: () async {
              final txs = await _fetchAllTransactions();
              await _exportPdf(context, txs);
            },
          ),
          IconButton(
            icon: const Icon(Icons.preview),
            tooltip: 'معاينة كشف الحساب',
            onPressed: () async {
              final txs = await _fetchAllTransactions();
              _showPreviewDialog(_applyFilter(_applyDirectionFilter(txs)));
            },
          ),
        ],
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              children: [
                const Text('فلترة: '),
                DropdownButton<String>(
                  value: _selectedFilter,
                  items: const [
                    DropdownMenuItem(
                        value: 'all', child: Text('جميع العمليات')),
                    DropdownMenuItem(value: 'payments', child: Text('الدفعات')),
                    DropdownMenuItem(
                        value: 'irrigations', child: Text('التسقيات')),
                    DropdownMenuItem(
                        value: 'transfers', child: Text('التحويلات')),
                  ],
                  onChanged: (val) {
                    setState(() {
                      _selectedFilter = val ?? 'all';
                    });
                  },
                ),
                const SizedBox(width: 16),
                const Text('من/إلى: '),
                DropdownButton<String>(
                  value: _directionFilter,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('الكل')),
                    DropdownMenuItem(value: 'sent', child: Text('من حسابك')),
                    DropdownMenuItem(
                        value: 'received', child: Text('إلى حسابك')),
                  ],
                  onChanged: (val) {
                    setState(() {
                      _directionFilter = val ?? 'all';
                    });
                  },
                ),
              ],
            ),
          ),
          Expanded(
            child: FutureBuilder<List<TransactionViewModel>>(
              future: _fetchAllTransactions(),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                }
                if (snapshot.hasError) {
                  return Center(child: Text('حدث خطأ: ${snapshot.error}'));
                }
                final txs =
                    _applyFilter(_applyDirectionFilter(snapshot.data ?? []));
                if (txs.isEmpty) {
                  return const Center(
                      child: Text('لا توجد عمليات لهذا العميل'));
                }
                // --- حساب الإجماليات ---
                double totalCash = 0;
                double totalDiesel = 0;
                for (final tx in txs) {
                  totalCash += tx.cashChange;
                  totalDiesel += tx.dieselChange;
                }
                // --- عرض عصري باستخدام بطاقات ---
                return Column(
                  children: [
                    Expanded(
                      child: ListView.separated(
                        padding: const EdgeInsets.all(16),
                        itemCount: txs.length,
                        separatorBuilder: (_, __) => const SizedBox(height: 10),
                        itemBuilder: (context, i) {
                          final tx = txs[i];
                          Color color;
                          IconData icon;
                          switch (tx.type) {
                            case 'دفعة':
                              color = Colors.green;
                              icon = Icons.attach_money;
                              break;
                            case 'تسقية':
                              color = Colors.blue;
                              icon = Icons.water_drop;
                              break;
                            case 'تحويل مرسل':
                              color = Colors.orange;
                              icon = Icons.arrow_upward;
                              break;
                            case 'تحويل مستقبل':
                              color = Colors.purple;
                              icon = Icons.arrow_downward;
                              break;
                            default:
                              color = Colors.grey;
                              icon = Icons.swap_horiz;
                          }
                          return Card(
                            elevation: 2,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(12),
                              child: Row(
                                children: [
                                  CircleAvatar(
                                    backgroundColor: color.withOpacity(0.15),
                                    child: Icon(icon, color: color),
                                  ),
                                  const SizedBox(width: 12),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Row(
                                          children: [
                                            Text(
                                              tx.type,
                                              style: TextStyle(
                                                  color: color,
                                                  fontWeight: FontWeight.bold),
                                            ),
                                            const SizedBox(width: 8),
                                            Text(
                                              tx.date.toString().split(" ")[0],
                                              style: const TextStyle(
                                                  color: Colors.grey,
                                                  fontSize: 13),
                                            ),
                                          ],
                                        ),
                                        const SizedBox(height: 4),
                                        Text(
                                          _getSimpleDescription(tx.description),
                                          style: const TextStyle(fontSize: 15),
                                        ),
                                        const SizedBox(height: 6),
                                        Row(
                                          children: [
                                            Icon(Icons.payments,
                                                size: 16, color: Colors.green),
                                            const SizedBox(width: 2),
                                            Text(
                                              (tx.cashChange > 0 ? '+' : '') +
                                                  tx.cashChange
                                                      .toStringAsFixed(2),
                                              style: TextStyle(
                                                  color: tx.cashChange > 0
                                                      ? Colors.green
                                                      : (tx.cashChange < 0
                                                          ? Colors.red
                                                          : Colors.black),
                                                  fontWeight: FontWeight.bold),
                                            ),
                                            const SizedBox(width: 12),
                                            Icon(Icons.local_gas_station,
                                                size: 16, color: Colors.blue),
                                            const SizedBox(width: 2),
                                            Text(
                                              (tx.dieselChange > 0 ? '+' : '') +
                                                  tx.dieselChange
                                                      .toStringAsFixed(2),
                                              style: TextStyle(
                                                  color: tx.dieselChange > 0
                                                      ? Colors.blue
                                                      : (tx.dieselChange < 0
                                                          ? Colors.red
                                                          : Colors.black),
                                                  fontWeight: FontWeight.bold),
                                            ),
                                            const SizedBox(width: 12),
                                            Text(
                                              'الرصيد بعد: ',
                                              style: const TextStyle(
                                                  fontSize: 12,
                                                  color: Colors.grey),
                                            ),
                                            Text(
                                              '${tx.cashAfter.toStringAsFixed(2)} ر.س | ${tx.dieselAfter.toStringAsFixed(2)} لتر',
                                              style: const TextStyle(
                                                  fontSize: 12,
                                                  color: Colors.black),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                    // --- صف الإجمالي ---
                    Container(
                      width: double.infinity,
                      margin: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 8),
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade100,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.grey.shade300),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.grey.withOpacity(0.08),
                            blurRadius: 6,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text('الإجمالي:',
                              style: TextStyle(
                                  fontWeight: FontWeight.bold, fontSize: 16)),
                          Row(
                            children: [
                              Icon(Icons.payments,
                                  size: 18,
                                  color: totalCash >= 0
                                      ? Colors.green
                                      : Colors.red),
                              const SizedBox(width: 4),
                              Text(
                                totalCash.toStringAsFixed(2),
                                style: TextStyle(
                                    color: totalCash >= 0
                                        ? Colors.green
                                        : Colors.red,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16),
                              ),
                              const SizedBox(width: 16),
                              Icon(Icons.local_gas_station,
                                  size: 18,
                                  color: totalDiesel >= 0
                                      ? Colors.blue
                                      : Colors.red),
                              const SizedBox(width: 4),
                              Text(
                                totalDiesel.toStringAsFixed(2),
                                style: TextStyle(
                                    color: totalDiesel >= 0
                                        ? Colors.blue
                                        : Colors.red,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  List<TransactionViewModel> _applyFilter(List<TransactionViewModel> txs) {
    switch (_selectedFilter) {
      case 'payments':
        return txs.where((tx) => tx.type == 'دفعة').toList();
      case 'irrigations':
        return txs.where((tx) => tx.type == 'تسقية').toList();
      case 'transfers':
        return txs
            .where((tx) => tx.type == 'تحويل مرسل' || tx.type == 'تحويل مستقبل')
            .toList();
      default:
        return txs;
    }
  }

  List<TransactionViewModel> _applyDirectionFilter(
      List<TransactionViewModel> txs) {
    switch (_directionFilter) {
      case 'sent':
        return txs.where((tx) => tx.type == 'تحويل مرسل').toList();
      case 'received':
        return txs.where((tx) => tx.type == 'تحويل مستقبل').toList();
      default:
        return txs;
    }
  }

  // دالة تصدير PDF
  Future<void> _exportPdf(
      BuildContext context, List<TransactionViewModel> txs) async {
    final pdf = pw.Document();
    final totalCash = txs.fold<double>(0, (sum, tx) => sum + tx.cashChange);
    final totalDiesel = txs.fold<double>(0, (sum, tx) => sum + tx.dieselChange);
    final font = await PdfGoogleFonts.cairoRegular();
    final boldFont = await PdfGoogleFonts.cairoBold();
    pdf.addPage(
      pw.MultiPage(
        theme: pw.ThemeData.withFont(
          base: font,
          bold: boldFont,
        ),
        textDirection: pw.TextDirection.rtl,
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(20),
        build: (context) => [
          pw.Header(
            level: 0,
            child: pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                pw.Text('كشف حساب عميل',
                    style: pw.TextStyle(
                        font: boldFont,
                        fontSize: 22,
                        fontWeight: pw.FontWeight.bold)),
                pw.Text(
                    'تاريخ الطباعة: ${DateTime.now().toString().split(' ')[0]}',
                    style: pw.TextStyle(font: font, fontSize: 12)),
              ],
            ),
          ),
          pw.SizedBox(height: 8),
          pw.Text('اسم العميل: ${widget.clientName ?? ''}',
              style: pw.TextStyle(font: font, fontSize: 16)),
          pw.SizedBox(height: 4),
          pw.Text('عدد العمليات: ${txs.length}',
              style: pw.TextStyle(font: font, fontSize: 12)),
          pw.SizedBox(height: 12),
          pw.Table.fromTextArray(
            cellAlignment: pw.Alignment.center,
            headerStyle: pw.TextStyle(
                font: boldFont, fontWeight: pw.FontWeight.bold, fontSize: 12),
            cellStyle: pw.TextStyle(font: font, fontSize: 11),
            headerDecoration:
                pw.BoxDecoration(color: PdfColor.fromInt(0xFFE0E0E0)),
            rowDecoration: pw.BoxDecoration(),
            headers: [
              'التاريخ',
              'النوع',
              'الوصف',
              'نقدي',
              'ديزل',
              'رصيد نقدي',
              'رصيد ديزل',
            ],
            data: [
              ...txs.map((tx) => [
                    tx.date.toString().split(' ')[0],
                    tx.type,
                    _getSimpleDescription(tx.description),
                    (tx.cashChange > 0 ? '+' : '') +
                        tx.cashChange.toStringAsFixed(2),
                    (tx.dieselChange > 0 ? '+' : '') +
                        tx.dieselChange.toStringAsFixed(2),
                    tx.cashAfter.toStringAsFixed(2),
                    tx.dieselAfter.toStringAsFixed(2),
                  ]),
              // صف الإجمالي
              [
                '',
                '',
                'الإجمالي',
                totalCash.toStringAsFixed(2),
                totalDiesel.toStringAsFixed(2),
                '',
                '',
              ]
            ],
            cellAlignments: {
              0: pw.Alignment.center,
              1: pw.Alignment.center,
              2: pw.Alignment.centerRight,
              3: pw.Alignment.center,
              4: pw.Alignment.center,
              5: pw.Alignment.center,
              6: pw.Alignment.center,
            },
          ),
          pw.SizedBox(height: 16),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.end,
            children: [
              pw.Text('توقيع الإدارة: _______________',
                  style: pw.TextStyle(font: font, fontSize: 13)),
            ],
          ),
        ],
      ),
    );
    await Printing.layoutPdf(
      onLayout: (PdfPageFormat format) async => pdf.save(),
    );
  }
}

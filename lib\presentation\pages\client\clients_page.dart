import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:untitled/core/theme/app_theme.dart';
import 'package:untitled/data/models/client_model.dart';
import 'package:untitled/data/models/farm_model.dart';
import 'package:untitled/presentation/blocs/client/client_bloc.dart';
import 'package:untitled/presentation/blocs/client/client_event.dart';
import 'package:untitled/presentation/blocs/client/client_state.dart';
import 'package:untitled/presentation/blocs/farm/farm_bloc.dart';
import 'package:untitled/presentation/blocs/farm/farm_event.dart';
import 'package:untitled/presentation/blocs/farm/farm_state.dart';
import 'package:untitled/presentation/widgets/loading_indicator.dart';
import 'package:untitled/presentation/widgets/error_message.dart';
import 'package:untitled/presentation/widgets/empty_list_message.dart';
import 'package:untitled/presentation/widgets/prompt_helper.dart';
import 'package:untitled/presentation/widgets/help_overlay.dart';
import 'package:untitled/presentation/widgets/balance_display_widget.dart';

class ClientsPage extends StatefulWidget {
  const ClientsPage({super.key});

  @override
  State<ClientsPage> createState() => _ClientsPageState();
}

class _ClientsPageState extends State<ClientsPage> {
  final TextEditingController _searchController = TextEditingController();
  String _currentFilter = 'all'; // all, with_farms, without_farms

  @override
  void initState() {
    super.initState();
    // تحميل العملاء
    context.read<ClientBloc>().add(const LoadClients());
    // تحميل المزارع
    context.read<FarmBloc>().add(const LoadFarms());
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة العملاء والمزارع'),
        actions: [
          const PromptHelper(
            category: 'client_farm',
            promptKey: 'client_search',
            promptType: PromptType.dialog,
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            tooltip: 'فلترة العملاء',
            onPressed: () {
              _showFilterDialog(context);
            },
          ),
          IconButton(
            icon: const Icon(Icons.search),
            tooltip: 'البحث',
            onPressed: () {
              _showSearchDialog(context);
            },
          ),
        ],
      ),
      body: BlocConsumer<ClientBloc, ClientState>(
        listener: (context, state) {
          if (state is ClientOperationSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.green,
              ),
            );
          } else if (state is ClientError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        builder: (context, state) {
          if (state is ClientLoading) {
            return const LoadingIndicator();
          } else if (state is ClientsLoaded) {
            return _buildClientsList(state.clients);
          } else if (state is ClientError) {
            return ErrorMessage(message: state.message);
          } else {
            return const SizedBox.shrink();
          }
        },
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          Navigator.pushNamed(context, '/add-client');
        },
        icon: const Icon(Icons.person_add),
        label: const Text('إضافة عميل'),
        tooltip: 'إضافة عميل جديد مع مزارعه',
      ),
    );
  }

  Widget _buildClientsList(List<ClientModel> clients) {
    if (clients.isEmpty) {
      return EmptyListMessage(
        message: 'لا يوجد عملاء',
        icon: Icons.people,
        onAction: () {
          Navigator.pushNamed(context, '/add-client');
        },
        actionLabel: 'إضافة عميل جديد',
      );
    }

    return Column(
      children: [
        // شريط نصائح
        const QuickTipBar(
          category: 'client_farm',
          promptKey: 'farm_management',
          icon: Icons.lightbulb_outline,
        ),
        // قائمة العملاء
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: clients.length,
            itemBuilder: (context, index) {
              final client = clients[index];
              return _buildClientCard(client);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildClientCard(ClientModel client) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          // معلومات العميل الأساسية
          InkWell(
            onTap: () {
              Navigator.pushNamed(
                context,
                '/client-details',
                arguments: client.id,
              );
            },
            borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: AppTheme.primaryColor.withValues(alpha: 0.1),
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.person,
                          color: AppTheme.primaryColor,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              client.name,
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.black87,
                              ),
                            ),
                            if (client.phone != null)
                              Text(
                                'الهاتف: ${client.phone}',
                                style: const TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey,
                                ),
                              ),
                          ],
                        ),
                      ),
                      PopupMenuButton<String>(
                        onSelected: (value) {
                          if (value == 'edit') {
                            Navigator.pushNamed(
                              context,
                              '/edit-client',
                              arguments: client,
                            );
                          } else if (value == 'delete') {
                            _showDeleteConfirmationDialog(context, client);
                          } else if (value == 'add_farm') {
                            Navigator.pushNamed(
                              context,
                              '/add-farm',
                              arguments: client.id,
                            );
                          }
                        },
                        itemBuilder: (context) => [
                          const PopupMenuItem<String>(
                            value: 'add_farm',
                            child: Row(
                              children: [
                                Icon(Icons.add_business, color: Colors.green),
                                SizedBox(width: 8),
                                Text('إضافة مزرعة'),
                              ],
                            ),
                          ),
                          const PopupMenuItem<String>(
                            value: 'edit',
                            child: Row(
                              children: [
                                Icon(Icons.edit, color: Colors.blue),
                                SizedBox(width: 8),
                                Text('تعديل'),
                              ],
                            ),
                          ),
                          const PopupMenuItem<String>(
                            value: 'delete',
                            child: Row(
                              children: [
                                Icon(Icons.delete, color: Colors.red),
                                SizedBox(width: 8),
                                Text('حذف'),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  // عرض الأرصدة الفورية
                  BalanceDisplayWidget(
                    clientId: client.id.toString(),
                    showTitle: false,
                  ),
                  const SizedBox(height: 12),
                  // إحصائيات العميل
                  Row(
                    children: [
                      _buildInfoChip(
                        icon: Icons.landscape,
                        label:
                            '3 مزارع', // سيتم تحديثها لاحقاً بالبيانات الحقيقية
                        color: Colors.green,
                      ),
                      const SizedBox(width: 8),
                      _buildInfoChip(
                        icon: Icons.water_drop,
                        label: '12 تسقية',
                        color: Colors.blue,
                      ),
                      const SizedBox(width: 8),
                      _buildInfoChip(
                        icon: Icons.payment,
                        label: '5 دفعات',
                        color: Colors.purple,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          // قسم إدارة المزارع
          Container(
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius:
                  const BorderRadius.vertical(bottom: Radius.circular(12)),
            ),
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () {
                        _showFarmsBottomSheet(context, client);
                      },
                      icon: const Icon(Icons.landscape, size: 18),
                      label: const Text('إدارة المزارع'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                        elevation: 0,
                        padding: const EdgeInsets.symmetric(vertical: 8),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton.icon(
                    onPressed: () {
                      Navigator.pushNamed(
                        context,
                        '/add-farm',
                        arguments: client.id,
                      );
                    },
                    icon: const Icon(Icons.add, size: 18),
                    label: const Text('إضافة'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                      foregroundColor: Colors.white,
                      elevation: 0,
                      padding: const EdgeInsets.symmetric(
                          vertical: 8, horizontal: 16),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoChip({
    required IconData icon,
    required String label,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withAlpha((0.1 * 255).round()),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: color.withAlpha((0.3 * 255).round()),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 14,
            color: color,
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  void _showSearchDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('بحث عن عميل'),
          content: TextField(
            controller: _searchController,
            decoration: const InputDecoration(
              hintText: 'أدخل اسم أو رقم هاتف العميل',
              prefixIcon: Icon(Icons.search),
            ),
            onSubmitted: (value) {
              if (value.isNotEmpty) {
                context.read<ClientBloc>().add(SearchClients(value));
                Navigator.pop(context);
              }
            },
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                if (_searchController.text.isNotEmpty) {
                  context
                      .read<ClientBloc>()
                      .add(SearchClients(_searchController.text));
                  Navigator.pop(context);
                }
              },
              child: const Text('بحث'),
            ),
          ],
        );
      },
    );
  }

  void _showDeleteConfirmationDialog(BuildContext context, ClientModel client) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('تأكيد الحذف'),
          content: Text('هل أنت متأكد من حذف العميل ${client.name}؟'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                context.read<ClientBloc>().add(DeleteClient(client.id!));
                Navigator.pop(context);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
              ),
              child: const Text('حذف'),
            ),
          ],
        );
      },
    );
  }

  void _showFilterDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('فلترة العملاء'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('جميع العملاء'),
              value: 'all',
              groupValue: _currentFilter,
              onChanged: (value) {
                setState(() {
                  _currentFilter = value!;
                });
                Navigator.pop(context);
                context.read<ClientBloc>().add(const LoadClients());
              },
            ),
            RadioListTile<String>(
              title: const Text('العملاء الذين لديهم مزارع'),
              value: 'with_farms',
              groupValue: _currentFilter,
              onChanged: (value) {
                setState(() {
                  _currentFilter = value!;
                });
                Navigator.pop(context);
                // سيتم تنفيذ الفلترة لاحقاً
                context.read<ClientBloc>().add(const LoadClients());
              },
            ),
            RadioListTile<String>(
              title: const Text('العملاء بدون مزارع'),
              value: 'without_farms',
              groupValue: _currentFilter,
              onChanged: (value) {
                setState(() {
                  _currentFilter = value!;
                });
                Navigator.pop(context);
                // سيتم تنفيذ الفلترة لاحقاً
                context.read<ClientBloc>().add(const LoadClients());
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _showFarmsBottomSheet(BuildContext context, ClientModel client) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        expand: false,
        builder: (context, scrollController) {
          return Container(
            decoration: const BoxDecoration(
              borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
            ),
            child: Column(
              children: [
                // مقبض السحب
                Container(
                  margin: const EdgeInsets.only(top: 8),
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey.shade300,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                // العنوان
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      const Icon(
                        Icons.landscape,
                        color: AppTheme.primaryColor,
                        size: 24,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'مزارع ${client.name}',
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: AppTheme.primaryColor,
                          ),
                        ),
                      ),
                      IconButton(
                        onPressed: () {
                          Navigator.pushNamed(
                            context,
                            '/add-farm',
                            arguments: client.id,
                          );
                        },
                        icon: const Icon(Icons.add),
                        tooltip: 'إضافة مزرعة جديدة',
                      ),
                    ],
                  ),
                ),
                const Divider(),
                // قائمة المزارع
                Expanded(
                  child: BlocBuilder<FarmBloc, FarmState>(
                    builder: (context, state) {
                      if (state is FarmLoading) {
                        return const LoadingIndicator();
                      } else if (state is FarmsLoaded) {
                        // فلترة المزارع حسب العميل المحدد
                        final clientFarms = state.farms
                            .where((farm) => farm.clientId == client.id)
                            .toList();

                        if (clientFarms.isEmpty) {
                          return EmptyListMessage(
                            message: 'لا توجد مزارع لهذا العميل',
                            icon: Icons.landscape,
                            onAction: () {
                              Navigator.pushNamed(
                                context,
                                '/add-farm',
                                arguments: client.id,
                              );
                            },
                            actionLabel: 'إضافة مزرعة',
                          );
                        }
                        return ListView.builder(
                          controller: scrollController,
                          padding: const EdgeInsets.all(16),
                          itemCount: clientFarms.length,
                          itemBuilder: (context, index) {
                            final farm = clientFarms[index];
                            return _buildFarmCard(farm);
                          },
                        );
                      } else if (state is FarmError) {
                        return ErrorMessage(
                          message: state.message,
                          onRetry: () {
                            context
                                .read<FarmBloc>()
                                .add(const LoadFarms());
                          },
                        );
                      }
                      return const SizedBox.shrink();
                    },
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildFarmCard(FarmModel farm) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.green.withValues(alpha: 0.1),
            shape: BoxShape.circle,
          ),
          child: const Icon(
            Icons.landscape,
            color: Colors.green,
            size: 20,
          ),
        ),
        title: Text(
          farm.name,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: farm.location != null
            ? Text(
                farm.location!,
                style: const TextStyle(
                  color: Colors.grey,
                  fontSize: 12,
                ),
              )
            : null,
        trailing: PopupMenuButton<String>(
          onSelected: (value) {
            if (value == 'edit') {
              Navigator.pushNamed(
                context,
                '/edit-farm',
                arguments: farm,
              );
            } else if (value == 'delete') {
              _showDeleteFarmDialog(context, farm);
            } else if (value == 'details') {
              Navigator.pushNamed(
                context,
                '/farm-details',
                arguments: farm.id,
              );
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem<String>(
              value: 'details',
              child: Row(
                children: [
                  Icon(Icons.info, color: Colors.blue),
                  SizedBox(width: 8),
                  Text('التفاصيل'),
                ],
              ),
            ),
            const PopupMenuItem<String>(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit, color: Colors.orange),
                  SizedBox(width: 8),
                  Text('تعديل'),
                ],
              ),
            ),
            const PopupMenuItem<String>(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, color: Colors.red),
                  SizedBox(width: 8),
                  Text('حذف'),
                ],
              ),
            ),
          ],
        ),
        onTap: () {
          Navigator.pushNamed(
            context,
            '/farm-details',
            arguments: farm.id,
          );
        },
      ),
    );
  }

  void _showDeleteFarmDialog(BuildContext context, FarmModel farm) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف مزرعة "${farm.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              context.read<FarmBloc>().add(DeleteFarm(farm.id!));
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }
}

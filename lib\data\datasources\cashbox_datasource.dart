import 'package:flutter/foundation.dart';
import 'package:untitled/data/datasources/database_helper.dart';
import 'package:untitled/data/models/cashbox_model.dart';

class CashboxDataSource {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // إضافة صندوق جديد
  Future<int> addCashbox(CashboxModel cashbox) async {
    try {
      final db = await _databaseHelper.database;

      // إعداد البيانات للإدراج (بدون id للسماح بـ AUTOINCREMENT)
      final data = cashbox.toJson();
      if (cashbox.id == null) {
        data.remove('id'); // إزالة id للسماح بـ AUTOINCREMENT
      }

      final result = await db.insert('cashboxes', data);
      debugPrint('✅ تم إضافة صندوق جديد: ${cashbox.name} (ID: $result)');
      return result;
    } catch (e) {
      debugPrint('❌ خطأ في إضافة الصندوق ${cashbox.name}: $e');
      throw Exception('خطأ في إضافة الصندوق: $e');
    }
  }

  // الحصول على جميع الصناديق
  Future<List<CashboxModel>> getAllCashboxes() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query('cashboxes');
    return List.generate(maps.length, (i) {
      return CashboxModel.fromJson(maps[i]);
    });
  }

  // الحصول على صندوق بواسطة المعرف
  Future<CashboxModel?> getCashboxById(int id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'cashboxes',
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return CashboxModel.fromJson(maps.first);
    }
    return null;
  }

  // الحصول على الصناديق حسب النوع (نقدي أو ديزل)
  Future<List<CashboxModel>> getCashboxesByType(String type) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'cashboxes',
      where: 'type = ?',
      whereArgs: [type],
    );
    return List.generate(maps.length, (i) {
      return CashboxModel.fromJson(maps[i]);
    });
  }

  // تحديث بيانات صندوق
  Future<int> updateCashbox(CashboxModel cashbox) async {
    final db = await _databaseHelper.database;
    return await db.update(
      'cashboxes',
      cashbox.toJson(),
      where: 'id = ?',
      whereArgs: [cashbox.id],
    );
  }

  // تحديث رصيد صندوق
  Future<int> updateCashboxBalance(int id, double newBalance) async {
    final db = await _databaseHelper.database;
    return await db.update(
      'cashboxes',
      {'balance': newBalance, 'last_updated': DateTime.now().toIso8601String()},
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // إضافة مبلغ لرصيد الصندوق
  Future<bool> addBalance(int id, double amount) async {
    final cashbox = await getCashboxById(id);
    if (cashbox != null) {
      final newBalance = cashbox.balance + amount;
      final result = await updateCashboxBalance(id, newBalance);
      return result > 0;
    }
    return false;
  }

  // خصم مبلغ من رصيد الصندوق
  Future<bool> deductBalance(int id, double amount) async {
    final cashbox = await getCashboxById(id);
    if (cashbox != null && cashbox.balance >= amount) {
      final newBalance = cashbox.balance - amount;
      final result = await updateCashboxBalance(id, newBalance);
      return result > 0;
    }
    return false;
  }

  // حذف صندوق
  Future<int> deleteCashbox(int id) async {
    final db = await _databaseHelper.database;
    return await db.delete('cashboxes', where: 'id = ?', whereArgs: [id]);
  }

  // الحصول على عدد الصناديق
  Future<int> getCashboxesCount() async {
    final db = await _databaseHelper.database;
    final result = await db.rawQuery('SELECT COUNT(*) FROM cashboxes');
    return result.first.values.first as int? ?? 0;
  }

  // الحصول على إجمالي رصيد الصناديق النقدية
  Future<double> getTotalCashBalance() async {
    final db = await _databaseHelper.database;
    final result = await db.rawQuery(
      'SELECT SUM(balance) as total FROM cashboxes WHERE type = ?',
      ['cash'],
    );
    return result.first['total'] as double? ?? 0.0;
  }

  // الحصول على إجمالي رصيد صناديق الديزل
  Future<double> getTotalDieselBalance() async {
    final db = await _databaseHelper.database;
    final result = await db.rawQuery(
      'SELECT SUM(balance) as total FROM cashboxes WHERE type = ?',
      ['diesel'],
    );
    return result.first['total'] as double? ?? 0.0;
  }
}

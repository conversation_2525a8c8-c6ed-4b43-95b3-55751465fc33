import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:untitled/app.dart';
import 'package:untitled/core/performance_monitor.dart';
import 'package:untitled/data/datasources/client_datasource.dart';
import 'package:untitled/data/datasources/farm_datasource.dart';
import 'package:untitled/data/datasources/irrigation_datasource.dart';
import 'package:untitled/data/datasources/payment_datasource.dart';
import 'package:untitled/data/datasources/cashbox_datasource.dart';
import 'package:untitled/data/datasources/client_account_datasource.dart';
import 'package:untitled/presentation/blocs/client/client_bloc.dart';
import 'package:untitled/presentation/blocs/farm/farm_bloc.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_bloc.dart';
import 'package:untitled/presentation/blocs/payment/payment_bloc.dart';
import 'package:untitled/presentation/blocs/balance/balance_bloc.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_bloc.dart';
import 'package:untitled/presentation/blocs/client_account/client_account_bloc.dart';
import 'package:untitled/presentation/blocs/dashboard/dashboard_bloc.dart';
import 'package:untitled/presentation/blocs/contacts/contacts_bloc.dart';
import 'package:untitled/services/contacts_service.dart';
import 'package:untitled/core/data_initializer.dart';
import 'package:untitled/core/error_handler.dart';
import 'package:untitled/core/services/simple_notification_service.dart';
import 'package:untitled/core/services/client_notification_service.dart';

/// بدء مراقبة الإشعارات التلقائية
void _startAutomaticNotifications() {
  // تشغيل مراقبة الإشعارات كل 30 دقيقة
  Timer.periodic(const Duration(minutes: 30), (timer) async {
    try {
      await ClientNotificationService.sendAutomaticNotifications();
    } catch (e) {
      debugPrint('❌ خطأ في الإشعارات التلقائية: $e');
    }
  });

  // تشغيل مراقبة حالة العملاء كل ساعة
  Timer.periodic(const Duration(hours: 1), (timer) async {
    try {
      await ClientNotificationService.monitorClientStatus();
    } catch (e) {
      debugPrint('❌ خطأ في مراقبة حالة العملاء: $e');
    }
  });

  // تشغيل مراقبة استهلاك الديزل كل 6 ساعات
  Timer.periodic(const Duration(hours: 6), (timer) async {
    try {
      await ClientNotificationService.monitorDieselConsumption();
    } catch (e) {
      debugPrint('❌ خطأ في مراقبة استهلاك الديزل: $e');
    }
  });

  debugPrint('✅ تم بدء مراقبة الإشعارات التلقائية');
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    if (kDebugMode) {
      PerformanceMonitor().startMonitoring();
    }

    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);

    final clientDataSource = ClientDataSource();
    final farmDataSource = FarmDataSource();
    final irrigationDataSource = IrrigationDataSource();
    final paymentDataSource = PaymentDataSource();
    final cashboxDataSource = CashboxDataSource();

    await DataInitializer.initializeAppData().timeout(
      const Duration(seconds: 30),
      onTimeout: () => throw TimeoutException('انتهت مهلة تهيئة البيانات'),
    );

    // تهيئة خدمة الإشعارات المبسطة
    await SimpleNotificationService.initialize();

    // بدء مراقبة الإشعارات التلقائية
    _startAutomaticNotifications();

    if (kDebugMode) {
      final dataStatus = await DataInitializer.checkDataStatus();
      debugPrint('📊 حالة البيانات: $dataStatus');
    }

    if (kDebugMode) {
      FlutterError.onError = (FlutterErrorDetails details) {
        debugPrint('🚨 خطأ Flutter: ${details.exception}');
        FlutterError.presentError(details);
      };
    }

    runApp(
      MultiBlocProvider(
        providers: [
          BlocProvider<ClientBloc>(
            create: (context) => ClientBloc(clientDataSource),
          ),
          BlocProvider(
            create: (context) => BalanceBloc(),
          ),
          BlocProvider<FarmBloc>(
            create: (context) => FarmBloc(farmDataSource),
          ),
          BlocProvider<IrrigationBloc>(
            create: (context) => IrrigationBloc(irrigationDataSource),
          ),
          BlocProvider<PaymentBloc>(
            create: (context) => PaymentBloc(paymentDataSource),
          ),
          BlocProvider<CashboxBloc>(
            create: (context) => CashboxBloc(cashboxDataSource),
          ),
          BlocProvider<ClientAccountBloc>(
            create: (context) => ClientAccountBloc(ClientAccountDataSource()),
          ),
          BlocProvider<DashboardBloc>(
            create: (context) => DashboardBloc(
              clientDataSource: clientDataSource,
              farmDataSource: farmDataSource,
              irrigationDataSource: irrigationDataSource,
              paymentDataSource: paymentDataSource,
            ),
          ),
          BlocProvider<ContactsBloc>(
            create: (context) => ContactsBloc(ContactsService()),
          ),
        ],
        child: const WateringApp(),
      ),
    );
  } catch (e, stackTrace) {
    debugPrint('❌ خطأ في تهيئة التطبيق: $e');
    debugPrint('📍 Stack trace: $stackTrace');

    runApp(
      MaterialApp(
        title: 'خطأ في التطبيق',
        home: Scaffold(
          appBar: AppBar(title: const Text('خطأ في التطبيق')),
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error, size: 64, color: Colors.red),
                const SizedBox(height: 16),
                const Text('حدث خطأ في تهيئة التطبيق'),
                const SizedBox(height: 8),
                Text('التفاصيل: ${ErrorHandler.handleError(e)}'),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () {
                    // إعادة تشغيل التطبيق
                    main();
                  },
                  child: const Text('إعادة المحاولة'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

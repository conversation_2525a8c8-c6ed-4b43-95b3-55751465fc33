import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

// دوال لإدارة الصندوق
class CashboxOperations {
  // تحديث الرصيد
  static CashboxModel updateBalance(CashboxModel cashbox, double newBalance) {
    return cashbox.copyWith(
      balance: newBalance,
      updatedAt: DateTime.now(),
    );
  }

  // إضافة مبلغ إلى الرصيد
  static CashboxModel addToBalance(CashboxModel cashbox, double amount) {
    return cashbox.copyWith(
      balance: cashbox.balance + amount,
      updatedAt: DateTime.now(),
    );
  }

  // خصم مبلغ من الرصيد
  static CashboxModel subtractFromBalance(CashboxModel cashbox, double amount) {
    return cashbox.copyWith(
      balance: cashbox.balance - amount,
      updatedAt: DateTime.now(),
    );
  }
}

/// تصنيف استخدام الصناديق
enum CashboxUsageType {
  administrative, // مصاريف إدارية
  maintenance,    // صيانة
  fuel,          // وقود
  sales,         // مبيعات
  operations,    // عمليات
  emergency,     // طوارئ
  other,         // أخرى
}

/// امتداد لتصنيف الاستخدام
extension CashboxUsageTypeExtension on CashboxUsageType {
  String get displayName {
    switch (this) {
      case CashboxUsageType.administrative:
        return 'مصاريف إدارية';
      case CashboxUsageType.maintenance:
        return 'صيانة';
      case CashboxUsageType.fuel:
        return 'وقود';
      case CashboxUsageType.sales:
        return 'مبيعات';
      case CashboxUsageType.operations:
        return 'عمليات';
      case CashboxUsageType.emergency:
        return 'طوارئ';
      case CashboxUsageType.other:
        return 'أخرى';
    }
  }

  String get code {
    switch (this) {
      case CashboxUsageType.administrative:
        return 'administrative';
      case CashboxUsageType.maintenance:
        return 'maintenance';
      case CashboxUsageType.fuel:
        return 'fuel';
      case CashboxUsageType.sales:
        return 'sales';
      case CashboxUsageType.operations:
        return 'operations';
      case CashboxUsageType.emergency:
        return 'emergency';
      case CashboxUsageType.other:
        return 'other';
    }
  }

  Color get color {
    switch (this) {
      case CashboxUsageType.administrative:
        return Colors.blue;
      case CashboxUsageType.maintenance:
        return Colors.orange;
      case CashboxUsageType.fuel:
        return Colors.red;
      case CashboxUsageType.sales:
        return Colors.green;
      case CashboxUsageType.operations:
        return Colors.purple;
      case CashboxUsageType.emergency:
        return Colors.deepOrange;
      case CashboxUsageType.other:
        return Colors.grey;
    }
  }

  IconData get icon {
    switch (this) {
      case CashboxUsageType.administrative:
        return Icons.business;
      case CashboxUsageType.maintenance:
        return Icons.build;
      case CashboxUsageType.fuel:
        return Icons.local_gas_station;
      case CashboxUsageType.sales:
        return Icons.shopping_cart;
      case CashboxUsageType.operations:
        return Icons.settings;
      case CashboxUsageType.emergency:
        return Icons.warning;
      case CashboxUsageType.other:
        return Icons.category;
    }
  }

  static CashboxUsageType fromCode(String code) {
    switch (code) {
      case 'administrative':
        return CashboxUsageType.administrative;
      case 'maintenance':
        return CashboxUsageType.maintenance;
      case 'fuel':
        return CashboxUsageType.fuel;
      case 'sales':
        return CashboxUsageType.sales;
      case 'operations':
        return CashboxUsageType.operations;
      case 'emergency':
        return CashboxUsageType.emergency;
      case 'other':
      default:
        return CashboxUsageType.other;
    }
  }
}

class CashboxModel extends Equatable {
  final int? id;
  final String name;
  final String type; // cash أو diesel
  final double balance;
  final String? notes; // إضافة حقل الملاحظات
  final CashboxUsageType usageType; // تصنيف الاستخدام
  final String? purpose; // الغرض من الاستخدام
  final DateTime createdAt;
  final DateTime updatedAt;

  const CashboxModel({
    this.id,
    required this.name,
    required this.type,
    required this.balance,
    this.notes,
    this.usageType = CashboxUsageType.other,
    this.purpose,
    required this.createdAt,
    required this.updatedAt,
  });

  // تحويل من JSON
  factory CashboxModel.fromJson(Map<String, dynamic> json) {
    return CashboxModel(
      id: json['id'] is String ? int.tryParse(json['id']) : json['id'],
      name: json['name'],
      type: json['type'],
      balance: (json['balance'] as num).toDouble(),
      notes: json['notes'],
      usageType: json['usage_type'] != null 
          ? CashboxUsageTypeExtension.fromCode(json['usage_type'])
          : CashboxUsageType.other,
      purpose: json['purpose'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['last_updated'] ?? json['updated_at']),
    );
  }

  // تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type,
      'balance': balance,
      'notes': notes,
      'usage_type': usageType.code,
      'purpose': purpose,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'last_updated': updatedAt.toIso8601String(),
    };
  }

  // نسخة معدلة من الكائن
  CashboxModel copyWith({
    int? id,
    String? name,
    String? type,
    double? balance,
    String? notes,
    CashboxUsageType? usageType,
    String? purpose,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CashboxModel(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      balance: balance ?? this.balance,
      notes: notes ?? this.notes,
      usageType: usageType ?? this.usageType,
      purpose: purpose ?? this.purpose,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props =>
      [id, name, type, balance, notes, usageType, purpose, createdAt, updatedAt];
}

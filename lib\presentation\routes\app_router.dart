import 'package:flutter/material.dart';
import 'package:untitled/data/models/cashbox_model.dart';
import 'package:untitled/data/models/client_model.dart';
// تم إزالة imports غير مستخدمة مؤقتاً
import 'package:untitled/presentation/pages/admin/admin_management_page.dart';
import 'package:untitled/presentation/pages/auth/login_page.dart';
import 'package:untitled/presentation/pages/cashbox/add_cashbox_page.dart';
import 'package:untitled/presentation/pages/cashbox/cashbox_details_page.dart';
import 'package:untitled/presentation/pages/cashbox/cashbox_management_page.dart';
import 'package:untitled/presentation/pages/client/add_client_page_new.dart';
import 'package:untitled/presentation/pages/client/client_details_page.dart';
import 'package:untitled/presentation/pages/client/clients_page.dart';
import 'package:untitled/presentation/pages/client/clients_list_page.dart';
// import 'package:untitled/presentation/pages/disabled_pages.dart'; // لم تعد مطلوبة
// import 'package:untitled/presentation/pages/farm/farms_list_page.dart'; // معطل مؤقتاً
// import 'package:untitled/presentation/pages/irrigation/irrigations_list_page.dart'; // معطل مؤقتاً
// import 'package:untitled/presentation/pages/payment/payments_list_page.dart'; // معطل مؤقتاً
import 'package:untitled/presentation/pages/dashboard_page.dart';
// import 'package:untitled/presentation/pages/farm/add_farm_page.dart'; // معطل مؤقتاً
// import 'package:untitled/presentation/pages/farm/farm_details_page.dart'; // معطل مؤقتاً
import 'package:untitled/presentation/pages/help/help_center_page.dart';
import 'package:untitled/presentation/pages/help/quick_start_page.dart';
import 'package:untitled/presentation/pages/help/faq_page.dart';
import 'package:untitled/presentation/pages/help/app_tour_page.dart';
import 'package:untitled/presentation/pages/help/user_guide_page.dart';
import 'package:untitled/presentation/pages/help/interactive_user_guide_page.dart';
import 'package:untitled/presentation/pages/settings/modern_settings_page.dart';
import 'package:untitled/presentation/pages/reports/reports_main_page.dart';
import 'package:untitled/presentation/pages/reports/client_statements_page.dart';
import 'package:untitled/presentation/pages/reports/cashbox_statements_page.dart';
import 'package:untitled/presentation/pages/reports/irrigation_reports_page.dart';
import 'package:untitled/presentation/pages/reports/payment_reports_page.dart';
import 'package:untitled/presentation/pages/reports/financial_reports_page.dart';
import 'package:untitled/presentation/pages/reports/simple_reports_page.dart';
import 'package:untitled/presentation/pages/reports/custom_reports_page.dart';
import 'package:untitled/presentation/pages/irrigation/add_irrigation_page.dart';
import 'package:untitled/presentation/pages/test/balance_test_page.dart';
import 'package:untitled/presentation/pages/test/notification_test_page.dart';
import 'package:untitled/presentation/pages/balance/balance_management_page.dart';
import 'package:untitled/presentation/pages/irrigation/irrigation_details_page.dart';
import 'package:untitled/presentation/pages/irrigation/irrigations_list_page.dart';
import 'package:untitled/presentation/pages/payment/payments_list_page.dart';
import 'package:untitled/presentation/pages/client/client_account_details_page.dart';
import 'package:untitled/presentation/pages/payment/unified_payment_page.dart';
import 'package:untitled/presentation/pages/payment/payment_details_page.dart';
import 'package:untitled/presentation/pages/farm/farms_page.dart';
import 'package:untitled/presentation/pages/farm/add_farm_page.dart';
import 'package:untitled/presentation/pages/farm/farm_details_page.dart';
import 'package:untitled/presentation/pages/transfer/transfers_page.dart';
import 'package:untitled/presentation/pages/reports/comprehensive_reports_page.dart';
import 'package:untitled/data/models/irrigation_model.dart';
import 'package:untitled/data/models/farm_model.dart';
// import 'package:untitled/presentation/pages/payment/add_payment_page.dart'; // معطل مؤقتاً
// import 'package:untitled/presentation/pages/report/reports_page.dart'; // معطل مؤقتاً
import 'package:untitled/presentation/pages/settings/settings_page.dart';
import 'package:untitled/presentation/pages/developer/developer_settings_page.dart';

class AppRouter {
  // ثوابت المسارات
  static const String dashboard = '/';
  static const String clients = '/clients';
  static const String clientsList = '/clients-list';
  static const String clientDetails = '/client-details';
  static const String clientAccountDetails = '/client-account-details';
  static const String addClient = '/add-client';
  static const String editClient = '/edit-client';
  static const String farms = '/farms';
  static const String farmsList = '/farms-list';
  static const String farmDetails = '/farm-details';
  static const String addFarm = '/add-farm';
  static const String editFarm = '/edit-farm';
  static const String irrigations = '/irrigations';
  static const String irrigationsList = '/irrigations-list';
  static const String addIrrigation = '/add-irrigation';
  static const String irrigationDetails = '/irrigation-details';
  static const String editIrrigation = '/edit-irrigation';
  static const String payments = '/payments';
  static const String paymentsList = '/payments-list';
  static const String addPayment = '/add-payment';
  static const String editPayment = '/edit-payment';
  static const String paymentDetails = '/payment-details';
  static const String cashboxes = '/cashboxes';
  static const String cashboxDetails = '/cashbox-details';
  static const String addCashbox = '/add-cashbox';
  static const String editCashbox = '/edit-cashbox';
  static const String transfers = '/transfers';
  static const String reports = '/reports';
  static const String settings = '/settings';
  static const String login = '/login';
  static const String adminManagement = '/admin-management';
  static const String helpCenter = '/help-center';
  static const String quickStart = '/quick-start';
  static const String faq = '/faq';
  static const String appTour = '/app-tour';
  static const String userGuide = '/user-guide';
  static const String interactiveGuide = '/interactive-guide';
  static const String modernSettings = '/modern-settings';
  static const String helpContent = '/help-content';

  // مسارات التقارير الجديدة
  static const String reportsMain = '/reports-main';
  static const String clientStatements = '/client-statements';
  static const String cashboxStatements = '/cashbox-statements';
  static const String irrigationReports = '/irrigation-reports';
  static const String paymentReports = '/payment-reports';
  static const String financialReports = '/financial-reports';
  static const String customReports = '/custom-reports';
  static const String balanceTest = '/balance-test';
  static const String notificationTest = '/notification-test';
  static const String balanceManagement = '/balance-management';
  static const String developerSettings = '/developer-settings';

  // توجيه المسارات
  static Route<dynamic> onGenerateRoute(RouteSettings settings) {
    switch (settings.name) {
      case dashboard:
        return MaterialPageRoute(builder: (_) => const DashboardPage());
      case clients:
        return MaterialPageRoute(builder: (_) => const ClientsPage());
      case clientsList:
        return MaterialPageRoute(builder: (_) => const ClientsListPage());
      case clientDetails:
        final clientId = settings.arguments as int;
        return MaterialPageRoute(
          builder: (_) => ClientDetailsPage(clientId: clientId),
        );
      case addClient:
        return MaterialPageRoute(builder: (_) => const AddClientPageNew());
      case editClient:
        final client = settings.arguments as ClientModel;
        return MaterialPageRoute(
            builder: (_) => AddClientPageNew(client: client));
      case farms:
      case farmsList:
        return MaterialPageRoute(builder: (_) => const FarmsPage());
      case addFarm:
        final clientId = settings.arguments as int?;
        return MaterialPageRoute(
            builder: (_) => AddFarmPage(clientId: clientId));
      case farmDetails:
        final farmId = settings.arguments as int;
        return MaterialPageRoute(
            builder: (_) => FarmDetailsPage(farmId: farmId));
      case editFarm:
        final farm = settings.arguments as FarmModel;
        return MaterialPageRoute(builder: (_) => AddFarmPage(farm: farm));
      case irrigationsList:
        return MaterialPageRoute(builder: (_) => const IrrigationsListPage());
      case addIrrigation:
        return MaterialPageRoute(builder: (_) => const AddIrrigationPage());
      case irrigationDetails:
        final irrigationId = settings.arguments as int;
        return MaterialPageRoute(
            builder: (_) => IrrigationDetailsPage(irrigationId: irrigationId));
      case clientAccountDetails:
        final clientId = settings.arguments as int;
        return MaterialPageRoute(
            builder: (_) => ClientAccountDetailsPage(clientId: clientId));
      case editIrrigation:
        final irrigation = settings.arguments as IrrigationModel?;
        return MaterialPageRoute(
          builder: (_) => AddIrrigationPage(irrigation: irrigation),
        );
      case paymentsList:
        return MaterialPageRoute(builder: (_) => const PaymentsListPage());
      case addPayment:
        return MaterialPageRoute(builder: (_) => const UnifiedPaymentPage());
      case paymentDetails:
        final paymentId = settings.arguments as int;
        return MaterialPageRoute(
            builder: (_) => PaymentDetailsPage(paymentId: paymentId));
      case editPayment:
        return MaterialPageRoute(builder: (_) => const UnifiedPaymentPage());
      case cashboxes:
        return MaterialPageRoute(builder: (_) => const CashboxManagementPage());
      case addCashbox:
        final type = settings.arguments as String;
        return MaterialPageRoute(builder: (_) => AddCashboxPage(type: type));
      case editCashbox:
        final cashbox = settings.arguments as CashboxModel;
        return MaterialPageRoute(
          builder: (_) => AddCashboxPage(type: cashbox.type, cashbox: cashbox),
        );
      case transfers:
        return MaterialPageRoute(builder: (_) => const TransfersPage());
      case AppRouter.reports:
        return MaterialPageRoute(
            builder: (_) => const ComprehensiveReportsPage());
      case AppRouter.settings:
        return MaterialPageRoute(builder: (_) => const SettingsPage());
      case AppRouter.login:
        return MaterialPageRoute(builder: (_) => const LoginPage());

      case AppRouter.cashboxDetails:
        final cashboxId = settings.arguments as int;
        return MaterialPageRoute(
          builder: (_) => CashboxDetailsPage(cashboxId: cashboxId),
        );
      case AppRouter.adminManagement:
        return MaterialPageRoute(builder: (_) => const AdminManagementPage());
      case AppRouter.helpCenter:
        return MaterialPageRoute(builder: (_) => const HelpCenterPage());
      case AppRouter.quickStart:
        return MaterialPageRoute(builder: (_) => const QuickStartPage());
      case AppRouter.faq:
        return MaterialPageRoute(builder: (_) => const FAQPage());
      case AppRouter.appTour:
        return MaterialPageRoute(builder: (_) => const AppTourPage());
      case AppRouter.userGuide:
        return MaterialPageRoute(builder: (_) => const UserGuidePage());
      case AppRouter.interactiveGuide:
        return MaterialPageRoute(
            builder: (_) => const InteractiveUserGuidePage());
      case AppRouter.modernSettings:
        return MaterialPageRoute(builder: (_) => const ModernSettingsPage());

      // مسارات التقارير
      case AppRouter.reportsMain:
        return MaterialPageRoute(builder: (_) => const ReportsMainPage());
      case AppRouter.clientStatements:
        return MaterialPageRoute(builder: (_) => const ClientStatementsPage());
      case AppRouter.cashboxStatements:
        return MaterialPageRoute(builder: (_) => const CashboxStatementsPage());
      case AppRouter.irrigationReports:
        return MaterialPageRoute(builder: (_) => const IrrigationReportsPage());
      case AppRouter.paymentReports:
        return MaterialPageRoute(builder: (_) => const PaymentReportsPage());
      case AppRouter.financialReports:
        return MaterialPageRoute(builder: (_) => const FinancialReportsPage());
      case AppRouter.customReports:
        return MaterialPageRoute(builder: (_) => const CustomReportsPage());
      case AppRouter.balanceTest:
        return MaterialPageRoute(builder: (_) => const BalanceTestPage());
      case AppRouter.notificationTest:
        return MaterialPageRoute(builder: (_) => const NotificationTestPage());
      case AppRouter.balanceManagement:
        return MaterialPageRoute(builder: (_) => const BalanceManagementPage());
      case AppRouter.developerSettings:
        return MaterialPageRoute(builder: (_) => const DeveloperSettingsPage());
      default:
        return MaterialPageRoute(builder: (_) => const DashboardPage());
    }
  }
}

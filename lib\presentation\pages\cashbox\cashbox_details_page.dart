import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:untitled/data/models/cashbox_model.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_bloc.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_event.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_state.dart';

class CashboxDetailsPage extends StatefulWidget {
  final int cashboxId;

  const CashboxDetailsPage({super.key, required this.cashboxId});

  @override
  State<CashboxDetailsPage> createState() => _CashboxDetailsPageState();
}

class _CashboxDetailsPageState extends State<CashboxDetailsPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  CashboxModel? _cashbox;
  final DateFormat _dateFormat = DateFormat('yyyy/MM/dd - HH:mm');

  // تخزين مرجع للـ bloc
  late final CashboxBloc _cashboxBloc;

  // تاريخ الفلتر
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    // تخزين مرجع للـ bloc
    _cashboxBloc = context.read<CashboxBloc>();

    // تحميل بيانات الصندوق
    _cashboxBloc.add(GetCashboxById(widget.cashboxId));

    // تحميل العمليات المرتبطة بالصندوق
    _loadTransactions();
  }

  void _loadTransactions() {
    // سيتم تنفيذ هذا لاحقًا عندما نضيف العلاقات بين الجداول
    // حاليًا سنستخدم بيانات وهمية للعرض
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_cashbox?.name ?? 'تفاصيل الصندوق'),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () {
              if (_cashbox != null) {
                Navigator.pushNamed(
                  context,
                  '/edit-cashbox',
                  arguments: _cashbox,
                ).then((_) {
                  // إعادة تحميل بيانات الصندوق بعد التعديل
                  if (!mounted) return;

                  // استخدام مرجع الـ bloc المخزن مسبقًا
                  _cashboxBloc.add(GetCashboxById(widget.cashboxId));
                });
              }
            },
          ),
          IconButton(
            icon: const Icon(Icons.picture_as_pdf),
            onPressed: () {
              _exportCashboxReport();
            },
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          indicatorWeight: 3,
          tabs: [
            Tab(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: Colors.green.withValues(alpha: 0.3),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.arrow_downward,
                      color: Colors.white,
                      size: 16,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(_cashbox?.type == 'cash' ? 'الوارد' : 'الديزل الوارد'),
                ],
              ),
            ),
            Tab(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: Colors.red.withValues(alpha: 0.3),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.arrow_upward,
                      color: Colors.white,
                      size: 16,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(_cashbox?.type == 'cash' ? 'الصادر' : 'الديزل الصادر'),
                ],
              ),
            ),
          ],
        ),
      ),
      body: BlocListener<CashboxBloc, CashboxState>(
        listener: (context, state) {
          if (state is CashboxLoaded) {
            setState(() {
              _cashbox = state.cashbox;
            });
          } else if (state is CashboxError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        child: Column(
          children: [
            _buildCashboxHeader(),
            _buildDateFilter(),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildIncomingTab(),
                  _buildOutgoingTab(),
                ],
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _showUpdateBalanceDialog();
        },
        child: const Icon(Icons.edit),
      ),
    );
  }

  Widget _buildCashboxHeader() {
    if (_cashbox == null) {
      return const SizedBox(
        height: 120,
        child: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    final bool isCash = _cashbox!.type == 'cash';
    final Color primaryColor = isCash ? Colors.green : Colors.red;
    final String balanceUnit = isCash ? 'ريال' : 'لتر';
    final IconData typeIcon =
        isCash ? Icons.account_balance_wallet : Icons.local_gas_station;

    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: Column(
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 30,
                backgroundColor: primaryColor,
                child: Icon(
                  typeIcon,
                  size: 30,
                  color: Colors.white,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _cashbox!.name,
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      'النوع: ${isCash ? 'صندوق نقدي' : 'صندوق ديزل'}',
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                      ),
                    ),
                    Text(
                      'تاريخ الإنشاء: ${_dateFormat.format(_cashbox!.createdAt)}',
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: primaryColor.withAlpha((0.1 * 255).round()),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: primaryColor.withAlpha((0.3 * 255).round()),
                width: 1,
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'الرصيد الحالي:',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '${_cashbox!.balance.toStringAsFixed(2)} $balanceUnit',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: primaryColor,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStatItem(
                icon: Icons.arrow_downward,
                value: '0.00',
                label: 'وارد',
                color: Colors.green,
                unit: balanceUnit,
              ),
              _buildStatItem(
                icon: Icons.arrow_upward,
                value: '0.00',
                label: 'صادر',
                color: Colors.red,
                unit: balanceUnit,
              ),
              _buildStatItem(
                icon: Icons.swap_horiz,
                value: '0',
                label: 'عمليات',
                color: Colors.blue,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDateFilter() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: Row(
        children: [
          Expanded(
            child: _buildDatePicker(
              label: 'من',
              value: _startDate,
              onChanged: (date) {
                setState(() {
                  _startDate = date;
                });
                _loadTransactions();
              },
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildDatePicker(
              label: 'إلى',
              value: _endDate,
              onChanged: (date) {
                setState(() {
                  _endDate = date;
                });
                _loadTransactions();
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDatePicker({
    required String label,
    required DateTime value,
    required Function(DateTime) onChanged,
  }) {
    final dateFormat = DateFormat('yyyy/MM/dd');

    return InkWell(
      onTap: () async {
        final date = await showDatePicker(
          context: context,
          initialDate: value,
          firstDate: DateTime(2020),
          lastDate: DateTime(2030),
        );

        if (date != null) {
          onChanged(date);
        }
      },
      child: InputDecorator(
        decoration: InputDecoration(
          labelText: label,
          prefixIcon: const Icon(Icons.calendar_today),
          border: const OutlineInputBorder(),
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        ),
        child: Text(
          dateFormat.format(value),
          style: const TextStyle(fontSize: 14),
        ),
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String value,
    required String label,
    required Color color,
    String? unit,
  }) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withAlpha((0.1 * 255).round()),
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            color: color,
            size: 24,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          unit != null ? '$value $unit' : value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: color,
          ),
          textAlign: TextAlign.center,
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: Colors.grey,
          ),
        ),
      ],
    );
  }

  Widget _buildIncomingTab() {
    final isCash = _cashbox?.type == 'cash';
    const color = Colors.green;
    final icon = isCash ? Icons.attach_money : Icons.local_gas_station;
    final unit = isCash ? 'ريال' : 'لتر';

    return Column(
      children: [
        // بطاقة إحصائيات الوارد
        Container(
          margin: const EdgeInsets.all(16),
          child: Card(
            elevation: 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: color.withValues(alpha: 0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      icon,
                      color: color,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          isCash ? 'المدفوعات الواردة' : 'الديزل الوارد',
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: color,
                          ),
                        ),
                        Text(
                          'إجمالي الوارد: 0.00 $unit',
                          style: const TextStyle(
                            fontSize: 14,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        // قائمة المعاملات الواردة
        Expanded(
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  icon,
                  size: 64,
                  color: Colors.grey.shade300,
                ),
                const SizedBox(height: 16),
                Text(
                  isCash ? 'لا توجد مدفوعات واردة' : 'لا توجد كميات ديزل واردة',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildOutgoingTab() {
    final isCash = _cashbox?.type == 'cash';
    const color = Colors.red;
    final icon = isCash ? Icons.attach_money : Icons.local_gas_station;
    final unit = isCash ? 'ريال' : 'لتر';

    return Column(
      children: [
        // بطاقة إحصائيات الصادر
        Container(
          margin: const EdgeInsets.all(16),
          child: Card(
            elevation: 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: color.withValues(alpha: 0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      icon,
                      color: color,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          isCash ? 'المدفوعات الصادرة' : 'الديزل الصادر',
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: color,
                          ),
                        ),
                        Text(
                          'إجمالي الصادر: 0.00 $unit',
                          style: const TextStyle(
                            fontSize: 14,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        // قائمة المعاملات الصادرة
        Expanded(
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  icon,
                  size: 64,
                  color: Colors.grey.shade300,
                ),
                const SizedBox(height: 16),
                Text(
                  isCash ? 'لا توجد مدفوعات صادرة' : 'لا توجد كميات ديزل صادرة',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  void _showUpdateBalanceDialog() {
    if (_cashbox == null) return;

    final TextEditingController balanceController = TextEditingController(
      text: _cashbox!.balance.toString(),
    );

    final bool isCash = _cashbox!.type == 'cash';
    final String balanceUnit = isCash ? 'ريال' : 'لتر';

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('تعديل الرصيد'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('الصندوق: ${_cashbox!.name}'),
              const SizedBox(height: 16),
              TextFormField(
                controller: balanceController,
                decoration: InputDecoration(
                  labelText: 'الرصيد ($balanceUnit)',
                  border: const OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 16),
              TextFormField(
                decoration: const InputDecoration(
                  labelText: 'سبب التعديل',
                  border: OutlineInputBorder(),
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                final newBalance = double.tryParse(balanceController.text);
                if (newBalance != null) {
                  // استخدام مرجع الـ bloc المخزن مسبقًا
                  _cashboxBloc
                      .add(UpdateCashboxBalance(_cashbox!.id!, newBalance));
                  Navigator.pop(context);
                }
              },
              child: const Text('حفظ'),
            ),
          ],
        );
      },
    );
  }

  void _exportCashboxReport() {
    // سيتم تنفيذ هذا لاحقًا
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاري تصدير تقرير الصندوق...'),
        backgroundColor: Colors.green,
      ),
    );
  }
}

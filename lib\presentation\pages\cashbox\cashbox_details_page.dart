import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:untitled/data/models/cashbox_model.dart';
import 'package:untitled/data/models/payment_model.dart';
import 'package:untitled/data/models/irrigation_model.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_bloc.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_event.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_state.dart';
import 'package:untitled/presentation/blocs/payment/payment_bloc.dart';
import 'package:untitled/presentation/blocs/payment/payment_event.dart';
import 'package:untitled/presentation/blocs/payment/payment_state.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_bloc.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_event.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_state.dart';
import 'package:untitled/services/cashbox_statement_service.dart';

class CashboxDetailsPage extends StatefulWidget {
  final int cashboxId;

  const CashboxDetailsPage({super.key, required this.cashboxId});

  @override
  State<CashboxDetailsPage> createState() => _CashboxDetailsPageState();
}

class _CashboxDetailsPageState extends State<CashboxDetailsPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  CashboxModel? _cashbox;
  final DateFormat _dateFormat = DateFormat('yyyy/MM/dd - HH:mm');

  // تخزين مرجع للـ bloc
  late final CashboxBloc _cashboxBloc;

  // تاريخ الفلتر
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();

  // بيانات العمليات
  List<PaymentModel> _payments = [];
  List<IrrigationModel> _irrigations = [];
  List<CashboxTransactionModel> _transactions = [];
  bool _isLoadingTransactions = false;

  // خدمة معاملات الصندوق
  final CashboxStatementService _cashboxService = CashboxStatementService();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);

    // تخزين مرجع للـ bloc
    _cashboxBloc = context.read<CashboxBloc>();

    // تحميل بيانات الصندوق
    _cashboxBloc.add(GetCashboxById(widget.cashboxId));

    // تحميل العمليات المرتبطة بالصندوق
    _loadTransactions();
  }

  void _loadTransactions() {
    setState(() {
      _isLoadingTransactions = true;
    });

    // تحميل المدفوعات
    context.read<PaymentBloc>().add(const LoadPayments());

    // تحميل التسقيات
    context.read<IrrigationBloc>().add(const LoadIrrigations());

    // تحميل معاملات الصندوق
    _loadCashboxTransactions();
  }

  Future<void> _loadCashboxTransactions() async {
    try {
      final transactions = await _cashboxService.getCashboxTransactions(
        cashboxId: widget.cashboxId,
        fromDate: _startDate,
        toDate: _endDate,
      );

      setState(() {
        _transactions = transactions;
        _isLoadingTransactions = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingTransactions = false;
      });
      debugPrint('خطأ في تحميل معاملات الصندوق: $e');
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  // حساب الوارد للصندوق
  double _calculateIncoming() {
    if (_cashbox == null) return 0.0;

    double total = 0.0;

    if (_cashbox!.type == 'cash') {
      // للصناديق النقدية: المدفوعات النقدية الواردة
      total = _payments
          .where((p) => p.type == 'cash' && p.amount > 0)
          .fold(0.0, (sum, p) => sum + p.amount);
    } else {
      // لصناديق الديزل: المدفوعات الديزل الواردة
      total = _payments
          .where((p) => p.type == 'diesel' && p.amount > 0)
          .fold(0.0, (sum, p) => sum + p.amount);
    }

    // إضافة عمليات الإيداع
    total += _transactions
        .where((t) => t.type == CashboxTransactionType.deposit)
        .fold(0.0, (sum, t) => sum + t.amount);

    return total;
  }

  // حساب الصادر للصندوق
  double _calculateOutgoing() {
    if (_cashbox == null) return 0.0;

    double total = 0.0;

    if (_cashbox!.type == 'cash') {
      // للصناديق النقدية: تكلفة التسقيات
      total = _irrigations.fold(0.0, (sum, i) => sum + i.cost);
    } else {
      // لصناديق الديزل: استهلاك الديزل في التسقيات
      total = _irrigations.fold(0.0, (sum, i) => sum + i.dieselConsumption);
    }

    // إضافة عمليات السحب
    total += _transactions
        .where((t) => t.type == CashboxTransactionType.withdraw)
        .fold(0.0, (sum, t) => sum + t.amount.abs());

    return total;
  }

  // حساب عدد العمليات
  int _calculateTransactionsCount() {
    if (_cashbox == null) return 0;

    int count = _transactions.length;

    if (_cashbox!.type == 'cash') {
      count += _payments.where((p) => p.type == 'cash').length;
      count += _irrigations.length;
    } else {
      count += _payments.where((p) => p.type == 'diesel').length;
      count += _irrigations.length;
    }

    return count;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_cashbox?.name ?? 'تفاصيل الصندوق'),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () {
              if (_cashbox != null) {
                Navigator.pushNamed(
                  context,
                  '/edit-cashbox',
                  arguments: _cashbox,
                ).then((_) {
                  // إعادة تحميل بيانات الصندوق بعد التعديل
                  if (!mounted) return;

                  // استخدام مرجع الـ bloc المخزن مسبقًا
                  _cashboxBloc.add(GetCashboxById(widget.cashboxId));
                });
              }
            },
          ),
          IconButton(
            icon: const Icon(Icons.picture_as_pdf),
            onPressed: () {
              _exportCashboxReport();
            },
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          indicatorWeight: 3,
          tabs: [
            Tab(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: Colors.green.withValues(alpha: 0.3),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.arrow_downward,
                      color: Colors.white,
                      size: 16,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(_cashbox?.type == 'cash' ? 'الوارد' : 'الديزل الوارد'),
                ],
              ),
            ),
            Tab(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: Colors.red.withValues(alpha: 0.3),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.arrow_upward,
                      color: Colors.white,
                      size: 16,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(_cashbox?.type == 'cash' ? 'الصادر' : 'الديزل الصادر'),
                ],
              ),
            ),
            Tab(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: Colors.blue.withValues(alpha: 0.3),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.history,
                      color: Colors.white,
                      size: 16,
                    ),
                  ),
                  const SizedBox(width: 8),
                  const Text('جميع العمليات'),
                ],
              ),
            ),
          ],
        ),
      ),
      body: MultiBlocListener(
        listeners: [
          BlocListener<CashboxBloc, CashboxState>(
            listener: (context, state) {
              if (state is CashboxLoaded) {
                setState(() {
                  _cashbox = state.cashbox;
                });
              } else if (state is CashboxError) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(state.message),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
          ),
          BlocListener<PaymentBloc, PaymentState>(
            listener: (context, state) {
              if (state is PaymentsLoaded) {
                setState(() {
                  _payments = state.payments;
                });
              }
            },
          ),
          BlocListener<IrrigationBloc, IrrigationState>(
            listener: (context, state) {
              if (state is IrrigationsLoaded) {
                setState(() {
                  _irrigations = state.irrigations;
                });
              }
            },
          ),
        ],
        child: Column(
          children: [
            _buildCashboxHeader(),
            _buildDateFilter(),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildIncomingTab(),
                  _buildOutgoingTab(),
                  _buildAllTransactionsTab(),
                ],
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _showTransactionDialog();
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildCashboxHeader() {
    if (_cashbox == null) {
      return const SizedBox(
        height: 120,
        child: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    final bool isCash = _cashbox!.type == 'cash';
    final Color primaryColor = isCash ? Colors.green : Colors.red;
    final String balanceUnit = isCash ? 'ريال' : 'لتر';
    final IconData typeIcon =
        isCash ? Icons.account_balance_wallet : Icons.local_gas_station;

    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: Column(
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 30,
                backgroundColor: primaryColor,
                child: Icon(
                  typeIcon,
                  size: 30,
                  color: Colors.white,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _cashbox!.name,
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      'النوع: ${isCash ? 'صندوق نقدي' : 'صندوق ديزل'}',
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                      ),
                    ),
                    Text(
                      'تاريخ الإنشاء: ${_dateFormat.format(_cashbox!.createdAt)}',
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: primaryColor.withAlpha((0.1 * 255).round()),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: primaryColor.withAlpha((0.3 * 255).round()),
                width: 1,
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'الرصيد الحالي:',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '${_cashbox!.balance.toStringAsFixed(2)} $balanceUnit',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: primaryColor,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStatItem(
                icon: Icons.arrow_downward,
                value: _calculateIncoming().toStringAsFixed(2),
                label: 'وارد',
                color: Colors.green,
                unit: balanceUnit,
              ),
              _buildStatItem(
                icon: Icons.arrow_upward,
                value: _calculateOutgoing().toStringAsFixed(2),
                label: 'صادر',
                color: Colors.red,
                unit: balanceUnit,
              ),
              _buildStatItem(
                icon: Icons.swap_horiz,
                value: _calculateTransactionsCount().toString(),
                label: 'عمليات',
                color: Colors.blue,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDateFilter() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: Row(
        children: [
          Expanded(
            child: _buildDatePicker(
              label: 'من',
              value: _startDate,
              onChanged: (date) {
                setState(() {
                  _startDate = date;
                });
                _loadTransactions();
              },
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildDatePicker(
              label: 'إلى',
              value: _endDate,
              onChanged: (date) {
                setState(() {
                  _endDate = date;
                });
                _loadTransactions();
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDatePicker({
    required String label,
    required DateTime value,
    required Function(DateTime) onChanged,
  }) {
    final dateFormat = DateFormat('yyyy/MM/dd');

    return InkWell(
      onTap: () async {
        final date = await showDatePicker(
          context: context,
          initialDate: value,
          firstDate: DateTime(2020),
          lastDate: DateTime(2030),
        );

        if (date != null) {
          onChanged(date);
        }
      },
      child: InputDecorator(
        decoration: InputDecoration(
          labelText: label,
          prefixIcon: const Icon(Icons.calendar_today),
          border: const OutlineInputBorder(),
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        ),
        child: Text(
          dateFormat.format(value),
          style: const TextStyle(fontSize: 14),
        ),
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String value,
    required String label,
    required Color color,
    String? unit,
  }) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withAlpha((0.1 * 255).round()),
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            color: color,
            size: 24,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          unit != null ? '$value $unit' : value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: color,
          ),
          textAlign: TextAlign.center,
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: Colors.grey,
          ),
        ),
      ],
    );
  }

  Widget _buildIncomingTab() {
    final isCash = _cashbox?.type == 'cash';
    const color = Colors.green;
    final icon = isCash ? Icons.attach_money : Icons.local_gas_station;
    final unit = isCash ? 'ريال' : 'لتر';

    // جمع العمليات الواردة
    final incomingTransactions = <Map<String, dynamic>>[];

    // إضافة معاملات الإيداع
    for (final transaction in _transactions) {
      if (transaction.type == CashboxTransactionType.deposit ||
          transaction.type == CashboxTransactionType.transfer_in ||
          transaction.type == CashboxTransactionType.payment_in) {
        incomingTransactions.add({
          'type': 'cashbox_transaction',
          'data': transaction,
          'date': transaction.date,
        });
      }
    }

    // إضافة المدفوعات الواردة
    for (final payment in _payments) {
      if ((_cashbox?.type == 'cash' && payment.type == 'cash') ||
          (_cashbox?.type == 'diesel' && payment.type == 'diesel')) {
        if (payment.amount > 0) {
          incomingTransactions.add({
            'type': 'payment',
            'data': payment,
            'date': payment.createdAt,
          });
        }
      }
    }

    // ترتيب العمليات الواردة حسب التاريخ (الأحدث أولاً)
    incomingTransactions.sort((a, b) => b['date'].compareTo(a['date']));

    return Column(
      children: [
        // بطاقة إحصائيات الوارد
        Container(
          margin: const EdgeInsets.all(16),
          child: Card(
            elevation: 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: color.withValues(alpha: 0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      icon,
                      color: color,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          isCash ? 'العمليات الواردة' : 'الديزل الوارد',
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: color,
                          ),
                        ),
                        Text(
                          'إجمالي الوارد: ${_calculateIncoming().toStringAsFixed(2)} $unit',
                          style: const TextStyle(
                            fontSize: 14,
                            color: Colors.grey,
                          ),
                        ),
                        Text(
                          'عدد العمليات: ${incomingTransactions.length}',
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        // قائمة العمليات الواردة
        Expanded(
          child: incomingTransactions.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        icon,
                        size: 64,
                        color: Colors.grey.shade300,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'لا توجد عمليات واردة',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: incomingTransactions.length,
                  itemBuilder: (context, index) {
                    final transaction = incomingTransactions[index];
                    final type = transaction['type'] as String;
                    final data = transaction['data'];

                    switch (type) {
                      case 'cashbox_transaction':
                        return _buildCashboxTransactionItem(
                            data as CashboxTransactionModel);
                      case 'payment':
                        return _buildPaymentTransactionItem(
                            data as PaymentModel);
                      default:
                        return const SizedBox.shrink();
                    }
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildOutgoingTab() {
    final isCash = _cashbox?.type == 'cash';
    const color = Colors.red;
    final icon = isCash ? Icons.attach_money : Icons.local_gas_station;
    final unit = isCash ? 'ريال' : 'لتر';

    // جمع العمليات الصادرة
    final outgoingTransactions = <Map<String, dynamic>>[];

    // إضافة معاملات السحب
    for (final transaction in _transactions) {
      if (transaction.type == CashboxTransactionType.withdraw ||
          transaction.type == CashboxTransactionType.transfer_out ||
          transaction.type == CashboxTransactionType.payment_out ||
          transaction.type == CashboxTransactionType.irrigation_cost) {
        outgoingTransactions.add({
          'type': 'cashbox_transaction',
          'data': transaction,
          'date': transaction.date,
        });
      }
    }

    // إضافة المدفوعات الصادرة
    for (final payment in _payments) {
      if ((_cashbox?.type == 'cash' && payment.type == 'cash') ||
          (_cashbox?.type == 'diesel' && payment.type == 'diesel')) {
        if (payment.amount < 0) {
          outgoingTransactions.add({
            'type': 'payment',
            'data': payment,
            'date': payment.createdAt,
          });
        }
      }
    }

    // إضافة تكاليف التسقيات
    for (final irrigation in _irrigations) {
      outgoingTransactions.add({
        'type': 'irrigation',
        'data': irrigation,
        'date': irrigation.createdAt,
      });
    }

    // ترتيب العمليات الصادرة حسب التاريخ (الأحدث أولاً)
    outgoingTransactions.sort((a, b) => b['date'].compareTo(a['date']));

    return Column(
      children: [
        // بطاقة إحصائيات الصادر
        Container(
          margin: const EdgeInsets.all(16),
          child: Card(
            elevation: 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: color.withValues(alpha: 0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      icon,
                      color: color,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          isCash ? 'العمليات الصادرة' : 'الديزل الصادر',
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: color,
                          ),
                        ),
                        Text(
                          'إجمالي الصادر: ${_calculateOutgoing().toStringAsFixed(2)} $unit',
                          style: const TextStyle(
                            fontSize: 14,
                            color: Colors.grey,
                          ),
                        ),
                        Text(
                          'عدد العمليات: ${outgoingTransactions.length}',
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        // قائمة العمليات الصادرة
        Expanded(
          child: outgoingTransactions.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        icon,
                        size: 64,
                        color: Colors.grey.shade300,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'لا توجد عمليات صادرة',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: outgoingTransactions.length,
                  itemBuilder: (context, index) {
                    final transaction = outgoingTransactions[index];
                    final type = transaction['type'] as String;
                    final data = transaction['data'];

                    switch (type) {
                      case 'cashbox_transaction':
                        return _buildCashboxTransactionItem(
                            data as CashboxTransactionModel);
                      case 'payment':
                        return _buildPaymentTransactionItem(
                            data as PaymentModel);
                      case 'irrigation':
                        return _buildIrrigationTransactionItem(
                            data as IrrigationModel);
                      default:
                        return const SizedBox.shrink();
                    }
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildAllTransactionsTab() {
    if (_isLoadingTransactions) {
      return const Center(child: CircularProgressIndicator());
    }

    // دمج جميع المعاملات وترتيبها حسب التاريخ
    final allTransactions = <Map<String, dynamic>>[];

    // إضافة معاملات الصندوق المخزنة
    for (final transaction in _transactions) {
      allTransactions.add({
        'type': 'cashbox_transaction',
        'data': transaction,
        'date': transaction.date,
      });
    }

    // إضافة المدفوعات
    for (final payment in _payments) {
      if (_cashbox?.type == 'cash' && payment.type == 'cash' ||
          _cashbox?.type == 'diesel' && payment.type == 'diesel') {
        allTransactions.add({
          'type': 'payment',
          'data': payment,
          'date': payment.createdAt,
        });
      }
    }

    // إضافة التسقيات
    for (final irrigation in _irrigations) {
      allTransactions.add({
        'type': 'irrigation',
        'data': irrigation,
        'date': irrigation.createdAt,
      });
    }

    // ترتيب المعاملات حسب التاريخ (الأحدث أولاً)
    allTransactions.sort((a, b) => b['date'].compareTo(a['date']));

    return Column(
      children: [
        // بطاقة إحصائيات شاملة
        Container(
          margin: const EdgeInsets.all(16),
          child: Card(
            elevation: 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.blue.withValues(alpha: 0.1),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.list_alt,
                      color: Colors.blue,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'جميع العمليات',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.blue,
                          ),
                        ),
                        Text(
                          'إجمالي العمليات: ${allTransactions.length}',
                          style: const TextStyle(
                            fontSize: 14,
                            color: Colors.grey,
                          ),
                        ),
                        Text(
                          'الرصيد الحالي: ${_cashbox?.balance.toStringAsFixed(2)} ${_cashbox?.type == 'cash' ? 'ريال' : 'لتر'}',
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        // قائمة جميع العمليات
        Expanded(
          child: allTransactions.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.history,
                        size: 64,
                        color: Colors.grey.shade300,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'لا توجد عمليات في الفترة المحددة',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: allTransactions.length,
                  itemBuilder: (context, index) {
                    final transaction = allTransactions[index];
                    final type = transaction['type'] as String;
                    final data = transaction['data'];
                    final date = transaction['date'] as DateTime;

                    switch (type) {
                      case 'cashbox_transaction':
                        return _buildCashboxTransactionItem(
                            data as CashboxTransactionModel);
                      case 'payment':
                        return _buildPaymentTransactionItem(
                            data as PaymentModel);
                      case 'irrigation':
                        return _buildIrrigationTransactionItem(
                            data as IrrigationModel);
                      default:
                        return const SizedBox.shrink();
                    }
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildCashboxTransactionItem(CashboxTransactionModel transaction) {
    final isCash = _cashbox?.type == 'cash';
    final unit = isCash ? 'ريال' : 'لتر';
    final isIncoming = transaction.type == CashboxTransactionType.deposit ||
        transaction.type == CashboxTransactionType.transfer_in ||
        transaction.type == CashboxTransactionType.payment_in;

    Color color;
    IconData icon;
    String title;
    String subtitle;

    switch (transaction.type) {
      case CashboxTransactionType.initial_balance:
        color = Colors.blue;
        icon = Icons.account_balance;
        title = 'رصيد ابتدائي';
        subtitle = 'الرصيد الابتدائي للصندوق';
        break;
      case CashboxTransactionType.deposit:
        color = Colors.green;
        icon = Icons.add_circle;
        title = 'إيداع';
        subtitle = 'إيداع مباشر في الصندوق';
        break;
      case CashboxTransactionType.withdraw:
        color = Colors.red;
        icon = Icons.remove_circle;
        title = 'سحب';
        subtitle = 'سحب مباشر من الصندوق';
        break;
      case CashboxTransactionType.transfer_in:
        color = Colors.blue;
        icon = Icons.swap_horiz;
        title = 'تحويل وارد';
        subtitle = 'تحويل من صندوق آخر';
        break;
      case CashboxTransactionType.transfer_out:
        color = Colors.orange;
        icon = Icons.swap_horiz;
        title = 'تحويل صادر';
        subtitle = 'تحويل إلى صندوق آخر';
        break;
      case CashboxTransactionType.payment_in:
        color = Colors.green;
        icon = Icons.payment;
        title = 'دفعة واردة';
        subtitle = 'دفعة من عميل';
        break;
      case CashboxTransactionType.payment_out:
        color = Colors.red;
        icon = Icons.payment;
        title = 'دفعة صادرة';
        subtitle = 'دفعة إلى عميل';
        break;
      case CashboxTransactionType.irrigation_cost:
        color = Colors.red;
        icon = Icons.water_drop;
        title = 'تكلفة تسقية';
        subtitle = 'تكلفة عملية تسقية';
        break;
      case CashboxTransactionType.income:
        color = Colors.green;
        icon = Icons.trending_up;
        title = 'دخل';
        subtitle = 'دخل إضافي';
        break;
      case CashboxTransactionType.expense:
        color = Colors.red;
        icon = Icons.trending_down;
        title = 'مصروف';
        subtitle = 'مصروف إضافي';
        break;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: color.withValues(alpha: 0.3)),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '${transaction.amount.toStringAsFixed(2)} $unit',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: color,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(Icons.access_time,
                          size: 16, color: Colors.grey.shade600),
                      const SizedBox(width: 4),
                      Text(
                        _formatDate(transaction.date),
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                  if (transaction.description.isNotEmpty) ...[
                    const SizedBox(height: 8),
                    Text(
                      'ملاحظات: ${transaction.description}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentTransactionItem(PaymentModel payment) {
    final isCash = _cashbox?.type == 'cash';
    final unit = isCash ? 'ريال' : 'لتر';
    final isIncoming = payment.amount > 0;
    final color = isIncoming ? Colors.green : Colors.red;
    final icon = isIncoming ? Icons.payment : Icons.payment;
    final title = isIncoming ? 'دفعة واردة' : 'دفعة صادرة';
    final subtitle = isIncoming ? 'دفعة من عميل' : 'دفعة إلى عميل';

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: color.withValues(alpha: 0.3)),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '${payment.amount.abs().toStringAsFixed(2)} $unit',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: color,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(Icons.person, size: 16, color: Colors.grey.shade600),
                      const SizedBox(width: 4),
                      Text(
                        'العميل: ${payment.clientId}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Icon(Icons.access_time,
                          size: 16, color: Colors.grey.shade600),
                      const SizedBox(width: 4),
                      Text(
                        _formatDate(payment.createdAt),
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                  if (payment.notes?.isNotEmpty == true) ...[
                    const SizedBox(height: 8),
                    Text(
                      'ملاحظات: ${payment.notes}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIrrigationTransactionItem(IrrigationModel irrigation) {
    final isCash = _cashbox?.type == 'cash';
    final unit = isCash ? 'ريال' : 'لتر';
    final color = Colors.red;
    final icon = Icons.water_drop;
    final title = 'تكلفة تسقية';
    final subtitle = 'تكلفة عملية تسقية';

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: color.withValues(alpha: 0.3)),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '${irrigation.cost.toStringAsFixed(2)} $unit',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: color,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(Icons.person, size: 16, color: Colors.grey.shade600),
                      const SizedBox(width: 4),
                      Text(
                        'العميل: ${irrigation.clientId}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Icon(Icons.agriculture,
                          size: 16, color: Colors.grey.shade600),
                      const SizedBox(width: 4),
                      Text(
                        'المزرعة: ${irrigation.farmName}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Icon(Icons.access_time,
                          size: 16, color: Colors.grey.shade600),
                      const SizedBox(width: 4),
                      Text(
                        _formatDate(irrigation.createdAt),
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                  if (irrigation.notes?.isNotEmpty == true) ...[
                    const SizedBox(height: 8),
                    Text(
                      'ملاحظات: ${irrigation.notes}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }

  void _showTransactionDialog() {
    if (_cashbox == null) return;

    showDialog(
      context: context,
      builder: (context) => _TransactionDialog(
        cashbox: _cashbox!,
        onTransactionAdded: () {
          Navigator.pop(context);
          _loadTransactions();
        },
      ),
    );
  }

  void _exportCashboxReport() {
    // سيتم تنفيذ هذا لاحقًا
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاري تصدير تقرير الصندوق...'),
        backgroundColor: Colors.green,
      ),
    );
  }
}

class _TransactionDialog extends StatefulWidget {
  final CashboxModel cashbox;
  final VoidCallback onTransactionAdded;

  const _TransactionDialog({
    required this.cashbox,
    required this.onTransactionAdded,
  });

  @override
  State<_TransactionDialog> createState() => _TransactionDialogState();
}

class _TransactionDialogState extends State<_TransactionDialog> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _notesController = TextEditingController();
  String _transactionType = 'deposit';
  bool _isLoading = false;

  @override
  void dispose() {
    _amountController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _performTransaction() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final amount = double.parse(_amountController.text);
      final notes = _notesController.text.trim();

      final cashboxService = CashboxStatementService();

      switch (_transactionType) {
        case 'deposit':
          await cashboxService.addDepositTransaction(
            cashboxId: widget.cashbox.id!,
            amount: amount,
            notes: notes,
          );
          break;
        case 'withdraw':
          await cashboxService.addWithdrawTransaction(
            cashboxId: widget.cashbox.id!,
            amount: amount,
            notes: notes,
          );
          break;
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                'تم ${_transactionType == 'deposit' ? 'الإيداع' : 'السحب'} بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
        widget.onTransactionAdded();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final unit = widget.cashbox.type == 'cash' ? 'ريال' : 'لتر';

    return AlertDialog(
      title: Text('إضافة عملية لصندوق ${widget.cashbox.name}'),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            DropdownButtonFormField<String>(
              decoration: const InputDecoration(
                labelText: 'نوع العملية',
                border: OutlineInputBorder(),
              ),
              value: _transactionType,
              items: [
                DropdownMenuItem(
                  value: 'deposit',
                  child: Row(
                    children: [
                      Icon(Icons.add_circle, color: Colors.green),
                      const SizedBox(width: 8),
                      const Text('إيداع'),
                    ],
                  ),
                ),
                DropdownMenuItem(
                  value: 'withdraw',
                  child: Row(
                    children: [
                      Icon(Icons.remove_circle, color: Colors.red),
                      const SizedBox(width: 8),
                      const Text('سحب'),
                    ],
                  ),
                ),
              ],
              onChanged: (value) {
                setState(() {
                  _transactionType = value!;
                });
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _amountController,
              decoration: InputDecoration(
                labelText: 'المبلغ ($unit)',
                border: const OutlineInputBorder(),
                prefixIcon: const Icon(Icons.attach_money),
              ),
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى إدخال المبلغ';
                }
                if (double.tryParse(value) == null) {
                  return 'يرجى إدخال رقم صحيح';
                }
                if (double.parse(value) <= 0) {
                  return 'يجب أن يكون المبلغ أكبر من صفر';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _notesController,
              decoration: const InputDecoration(
                labelText: 'ملاحظات (اختياري)',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.note),
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.pop(context),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _performTransaction,
          child: _isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : Text(_transactionType == 'deposit' ? 'إيداع' : 'سحب'),
        ),
      ],
    );
  }
}

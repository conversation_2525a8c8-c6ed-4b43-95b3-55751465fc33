import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:untitled/data/models/payment_model.dart';
import 'package:untitled/data/models/client_model.dart';
import 'package:untitled/data/models/cashbox_model.dart';
import 'package:untitled/presentation/blocs/payment/payment_bloc.dart';
import 'package:untitled/presentation/blocs/payment/payment_event.dart';
import 'package:untitled/presentation/blocs/payment/payment_state.dart';
import 'package:untitled/presentation/blocs/client/client_bloc.dart';
import 'package:untitled/presentation/blocs/client/client_event.dart';
import 'package:untitled/presentation/blocs/client/client_state.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_bloc.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_event.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_state.dart';
import 'package:untitled/core/theme/app_theme.dart';
import 'package:untitled/services/pdf_service.dart';
import 'package:open_file/open_file.dart';

/// صفحة تقرير المدفوعات المتقدمة
class PaymentReportsPage extends StatefulWidget {
  const PaymentReportsPage({super.key});

  @override
  State<PaymentReportsPage> createState() => _PaymentReportsPageState();
}

class _PaymentReportsPageState extends State<PaymentReportsPage> {
  List<PaymentModel> _payments = [];
  List<ClientModel> _clients = [];
  List<CashboxModel> _cashboxes = [];

  bool _isLoading = false;
  int _loadedCount = 0;
  final int _totalDataSources = 3;

  // فلاتر
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();
  String _selectedClient = 'all';
  String _selectedCashbox = 'all';
  String _selectedType = 'all'; // all, income, expense
  String _sortBy = 'date'; // date, amount, client
  bool _sortAscending = false;

  // بحث
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  bool _isExporting = false;

  @override
  void initState() {
    super.initState();
    _loadAllData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _loadAllData() {
    setState(() {
      _isLoading = true;
      _loadedCount = 0;
    });

    context.read<PaymentBloc>().add(const LoadPayments());
    context.read<ClientBloc>().add(const LoadClients());
    context.read<CashboxBloc>().add(const LoadCashboxes());
  }

  void _checkDataLoaded() {
    _loadedCount++;
    if (_loadedCount >= _totalDataSources) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  List<PaymentModel> get _filteredPayments {
    var filtered = _payments.where((payment) {
      // فلتر التاريخ
      final startOfDay =
          DateTime(_startDate.year, _startDate.month, _startDate.day);
      final endOfDay =
          DateTime(_endDate.year, _endDate.month, _endDate.day, 23, 59, 59);

      if (payment.paymentDate.isBefore(startOfDay) ||
          payment.paymentDate.isAfter(endOfDay)) {
        return false;
      }

      // فلتر البحث
      if (_searchQuery.isNotEmpty) {
        final client = _getClientById(payment.clientId);
        final cashbox = _getCashboxById(payment.cashboxId);
        if (!(client?.name
                    .toLowerCase()
                    .contains(_searchQuery.toLowerCase()) ??
                false) &&
            !(cashbox?.name
                    .toLowerCase()
                    .contains(_searchQuery.toLowerCase()) ??
                false) &&
            !(payment.notes
                    ?.toLowerCase()
                    .contains(_searchQuery.toLowerCase()) ??
                false)) {
          return false;
        }
      }

      // فلتر العميل
      if (_selectedClient != 'all' &&
          payment.clientId.toString() != _selectedClient) {
        return false;
      }

      // فلتر الصندوق
      if (_selectedCashbox != 'all' &&
          payment.cashboxId.toString() != _selectedCashbox) {
        return false;
      }

      // فلتر النوع
      if (_selectedType != 'all') {
        if (_selectedType == 'income' && payment.amount <= 0) {
          return false;
        }
        if (_selectedType == 'expense' && payment.amount >= 0) {
          return false;
        }
      }

      return true;
    }).toList();

    // ترتيب
    filtered.sort((a, b) {
      int comparison = 0;
      switch (_sortBy) {
        case 'date':
          comparison = a.createdAt.compareTo(b.createdAt);
          break;
        case 'amount':
          comparison = a.amount.abs().compareTo(b.amount.abs());
          break;
        case 'client':
          final clientA = _getClientById(a.clientId);
          final clientB = _getClientById(b.clientId);
          comparison = (clientA?.name ?? '').compareTo(clientB?.name ?? '');
          break;
      }
      return _sortAscending ? comparison : -comparison;
    });

    return filtered;
  }

  ClientModel? _getClientById(int clientId) {
    try {
      return _clients.firstWhere((client) => client.id == clientId);
    } catch (e) {
      return null;
    }
  }

  CashboxModel? _getCashboxById(int cashboxId) {
    try {
      return _cashboxes.firstWhere((cashbox) => cashbox.id == cashboxId);
    } catch (e) {
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildAppBar(),
      body: _isLoading
          ? _buildLoadingWidget()
          : SingleChildScrollView(
              child: Column(
                children: [
                  _buildFiltersSection(),
                  _buildPaymentSummarySection(),
                  _buildPaymentTable(),
                  const SizedBox(height: 80),
                ],
              ),
            ),
      floatingActionButton: _buildFloatingActionButtons(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text(
        'تقرير المدفوعات',
        style: TextStyle(fontWeight: FontWeight.bold, color: Colors.white),
      ),
      backgroundColor: AppTheme.primaryColor,
      iconTheme: const IconThemeData(color: Colors.white),
      actions: [
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: _loadAllData,
          tooltip: 'تحديث البيانات',
        ),
        IconButton(
          icon: const Icon(Icons.picture_as_pdf),
          onPressed: _isExporting ? null : _exportToPdf,
          tooltip: 'تصدير PDF',
        ),
        IconButton(
          icon: const Icon(Icons.file_download),
          onPressed: _exportToExcel,
          tooltip: 'تصدير إلى Excel',
        ),
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert),
          onSelected: (value) {
            switch (value) {
              case 'print':
                _printReport();
                break;
              case 'share':
                _shareReport();
                break;
              case 'analytics':
                _showAnalytics();
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'print',
              child: Row(
                children: [
                  Icon(Icons.print),
                  SizedBox(width: 8),
                  Text('طباعة التقرير'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'share',
              child: Row(
                children: [
                  Icon(Icons.share),
                  SizedBox(width: 8),
                  Text('مشاركة التقرير'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'analytics',
              child: Row(
                children: [
                  Icon(Icons.analytics),
                  SizedBox(width: 8),
                  Text('تحليلات متقدمة'),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  List<BlocListener> _buildBlocListeners() {
    return [
      BlocListener<PaymentBloc, PaymentState>(
        listener: (context, state) {
          if (state is PaymentsLoaded) {
            setState(() => _payments = state.payments);
            _checkDataLoaded();
          }
        },
      ),
      BlocListener<ClientBloc, ClientState>(
        listener: (context, state) {
          if (state is ClientsLoaded) {
            setState(() => _clients = state.clients);
            _checkDataLoaded();
          }
        },
      ),
      BlocListener<CashboxBloc, CashboxState>(
        listener: (context, state) {
          if (state is CashboxesLoaded) {
            setState(() => _cashboxes = state.cashboxes);
            _checkDataLoaded();
          }
        },
      ),
    ];
  }

  Widget _buildLoadingWidget() {
    return Container(
      color: Colors.white,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
          ),
          const SizedBox(height: 16),
          Text(
            'جاري تحميل بيانات المدفوعات...',
            style: TextStyle(fontSize: 16, color: Colors.grey.shade600),
          ),
          const SizedBox(height: 8),
          Text(
            'تم تحميل $_loadedCount من $_totalDataSources',
            style: TextStyle(fontSize: 14, color: Colors.grey[500]),
          ),
        ],
      ),
    );
  }

  Widget _buildFiltersSection() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.filter_list, color: AppTheme.primaryColor),
              const SizedBox(width: 8),
              const Text(
                'فلاتر التقرير',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              TextButton(
                onPressed: _resetFilters,
                child: const Text('إعادة تعيين'),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // شريط البحث
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'البحث بالعميل أو الصندوق أو الوصف...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        setState(() => _searchQuery = '');
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: AppTheme.primaryColor),
              ),
            ),
            onChanged: (value) {
              setState(() => _searchQuery = value);
            },
          ),

          const SizedBox(height: 16),

          // فلاتر الفترة الزمنية
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('من تاريخ:',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 4),
                    InkWell(
                      onTap: () => _selectDate(context, true),
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.calendar_today, size: 16),
                            const SizedBox(width: 8),
                            Text(DateFormat('yyyy-MM-dd').format(_startDate)),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('إلى تاريخ:',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 4),
                    InkWell(
                      onTap: () => _selectDate(context, false),
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.calendar_today, size: 16),
                            const SizedBox(width: 8),
                            Text(DateFormat('yyyy-MM-dd').format(_endDate)),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // فلاتر العميل والصندوق
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('العميل:',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 4),
                    DropdownButtonFormField<String>(
                      value: _selectedClient,
                      isExpanded: true,
                      decoration: InputDecoration(
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 8),
                      ),
                      items: [
                        const DropdownMenuItem(
                            value: 'all',
                            child: Text('جميع العملاء',
                                overflow: TextOverflow.ellipsis)),
                        ..._clients.map((client) => DropdownMenuItem(
                              value: client.id.toString(),
                              child: Text(client.name,
                                  overflow: TextOverflow.ellipsis),
                            )),
                      ],
                      onChanged: (value) {
                        setState(() => _selectedClient = value!);
                      },
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('الصندوق:',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 4),
                    DropdownButtonFormField<String>(
                      value: _selectedCashbox,
                      isExpanded: true,
                      decoration: InputDecoration(
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 8),
                      ),
                      items: [
                        const DropdownMenuItem(
                            value: 'all',
                            child: Text('جميع الصناديق',
                                overflow: TextOverflow.ellipsis)),
                        ..._cashboxes.map((cashbox) => DropdownMenuItem(
                              value: cashbox.id.toString(),
                              child: Text(cashbox.name,
                                  overflow: TextOverflow.ellipsis),
                            )),
                      ],
                      onChanged: (value) {
                        setState(() => _selectedCashbox = value!);
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // فلاتر النوع والترتيب
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('نوع المعاملة:',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 4),
                    DropdownButtonFormField<String>(
                      value: _selectedType,
                      isExpanded: true,
                      decoration: InputDecoration(
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 8),
                      ),
                      items: const [
                        DropdownMenuItem(
                            value: 'all',
                            child: Text('جميع المعاملات',
                                overflow: TextOverflow.ellipsis)),
                        DropdownMenuItem(
                            value: 'income',
                            child: Text('واردات',
                                overflow: TextOverflow.ellipsis)),
                        DropdownMenuItem(
                            value: 'expense',
                            child: Text('صادرات',
                                overflow: TextOverflow.ellipsis)),
                      ],
                      onChanged: (value) {
                        setState(() => _selectedType = value!);
                      },
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('ترتيب حسب:',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Expanded(
                          child: DropdownButtonFormField<String>(
                            value: _sortBy,
                            isExpanded: true,
                            decoration: InputDecoration(
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 8),
                            ),
                            items: const [
                              DropdownMenuItem(
                                  value: 'date',
                                  child: Text('التاريخ',
                                      overflow: TextOverflow.ellipsis)),
                              DropdownMenuItem(
                                  value: 'amount',
                                  child: Text('المبلغ',
                                      overflow: TextOverflow.ellipsis)),
                              DropdownMenuItem(
                                  value: 'client',
                                  child: Text('العميل',
                                      overflow: TextOverflow.ellipsis)),
                            ],
                            onChanged: (value) {
                              setState(() => _sortBy = value!);
                            },
                          ),
                        ),
                        const SizedBox(width: 8),
                        IconButton(
                          icon: Icon(
                            _sortAscending
                                ? Icons.arrow_upward
                                : Icons.arrow_downward,
                            color: AppTheme.primaryColor,
                          ),
                          onPressed: () {
                            setState(() => _sortAscending = !_sortAscending);
                          },
                          tooltip: _sortAscending ? 'تصاعدي' : 'تنازلي',
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentSummarySection() {
    final payments = _filteredPayments;
    final totalCount = payments.length;
    final totalCash = payments.fold<double>(
        0, (sum, p) => sum + (p.type == 'cash' ? p.amount : 0));
    final totalDiesel = payments.fold<double>(
        0, (sum, p) => sum + (p.type == 'diesel' ? p.amount : 0));
    final avgAmount = totalCount > 0
        ? payments.fold<double>(0, (sum, p) => sum + p.amount) / totalCount
        : 0;
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildSummaryItem('عدد المدفوعات', totalCount.toString()),
            _buildSummaryItem('إجمالي النقد', totalCash.toStringAsFixed(2)),
            _buildSummaryItem('إجمالي الديزل', totalDiesel.toStringAsFixed(2)),
            _buildSummaryItem('متوسط الدفعة', avgAmount.toStringAsFixed(2)),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(String label, String value) {
    return Column(
      children: [
        Text(label, style: const TextStyle(fontSize: 12, color: Colors.indigo)),
        const SizedBox(height: 2),
        Text(value,
            style: const TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.bold,
                color: Colors.indigo)),
      ],
    );
  }

  Widget _buildPaymentTable() {
    final payments = _filteredPayments;
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: DataTable(
          columns: const [
            DataColumn(label: Text('اسم العميل')),
            DataColumn(label: Text('نوع الدفعة')),
            DataColumn(label: Text('المبلغ/الكمية')),
            DataColumn(label: Text('التاريخ')),
            DataColumn(label: Text('طريقة الدفع')),
            DataColumn(label: Text('ملاحظات')),
          ],
          rows: payments.map((p) {
            final client = _clients.firstWhere((c) => c.id == p.clientId,
                orElse: () => ClientModel(
                    id: p.clientId,
                    name: 'غير محدد',
                    phone: '',
                    address: '',
                    createdAt: DateTime.now(),
                    updatedAt: DateTime.now()));
            return DataRow(cells: [
              DataCell(Text(client.name)),
              DataCell(Text(p.type == 'cash' ? 'نقدية' : 'ديزل')),
              DataCell(Text(p.amount.toStringAsFixed(2))),
              DataCell(Text(DateFormat('dd-MM-yyyy').format(p.paymentDate))),
              // TODO: دعم طريقة الدفع إذا أضيفت مستقبلاً في PaymentModel
              DataCell(Text('-')),
              DataCell(Text(p.notes ?? '-')),
            ]);
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildFloatingActionButtons() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        FloatingActionButton(
          heroTag: "reconcile",
          onPressed: _reconcilePayments,
          backgroundColor: Colors.orange,
          tooltip: 'تسوية المدفوعات',
          child: const Icon(Icons.balance, color: Colors.white),
        ),
        const SizedBox(height: 8),
        FloatingActionButton(
          heroTag: "export",
          onPressed: _exportToExcel,
          backgroundColor: Colors.green,
          tooltip: 'تصدير إلى Excel',
          child: const Icon(Icons.file_download, color: Colors.white),
        ),
        const SizedBox(height: 8),
        FloatingActionButton(
          heroTag: "refresh",
          onPressed: _loadAllData,
          backgroundColor: AppTheme.primaryColor,
          tooltip: 'تحديث البيانات',
          child: const Icon(Icons.refresh, color: Colors.white),
        ),
      ],
    );
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isStartDate ? _startDate : _endDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
          // التأكد من أن تاريخ البداية قبل تاريخ النهاية
          if (_endDate.isBefore(_startDate)) {
            _endDate = _startDate.add(const Duration(days: 1));
          }
        } else {
          // التأكد من أن تاريخ النهاية بعد تاريخ البداية
          if (picked.isBefore(_startDate)) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تاريخ النهاية يجب أن يكون بعد تاريخ البداية'),
                backgroundColor: Colors.orange,
              ),
            );
            return;
          }
          _endDate = picked;
        }
      });
    }
  }

  void _resetFilters() {
    setState(() {
      _startDate = DateTime.now().subtract(const Duration(days: 30));
      _endDate = DateTime.now();
      _selectedClient = 'all';
      _selectedCashbox = 'all';
      _selectedType = 'all';
      _sortBy = 'date';
      _sortAscending = false;
      _searchController.clear();
      _searchQuery = '';
    });
  }

  void _showPaymentDetails(PaymentModel payment) {
    final client = _getClientById(payment.clientId);
    final cashbox = _getCashboxById(payment.cashboxId);
    final isIncome = payment.amount > 0;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تفاصيل ${isIncome ? 'الوارد' : 'الصادر'}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('العميل:', client?.name ?? 'غير محدد'),
            _buildDetailRow('الصندوق:', cashbox?.name ?? 'غير محدد'),
            _buildDetailRow(
                'المبلغ:', '${payment.amount.toStringAsFixed(2)} ريال'),
            _buildDetailRow('التاريخ:',
                DateFormat('yyyy-MM-dd HH:mm').format(payment.createdAt)),
            if (payment.notes != null && payment.notes!.isNotEmpty)
              _buildDetailRow('الوصف:', payment.notes!),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _printPaymentReceipt(payment);
            },
            child: const Text('طباعة إيصال'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  void _printPaymentReceipt(PaymentModel payment) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاري طباعة الإيصال...'),
        backgroundColor: AppTheme.primaryColor,
      ),
    );
  }

  void _reconcilePayments() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسوية المدفوعات'),
        content: const Text(
            'هل تريد تسوية جميع المدفوعات؟\nسيتم التحقق من صحة جميع المعاملات وتحديث أرصدة الصناديق.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم تسوية المدفوعات بنجاح'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: const Text('تسوية'),
          ),
        ],
      ),
    );
  }

  void _exportToExcel() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاري تصدير التقرير إلى Excel...'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاري طباعة التقرير...'),
        backgroundColor: AppTheme.primaryColor,
      ),
    );
  }

  void _shareReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاري مشاركة التقرير...'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  Future<void> _exportToPdf() async {
    setState(() => _isExporting = true);
    try {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
            content: Text('ميزة تصدير PDF للمدفوعات غير متوفرة حالياً'),
            backgroundColor: Colors.orange),
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل تصدير PDF: $e')),
        );
      }
    } finally {
      if (mounted) setState(() => _isExporting = false);
    }
  }

  void _showAnalytics() {
    // Implementation of _showAnalytics method
  }
}

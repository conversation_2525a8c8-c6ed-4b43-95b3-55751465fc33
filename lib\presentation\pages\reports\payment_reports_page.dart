import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:untitled/data/models/payment_model.dart';
import 'package:untitled/data/models/client_model.dart';
import 'package:untitled/data/models/cashbox_model.dart';
import 'package:untitled/presentation/blocs/payment/payment_bloc.dart';
import 'package:untitled/presentation/blocs/payment/payment_event.dart';
import 'package:untitled/presentation/blocs/payment/payment_state.dart';
import 'package:untitled/presentation/blocs/client/client_bloc.dart';
import 'package:untitled/presentation/blocs/client/client_event.dart';
import 'package:untitled/presentation/blocs/client/client_state.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_bloc.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_event.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_state.dart';
import 'package:untitled/core/theme/app_theme.dart';

/// صفحة تقرير المدفوعات المتقدمة
class PaymentReportsPage extends StatefulWidget {
  const PaymentReportsPage({super.key});

  @override
  State<PaymentReportsPage> createState() => _PaymentReportsPageState();
}

class _PaymentReportsPageState extends State<PaymentReportsPage> {
  List<PaymentModel> _payments = [];
  List<ClientModel> _clients = [];
  List<CashboxModel> _cashboxes = [];
  
  bool _isLoading = false;
  int _loadedCount = 0;
  final int _totalDataSources = 3;
  
  // فلاتر
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();
  String _selectedClient = 'all';
  String _selectedCashbox = 'all';
  String _selectedType = 'all'; // all, income, expense
  String _sortBy = 'date'; // date, amount, client
  bool _sortAscending = false;
  
  // بحث
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadAllData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _loadAllData() {
    setState(() {
      _isLoading = true;
      _loadedCount = 0;
    });
    
    context.read<PaymentBloc>().add(const LoadPayments());
    context.read<ClientBloc>().add(const LoadClients());
    context.read<CashboxBloc>().add(const LoadCashboxes());
  }

  void _checkDataLoaded() {
    _loadedCount++;
    if (_loadedCount >= _totalDataSources) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  List<PaymentModel> get _filteredPayments {
    var filtered = _payments.where((payment) {
      // فلتر التاريخ
      final startOfDay = DateTime(_startDate.year, _startDate.month, _startDate.day);
      final endOfDay = DateTime(_endDate.year, _endDate.month, _endDate.day, 23, 59, 59);
      
      if (payment.paymentDate.isBefore(startOfDay) || payment.paymentDate.isAfter(endOfDay)) {
        return false;
      }
      
      // فلتر البحث
      if (_searchQuery.isNotEmpty) {
        final client = _getClientById(payment.clientId);
        final cashbox = _getCashboxById(payment.cashboxId);
        if (!(client?.name.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false) &&
            !(cashbox?.name.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false) &&
            !(payment.notes?.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false)) {
          return false;
        }
      }
      
      // فلتر العميل
      if (_selectedClient != 'all' && payment.clientId.toString() != _selectedClient) {
        return false;
      }
      
      // فلتر الصندوق
      if (_selectedCashbox != 'all' && payment.cashboxId.toString() != _selectedCashbox) {
        return false;
      }
      
      // فلتر النوع
      if (_selectedType != 'all') {
        if (_selectedType == 'income' && payment.amount <= 0) {
          return false;
        }
        if (_selectedType == 'expense' && payment.amount >= 0) {
          return false;
        }
      }
      
      return true;
    }).toList();

    // ترتيب
    filtered.sort((a, b) {
      int comparison = 0;
      switch (_sortBy) {
        case 'date':
          comparison = a.createdAt.compareTo(b.createdAt);
          break;
        case 'amount':
          comparison = a.amount.abs().compareTo(b.amount.abs());
          break;
        case 'client':
          final clientA = _getClientById(a.clientId);
          final clientB = _getClientById(b.clientId);
          comparison = (clientA?.name ?? '').compareTo(clientB?.name ?? '');
          break;
      }
      return _sortAscending ? comparison : -comparison;
    });

    return filtered;
  }

  ClientModel? _getClientById(int clientId) {
    try {
      return _clients.firstWhere((client) => client.id == clientId);
    } catch (e) {
      return null;
    }
  }

  CashboxModel? _getCashboxById(int cashboxId) {
    try {
      return _cashboxes.firstWhere((cashbox) => cashbox.id == cashboxId);
    } catch (e) {
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildAppBar(),
      body: MultiBlocListener(
        listeners: _buildBlocListeners(),
        child: _isLoading
            ? _buildLoadingWidget()
            : SingleChildScrollView(
                child: Column(
                  children: [
                    _buildFiltersSection(),
                    _buildSummaryCards(),
                    _buildChartsSection(),
                    _buildPaymentsList(),
                    const SizedBox(height: 80), // مساحة إضافية لتجنب تداخل FloatingActionButton
                  ],
                ),
              ),
      ),
      floatingActionButton: _buildFloatingActionButtons(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text(
        'تقرير المدفوعات',
        style: TextStyle(fontWeight: FontWeight.bold, color: Colors.white),
      ),
      backgroundColor: AppTheme.primaryColor,
      iconTheme: const IconThemeData(color: Colors.white),
      actions: [
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: _loadAllData,
          tooltip: 'تحديث البيانات',
        ),
        IconButton(
          icon: const Icon(Icons.file_download),
          onPressed: _exportToExcel,
          tooltip: 'تصدير إلى Excel',
        ),
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert),
          onSelected: (value) {
            switch (value) {
              case 'print':
                _printReport();
                break;
              case 'share':
                _shareReport();
                break;
              case 'reconcile':
                _reconcilePayments();
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'print',
              child: Row(
                children: [
                  Icon(Icons.print),
                  SizedBox(width: 8),
                  Text('طباعة التقرير'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'share',
              child: Row(
                children: [
                  Icon(Icons.share),
                  SizedBox(width: 8),
                  Text('مشاركة التقرير'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'reconcile',
              child: Row(
                children: [
                  Icon(Icons.balance),
                  SizedBox(width: 8),
                  Text('تسوية المدفوعات'),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  List<BlocListener> _buildBlocListeners() {
    return [
      BlocListener<PaymentBloc, PaymentState>(
        listener: (context, state) {
          if (state is PaymentsLoaded) {
            setState(() => _payments = state.payments);
            _checkDataLoaded();
          }
        },
      ),
      BlocListener<ClientBloc, ClientState>(
        listener: (context, state) {
          if (state is ClientsLoaded) {
            setState(() => _clients = state.clients);
            _checkDataLoaded();
          }
        },
      ),
      BlocListener<CashboxBloc, CashboxState>(
        listener: (context, state) {
          if (state is CashboxesLoaded) {
            setState(() => _cashboxes = state.cashboxes);
            _checkDataLoaded();
          }
        },
      ),
    ];
  }

  Widget _buildLoadingWidget() {
    return Container(
      color: Colors.white,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
          ),
          const SizedBox(height: 16),
          Text(
            'جاري تحميل بيانات المدفوعات...',
            style: TextStyle(fontSize: 16, color: Colors.grey.shade600),
          ),
          const SizedBox(height: 8),
          Text(
            'تم تحميل $_loadedCount من $_totalDataSources',
            style: TextStyle(fontSize: 14, color: Colors.grey[500]),
          ),
        ],
      ),
    );
  }

  Widget _buildFiltersSection() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.filter_list, color: AppTheme.primaryColor),
              const SizedBox(width: 8),
              const Text(
                'فلاتر التقرير',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              TextButton(
                onPressed: _resetFilters,
                child: const Text('إعادة تعيين'),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // شريط البحث
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'البحث بالعميل أو الصندوق أو الوصف...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        setState(() => _searchQuery = '');
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: AppTheme.primaryColor),
              ),
            ),
            onChanged: (value) {
              setState(() => _searchQuery = value);
            },
          ),
          
          const SizedBox(height: 16),
          
          // فلاتر الفترة الزمنية
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('من تاريخ:', style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 4),
                    InkWell(
                      onTap: () => _selectDate(context, true),
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.calendar_today, size: 16),
                            const SizedBox(width: 8),
                            Text(DateFormat('yyyy-MM-dd').format(_startDate)),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('إلى تاريخ:', style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 4),
                    InkWell(
                      onTap: () => _selectDate(context, false),
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.calendar_today, size: 16),
                            const SizedBox(width: 8),
                            Text(DateFormat('yyyy-MM-dd').format(_endDate)),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // فلاتر العميل والصندوق
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('العميل:', style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 4),
                    DropdownButtonFormField<String>(
                      value: _selectedClient,
                      isExpanded: true,
                      decoration: InputDecoration(
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      items: [
                        const DropdownMenuItem(value: 'all', child: Text('جميع العملاء', overflow: TextOverflow.ellipsis)),
                        ..._clients.map((client) => DropdownMenuItem(
                          value: client.id.toString(),
                          child: Text(client.name, overflow: TextOverflow.ellipsis),
                        )),
                      ],
                      onChanged: (value) {
                        setState(() => _selectedClient = value!);
                      },
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('الصندوق:', style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 4),
                    DropdownButtonFormField<String>(
                      value: _selectedCashbox,
                      isExpanded: true,
                      decoration: InputDecoration(
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      items: [
                        const DropdownMenuItem(value: 'all', child: Text('جميع الصناديق', overflow: TextOverflow.ellipsis)),
                        ..._cashboxes.map((cashbox) => DropdownMenuItem(
                          value: cashbox.id.toString(),
                          child: Text(cashbox.name, overflow: TextOverflow.ellipsis),
                        )),
                      ],
                      onChanged: (value) {
                        setState(() => _selectedCashbox = value!);
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // فلاتر النوع والترتيب
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('نوع المعاملة:', style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 4),
                    DropdownButtonFormField<String>(
                      value: _selectedType,
                      isExpanded: true,
                      decoration: InputDecoration(
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      items: const [
                        DropdownMenuItem(value: 'all', child: Text('جميع المعاملات', overflow: TextOverflow.ellipsis)),
                        DropdownMenuItem(value: 'income', child: Text('واردات', overflow: TextOverflow.ellipsis)),
                        DropdownMenuItem(value: 'expense', child: Text('صادرات', overflow: TextOverflow.ellipsis)),
                      ],
                      onChanged: (value) {
                        setState(() => _selectedType = value!);
                      },
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('ترتيب حسب:', style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Expanded(
                          child: DropdownButtonFormField<String>(
                            value: _sortBy,
                            isExpanded: true,
                            decoration: InputDecoration(
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                            ),
                            items: const [
                              DropdownMenuItem(value: 'date', child: Text('التاريخ', overflow: TextOverflow.ellipsis)),
                              DropdownMenuItem(value: 'amount', child: Text('المبلغ', overflow: TextOverflow.ellipsis)),
                              DropdownMenuItem(value: 'client', child: Text('العميل', overflow: TextOverflow.ellipsis)),
                            ],
                            onChanged: (value) {
                              setState(() => _sortBy = value!);
                            },
                          ),
                        ),
                        const SizedBox(width: 8),
                        IconButton(
                          icon: Icon(
                            _sortAscending ? Icons.arrow_upward : Icons.arrow_downward,
                            color: AppTheme.primaryColor,
                          ),
                          onPressed: () {
                            setState(() => _sortAscending = !_sortAscending);
                          },
                          tooltip: _sortAscending ? 'تصاعدي' : 'تنازلي',
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCards() {
    final filteredPayments = _filteredPayments;
    double totalIncome = filteredPayments.where((p) => p.amount > 0).fold(0.0, (sum, payment) => sum + payment.amount);
    double totalExpense = filteredPayments.where((p) => p.amount < 0).fold(0.0, (sum, payment) => sum + payment.amount.abs());
    double netAmount = totalIncome - totalExpense;
    int transactionCount = filteredPayments.length;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'ملخص المدفوعات',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),
          GridView.count(
            crossAxisCount: 2,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: 1.5,
            children: [
              _buildSummaryCard(
                'إجمالي المعاملات',
                '$transactionCount',
                Icons.receipt_long,
                Colors.blue,
                subtitle: 'للفترة المحددة',
              ),
              _buildSummaryCard(
                'إجمالي الواردات',
                '${totalIncome.toStringAsFixed(0)} ريال',
                Icons.arrow_downward,
                Colors.green,
              ),
              _buildSummaryCard(
                'إجمالي الصادرات',
                '${totalExpense.toStringAsFixed(0)} ريال',
                Icons.arrow_upward,
                Colors.red,
              ),
              _buildSummaryCard(
                'صافي الحركة',
                '${netAmount.toStringAsFixed(0)} ريال',
                Icons.account_balance,
                netAmount >= 0 ? Colors.green : Colors.red,
                subtitle: netAmount >= 0 ? 'ربح' : 'خسارة',
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(
    String title,
    String value,
    IconData icon,
    Color color, {
    String? subtitle,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(fontSize: 12, color: Colors.grey),
            textAlign: TextAlign.center,
          ),
          if (subtitle != null) ...[
            const SizedBox(height: 2),
            Text(
              subtitle,
              style: TextStyle(fontSize: 10, color: Colors.grey.shade600),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildChartsSection() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'الرسوم البيانية',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildMiniChart(
                  'المدفوعات اليومية',
                  _getDailyPaymentsChart(),
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildMiniChart(
                  'توزيع الصناديق',
                  _getCashboxDistributionChart(),
                  Colors.green,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMiniChart(String title, Widget chart, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 8),
          SizedBox(height: 60, child: chart),
        ],
      ),
    );
  }

  Widget _getDailyPaymentsChart() {
    return const Center(
      child: Text(
        'رسم بياني للمدفوعات اليومية\n(سيتم تطويره)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 10, color: Colors.grey),
      ),
    );
  }

  Widget _getCashboxDistributionChart() {
    return const Center(
      child: Text(
        'رسم بياني لتوزيع الصناديق\n(سيتم تطويره)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 10, color: Colors.grey),
      ),
    );
  }

  Widget _buildPaymentsList() {
    final filteredPayments = _filteredPayments;
    
    if (filteredPayments.isEmpty) {
      return _buildEmptyState();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header section
        Container(
          margin: const EdgeInsets.fromLTRB(16, 0, 16, 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'قائمة المدفوعات (${filteredPayments.length})',
                style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              Row(
                children: [
                  IconButton(
                    icon: const Icon(Icons.view_list),
                    onPressed: () {
                      // تغيير طريقة العرض
                    },
                    tooltip: 'عرض قائمة',
                  ),
                  IconButton(
                    icon: const Icon(Icons.view_module),
                    onPressed: () {
                      // تغيير طريقة العرض
                    },
                    tooltip: 'عرض بطاقات',
                  ),
                ],
              ),
            ],
          ),
        ),
        
        // List section
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          padding: const EdgeInsets.symmetric(horizontal: 16),
          itemCount: filteredPayments.length,
          itemBuilder: (context, index) {
            final payment = filteredPayments[index];
            return _buildPaymentCard(payment);
          },
        ),
      ],
    );
  }

  Widget _buildPaymentCard(PaymentModel payment) {
    final client = _getClientById(payment.clientId);
    final cashbox = _getCashboxById(payment.cashboxId);
    final isIncome = payment.amount > 0;
    final amountColor = isIncome ? Colors.green : Colors.red;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: amountColor.withValues(alpha: 0.1),
          child: Icon(
            isIncome ? Icons.add : Icons.remove,
            color: amountColor,
          ),
        ),
        title: Text(
          client?.name ?? 'عميل غير معروف',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الصندوق: ${cashbox?.name ?? 'غير محدد'}',
              style: TextStyle(color: Colors.grey.shade600),
            ),
            if (payment.notes != null && payment.notes!.isNotEmpty)
              Text(
                payment.notes!,
                style: TextStyle(color: Colors.grey[700]),
              ),
            Text(
              'التاريخ: ${DateFormat('yyyy-MM-dd HH:mm').format(payment.createdAt)}',
              style: TextStyle(color: Colors.grey[500], fontSize: 12),
            ),
          ],
        ),
        trailing: SizedBox(
          width: 80,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.end,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                '${payment.amount.toStringAsFixed(0)} ريال',
                style: TextStyle(
                  color: amountColor,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              Text(
                isIncome ? 'وارد' : 'صادر',
                style: TextStyle(
                  color: amountColor,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
        onTap: () => _showPaymentDetails(payment),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.search_off,
              size: 80,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد مدفوعات',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'جرب تغيير معايير البحث أو الفلاتر',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _resetFilters,
              child: const Text('إعادة تعيين الفلاتر'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFloatingActionButtons() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        FloatingActionButton(
          heroTag: "reconcile",
          onPressed: _reconcilePayments,
          backgroundColor: Colors.orange,
          tooltip: 'تسوية المدفوعات',
          child: const Icon(Icons.balance, color: Colors.white),
        ),
        const SizedBox(height: 8),
        FloatingActionButton(
          heroTag: "export",
          onPressed: _exportToExcel,
          backgroundColor: Colors.green,
          tooltip: 'تصدير إلى Excel',
          child: const Icon(Icons.file_download, color: Colors.white),
        ),
        const SizedBox(height: 8),
        FloatingActionButton(
          heroTag: "refresh",
          onPressed: _loadAllData,
          backgroundColor: AppTheme.primaryColor,
          tooltip: 'تحديث البيانات',
          child: const Icon(Icons.refresh, color: Colors.white),
        ),
      ],
    );
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isStartDate ? _startDate : _endDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
          // التأكد من أن تاريخ البداية قبل تاريخ النهاية
          if (_endDate.isBefore(_startDate)) {
            _endDate = _startDate.add(const Duration(days: 1));
          }
        } else {
          // التأكد من أن تاريخ النهاية بعد تاريخ البداية
          if (picked.isBefore(_startDate)) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تاريخ النهاية يجب أن يكون بعد تاريخ البداية'),
                backgroundColor: Colors.orange,
              ),
            );
            return;
          }
          _endDate = picked;
        }
      });
    }
  }

  void _resetFilters() {
    setState(() {
      _startDate = DateTime.now().subtract(const Duration(days: 30));
      _endDate = DateTime.now();
      _selectedClient = 'all';
      _selectedCashbox = 'all';
      _selectedType = 'all';
      _sortBy = 'date';
      _sortAscending = false;
      _searchController.clear();
      _searchQuery = '';
    });
  }

  void _showPaymentDetails(PaymentModel payment) {
    final client = _getClientById(payment.clientId);
    final cashbox = _getCashboxById(payment.cashboxId);
    final isIncome = payment.amount > 0;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تفاصيل ${isIncome ? 'الوارد' : 'الصادر'}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('العميل:', client?.name ?? 'غير محدد'),
            _buildDetailRow('الصندوق:', cashbox?.name ?? 'غير محدد'),
            _buildDetailRow('المبلغ:', '${payment.amount.toStringAsFixed(2)} ريال'),
            _buildDetailRow('التاريخ:', DateFormat('yyyy-MM-dd HH:mm').format(payment.createdAt)),
            if (payment.notes != null && payment.notes!.isNotEmpty)
              _buildDetailRow('الوصف:', payment.notes!),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _printPaymentReceipt(payment);
            },
            child: const Text('طباعة إيصال'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  void _printPaymentReceipt(PaymentModel payment) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاري طباعة الإيصال...'),
        backgroundColor: AppTheme.primaryColor,
      ),
    );
  }

  void _reconcilePayments() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسوية المدفوعات'),
        content: const Text('هل تريد تسوية جميع المدفوعات؟\nسيتم التحقق من صحة جميع المعاملات وتحديث أرصدة الصناديق.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم تسوية المدفوعات بنجاح'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: const Text('تسوية'),
          ),
        ],
      ),
    );
  }

  void _exportToExcel() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاري تصدير التقرير إلى Excel...'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاري طباعة التقرير...'),
        backgroundColor: AppTheme.primaryColor,
      ),
    );
  }

  void _shareReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاري مشاركة التقرير...'),
        backgroundColor: Colors.blue,
      ),
    );
  }
}

# 📊 دليل كشف الحساب المحاسبي الاحترافي

## نظرة عامة
تم تطوير كشف حساب العملاء ليتبع المعايير المحاسبية الاحترافية مع تصميم يلبي متطلبات المحاسبين والعملاء على حد سواء.

## المسار للوصول للوظيفة
```
الشريط الجانبي → التقارير الشاملة → كشف حساب العملاء
```

## 🆕 التحديث الجديد - النظام المحاسبي الاحترافي 3.0

## الميزات المحاسبية الجديدة

### 1. رأس التقرير المحاسبي الاحترافي 📋
- **اسم الحساب**: عرض اسم العميل في أعلى يمين الصفحة
- **رقم الحساب**: رقم فريد للعميل (مثل: 122010026) في أعلى وسط الصفحة
- **العملة**: عرض العملة المستخدمة (ريال يمني - YER)
- **الفترة الزمنية**: عرض واضح للفترة بالتواريخ الإنجليزية (DD-MM-YYYY)

### 2. الجدول المحاسبي الاحترافي 📊
الجدول الجديد يحتوي على الأعمدة التالية بالترتيب المحاسبي:

| التاريخ | نوع العمليه | رقم المستند | رقم القيد | الوصف | مدين | دائن | الرصيد |

**شرح الأعمدة:**
- **التاريخ**: بصيغة DD-MM-YYYY (إنجليزي)
- **نوع العمليه**: تسقية، دفعة نقدية، دفعة ديزل، تحويل، تسوية
- **رقم المستند**: رقم المرجع للمعاملة
- **رقم القيد**: رقم القيد المحاسبي (تلقائي)
- **الوصف**: وصف تفصيلي مع اسم المزرعة ومدة التسقية
- **مدين**: المبالغ المخصومة (أحمر)
- **دائن**: المبالغ المضافة (أخضر)
- **الرصيد**: الرصيد الجاري بعد كل معاملة

### 3. الأرصدة المحاسبية 💰
- **الرصيد الافتتاحي**: يظهر في بداية الجدول
- **الرصيد الختامي**: يظهر في نهاية الجدول
- **تنسيق الأرقام**: بفواصل الآلاف والعشرية
- **ألوان مميزة**: أحمر للمدين، أخضر للدائن

### 4. التذييل الاحترافي 📄
- **اسم الشركة**: في أسفل يسار الصفحة
- **تاريخ الطباعة**: بالتاريخ والوقت الإنجليزي
- **رقم الصفحة**: صفحة X من Y

## كيفية الاستخدام

### الخطوات:
1. انتقل إلى **التقارير الشاملة** → **كشف حساب العملاء**
2. اختر **الفترة الزمنية** المطلوبة
3. اضغط على **تطبيق الفلاتر** لعرض البيانات
4. اختر العميل المطلوب من القائمة
5. اضغط على زر **"طباعة كشف الحساب"**
6. انتظر حتى يتم إنشاء PDF
7. اختر **مشاركة** أو **عرض** الملف

### مثال على اسم الملف المُنشأ:
```
كشف_حساب_أحمد_محمد_1704067200000.pdf
```

## 📊 التصميم المحاسبي الاحترافي 3.0

### 🌟 المميزات المحاسبية الجديدة:

#### **1. رأس التقرير المحاسبي:**
```
┌─────────────────────────────────────────────────────────────┐
│                    شركة إدارة المزارع                       │
│ ─────────────────────────────────────────────────────────── │
│ اسم الحساب: أحمد محمد    رقم الحساب: 122010026    العملة: ريال يمني (YER) │
│                                                             │
│           كشف حساب للفترة من 01-01-2024 إلى 31-12-2024      │
└─────────────────────────────────────────────────────────────┘
```

#### **2. الجدول المحاسبي الاحترافي:**
```
┌─────────────────────────────────────────────────────────────┐
│ التاريخ │ نوع العمليه │ رقم المستند │ رقم القيد │ الوصف │ مدين │ دائن │ الرصيد │
├─────────┼─────────────┼─────────────┼──────────┼───────┼──────┼──────┼────────┤
│    -    │ رصيد افتتاحي │      -      │    -     │ الرصيد في بداية الفترة │  -   │  -   │ 1,000.00 │
├─────────┼─────────────┼─────────────┼──────────┼───────┼──────┼──────┼────────┤
│15-06-24 │   تسقية    │  IRR_001    │   0001   │ تسقية مزرعة الورود - 2.5 ساعة │ 150.00 │  -   │ 850.00 │
├─────────┼─────────────┼─────────────┼──────────┼───────┼──────┼──────┼────────┤
│20-06-24 │ دفعة نقدية  │  PAY_001    │   0002   │ دفعة من العميل │  -   │ 500.00 │ 1,350.00 │
├─────────┼─────────────┼─────────────┼──────────┼───────┼──────┼──────┼────────┤
│    -    │ رصيد ختامي  │      -      │    -     │ الرصيد في نهاية الفترة │  -   │  -   │ 1,350.00 │
└─────────┴─────────────┴─────────────┴──────────┴───────┴──────┴──────┴────────┘
```

#### **3. الألوان المحاسبية:**
- 🔴 **المدين (أحمر)**: المبالغ المخصومة من الحساب
- 🟢 **الدائن (أخضر)**: المبالغ المضافة للحساب
- 🔵 **الرصيد (أزرق)**: الرصيد الجاري بعد كل معاملة
- 🟡 **الأرصدة الخاصة**: الافتتاحي والختامي بألوان مميزة

#### **4. التذييل المحاسبي:**
```
┌─────────────────────────────────────────────────────────────┐
│ ─────────────────────────────────────────────────────────── │
│ شركة إدارة المزارع    تاريخ الطباعة: 08-12-2024 15:30    صفحة 1 من 1 │
└─────────────────────────────────────────────────────────────┘
```

#### **5. الميزات المحاسبية المتقدمة:**
- **أرقام الحسابات**: نظام ترقيم فريد لكل عميل (122010XXX)
- **أرقام القيود**: ترقيم تلقائي للقيود المحاسبية (0001, 0002, ...)
- **تنسيق العملة**: فواصل الآلاف والعشرية (1,350.00)
- **التواريخ الإنجليزية**: DD-MM-YYYY للوضوح المحاسبي
- **الترتيب الزمني**: المعاملات مرتبة تصاعدياً حسب التاريخ

## التحسينات التقنية الجديدة ⚡

### 1. هيكلة الكود المحسنة
- **دوال منفصلة**: تقسيم إنشاء PDF إلى دوال متخصصة
- **`_buildPdfHeader()`**: رأس الصفحة المحسن
- **`_buildInitialBalanceSection()`**: قسم الرصيد الابتدائي
- **`_buildTransactionsTable()`**: جدول المعاملات المتطور
- **`_buildFinalBalanceSection()`**: ملخص الأرصدة النهائية
- **`_buildSignatureSection()`**: قسم التوقيع والختم

### 2. تحسينات الجدول
- **أعمدة محسنة العرض**: `FixedColumnWidth` و `FlexColumnWidth`
- **ألوان متناوبة**: صفوف بيضاء ورمادية فاتحة
- **رأس جدول احترافي**: خلفية زرقاء داكنة مع نص أبيض
- **تنسيق ذكي للأرقام**: ألوان مختلفة حسب نوع المبلغ
- **معالجة النصوص الطويلة**: `maxLines` للوصف والملاحظات

### 3. معالجة الأخطاء المتقدمة
- **تحميل الخطوط**: fallback للخط الافتراضي في حالة عدم وجود الخطوط العربية
- **معالجة الاستثناءات**: رسائل خطأ واضحة للمستخدم
- **إدارة الذاكرة**: تنظيف الملفات المؤقتة
- **معالجة البيانات الفارغة**: رسائل واضحة عند عدم وجود معاملات

### 4. الأداء والتوافق
- **تحميل غير متزامن**: لا يتجمد التطبيق أثناء إنشاء PDF
- **ضغط البيانات**: ملفات PDF محسنة الحجم
- **كاش الخطوط**: تحميل الخطوط مرة واحدة
- **دعم متعدد المنصات**: يعمل على Android و Windows
- **مكتبات محدثة**: استخدام أحدث إصدارات مكتبات PDF
- **ترميز UTF-8**: دعم كامل للنصوص العربية

## الملفات المُعدلة

### 1. `lib/presentation/pages/reports/client_statements_page.dart`
- إضافة دالة `_printClientStatement()` محسنة
- تحسين معالجة الأحداث والأخطاء
- إضافة مؤشرات التحميل والحالة

### 2. `lib/services/pdf_service.dart`
- تحسين دالة `createClientStatementPdf()`
- إضافة معالجة للخطوط المفقودة
- تحسين تخطيط وتنسيق PDF

### 3. إضافة `test/pdf_service_test.dart`
- اختبارات شاملة لوظيفة PDF
- اختبار معالجة الأخطاء
- اختبار الحالات الحدية

## استكشاف الأخطاء

### مشاكل شائعة وحلولها:

#### 1. "تعذر تحميل الخطوط العربية"
- **السبب**: ملفات الخطوط غير موجودة
- **الحل**: سيتم استخدام الخط الافتراضي تلقائياً

#### 2. "فشل في إنشاء كشف الحساب"
- **السبب**: مشكلة في البيانات أو الذاكرة
- **الحل**: استخدم زر "إعادة المحاولة" أو أعد تشغيل التطبيق

#### 3. "لا يمكن مشاركة الملف"
- **السبب**: مشكلة في أذونات النظام
- **الحل**: تأكد من أذونات التخزين في إعدادات التطبيق

## التطوير المستقبلي

### ميزات مخططة:
- **قوالب PDF متعددة**: خيارات تخطيط مختلفة
- **تصدير Excel**: إضافة خيار تصدير لـ Excel
- **طباعة مباشرة**: إرسال لطابعة مباشرة
- **توقيع رقمي**: إضافة توقيع رقمي للملفات

### تحسينات تقنية:
- **ضغط أفضل**: تقليل حجم ملفات PDF
- **سرعة أكبر**: تحسين أداء إنشاء PDF
- **ذاكرة أقل**: تحسين استخدام الذاكرة

## الدعم التقني

في حالة مواجهة مشاكل:
1. تحقق من سجلات التطبيق (logs)
2. تأكد من وجود مساحة كافية في التخزين
3. أعد تشغيل التطبيق
4. تحقق من أذونات التطبيق

---

**ملاحظة**: هذه الوظيفة تم اختبارها وتعمل بشكل موثوق على جميع المنصات المدعومة.

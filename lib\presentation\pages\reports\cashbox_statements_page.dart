import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:untitled/data/models/cashbox_model.dart';
import 'package:untitled/data/models/payment_model.dart';
import 'package:untitled/data/models/client_model.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_bloc.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_event.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_state.dart';
import 'package:untitled/presentation/blocs/payment/payment_bloc.dart';
import 'package:untitled/presentation/blocs/payment/payment_event.dart';
import 'package:untitled/presentation/blocs/payment/payment_state.dart';
import 'package:untitled/presentation/blocs/client/client_bloc.dart';
import 'package:untitled/presentation/blocs/client/client_event.dart';
import 'package:untitled/presentation/blocs/client/client_state.dart';
import 'package:untitled/core/theme/app_theme.dart';
import 'package:untitled/services/cashbox_statement_service.dart';
import 'package:untitled/core/services/report_export_service.dart';
import 'package:untitled/services/pdf_service.dart';
import 'package:printing/printing.dart';

/// صفحة كشف حساب الصناديق المتقدمة
class CashboxStatementsPage extends StatefulWidget {
  const CashboxStatementsPage({super.key});

  @override
  State<CashboxStatementsPage> createState() => _CashboxStatementsPageState();
}

class _CashboxStatementsPageState extends State<CashboxStatementsPage> {
  List<CashboxModel> _cashboxes = [];
  List<PaymentModel> _payments = [];
  List<ClientModel> _clients = [];

  bool _isLoading = false;
  int _loadedCount = 0;
  final int _totalDataSources = 3;

  // فلاتر
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();
  String _selectedCashboxType = 'all'; // all, cash, diesel
  String _sortBy = 'name'; // name, balance, activity
  bool _sortAscending = true;

  // بحث
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  final CashboxStatementService _cashboxStatementService =
      CashboxStatementService();
  Map<int, List<CashboxTransactionModel>> _cashboxTransactions = {};
  Map<int, bool> _transactionsLoading = {};
  Map<int, Map<String, dynamic>> _cashboxStats = {};

  @override
  void initState() {
    super.initState();
    _loadAllData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _loadAllData() {
    setState(() {
      _isLoading = true;
      _loadedCount = 0;
    });

    context.read<CashboxBloc>().add(const LoadCashboxes());
    context.read<PaymentBloc>().add(const LoadPayments());
    context.read<ClientBloc>().add(const LoadClients());
  }

  void _checkDataLoaded() {
    _loadedCount++;
    if (_loadedCount >= _totalDataSources) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  List<CashboxModel> get _filteredCashboxes {
    var filtered = _cashboxes.where((cashbox) {
      // فلتر البحث
      if (_searchQuery.isNotEmpty) {
        if (!cashbox.name.toLowerCase().contains(_searchQuery.toLowerCase())) {
          return false;
        }
      }

      // فلتر النوع
      if (_selectedCashboxType != 'all') {
        if (cashbox.type != _selectedCashboxType) {
          return false;
        }
      }

      return true;
    }).toList();

    // ترتيب
    filtered.sort((a, b) {
      int comparison = 0;
      switch (_sortBy) {
        case 'name':
          comparison = a.name.compareTo(b.name);
          break;
        case 'balance':
          comparison = a.balance.compareTo(b.balance);
          break;
        case 'activity':
          final activityA = _getCashboxActivity(a.id!);
          final activityB = _getCashboxActivity(b.id!);
          comparison = activityA.compareTo(activityB);
          break;
      }
      return _sortAscending ? comparison : -comparison;
    });

    return filtered;
  }

  int _getCashboxActivity(int cashboxId) {
    return _payments
        .where((payment) =>
            payment.cashboxId == cashboxId &&
            payment.createdAt.isAfter(_startDate) &&
            payment.createdAt.isBefore(_endDate.add(const Duration(days: 1))))
        .length;
  }

  double _getCashboxTotalIncome(int cashboxId) {
    return _payments
        .where((payment) =>
            payment.cashboxId == cashboxId &&
            payment.amount > 0 &&
            payment.createdAt.isAfter(_startDate) &&
            payment.createdAt.isBefore(_endDate.add(const Duration(days: 1))))
        .fold(0.0, (sum, payment) => sum + payment.amount);
  }

  double _getCashboxTotalExpense(int cashboxId) {
    return _payments
        .where((payment) =>
            payment.cashboxId == cashboxId &&
            payment.amount < 0 &&
            payment.createdAt.isAfter(_startDate) &&
            payment.createdAt.isBefore(_endDate.add(const Duration(days: 1))))
        .fold(0.0, (sum, payment) => sum + payment.amount.abs());
  }

  List<PaymentModel> _getCashboxTransactions(int cashboxId) {
    return _payments
        .where((payment) =>
            payment.cashboxId == cashboxId &&
            payment.createdAt.isAfter(_startDate) &&
            payment.createdAt.isBefore(_endDate.add(const Duration(days: 1))))
        .toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
  }

  Future<void> _loadCashboxTransactions(int cashboxId, String type) async {
    setState(() {
      _transactionsLoading[cashboxId] = true;
    });

    try {
      final transactions =
          await _cashboxStatementService.getCashboxTransactions(
        cashboxId: cashboxId,
        fromDate: _startDate,
        toDate: _endDate,
      );

      // حساب الإحصائيات من المعاملات المحملة
      double totalIncome = 0;
      double totalExpenses = 0;
      double currentBalance = 0;

      for (final transaction in transactions) {
        if (transaction.amount > 0) {
          totalIncome += transaction.amount;
        } else {
          totalExpenses += transaction.amount.abs();
        }
        currentBalance = transaction.balanceAfter;
      }

      setState(() {
        _cashboxTransactions[cashboxId] = transactions;
        _transactionsLoading[cashboxId] = false;

        // تحديث إحصائيات الصندوق
        _cashboxStats[cashboxId] = {
          'totalIncome': totalIncome,
          'totalExpenses': totalExpenses,
          'currentBalance': currentBalance,
          'transactionCount': transactions.length,
        };
      });
    } catch (e) {
      debugPrint('❌ خطأ في تحميل معاملات الصندوق $cashboxId: $e');
      setState(() {
        _transactionsLoading[cashboxId] = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildAppBar(),
      body: MultiBlocListener(
        listeners: _buildBlocListeners(),
        child: _isLoading
            ? _buildLoadingWidget()
            : SingleChildScrollView(
                child: Column(
                  children: [
                    _buildFiltersSection(),
                    _buildSummaryCards(),
                    _buildCashboxesList(),
                    const SizedBox(
                        height:
                            80), // مساحة إضافية لتجنب تداخل FloatingActionButton
                  ],
                ),
              ),
      ),
      floatingActionButton: _buildFloatingActionButtons(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text(
        'كشف حساب الصناديق',
        style: TextStyle(fontWeight: FontWeight.bold, color: Colors.white),
      ),
      backgroundColor: AppTheme.primaryColor,
      iconTheme: const IconThemeData(color: Colors.white),
      actions: [
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: _loadAllData,
          tooltip: 'تحديث البيانات',
        ),
        IconButton(
          icon: const Icon(Icons.file_download),
          onPressed: _exportToExcel,
          tooltip: 'تصدير إلى Excel',
        ),
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert),
          onSelected: (value) {
            switch (value) {
              case 'print':
                _printReport();
                break;
              case 'share':
                _shareReport();
                break;
              case 'reconcile':
                _reconcileAllCashboxes();
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'print',
              child: Row(
                children: [
                  Icon(Icons.print),
                  SizedBox(width: 8),
                  Text('طباعة التقرير'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'share',
              child: Row(
                children: [
                  Icon(Icons.share),
                  SizedBox(width: 8),
                  Text('مشاركة التقرير'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'reconcile',
              child: Row(
                children: [
                  Icon(Icons.balance),
                  SizedBox(width: 8),
                  Text('تسوية الصناديق'),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  List<BlocListener> _buildBlocListeners() {
    return [
      BlocListener<CashboxBloc, CashboxState>(
        listener: (context, state) {
          if (state is CashboxesLoaded) {
            setState(() => _cashboxes = state.cashboxes);
            _checkDataLoaded();
          }
        },
      ),
      BlocListener<PaymentBloc, PaymentState>(
        listener: (context, state) {
          if (state is PaymentsLoaded) {
            setState(() => _payments = state.payments);
            _checkDataLoaded();
          }
        },
      ),
      BlocListener<ClientBloc, ClientState>(
        listener: (context, state) {
          if (state is ClientsLoaded) {
            setState(() => _clients = state.clients);
            _checkDataLoaded();
          }
        },
      ),
    ];
  }

  Widget _buildLoadingWidget() {
    return Container(
      color: Colors.white,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
          ),
          const SizedBox(height: 16),
          Text(
            'جاري تحميل بيانات الصناديق...',
            style: TextStyle(fontSize: 16, color: Colors.grey.shade600),
          ),
          const SizedBox(height: 8),
          Text(
            'تم تحميل $_loadedCount من $_totalDataSources',
            style: TextStyle(fontSize: 14, color: Colors.grey[500]),
          ),
        ],
      ),
    );
  }

  Widget _buildFiltersSection() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.filter_list, color: AppTheme.primaryColor),
              const SizedBox(width: 8),
              const Text(
                'فلاتر التقرير',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              TextButton(
                onPressed: _resetFilters,
                child: const Text('إعادة تعيين'),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // شريط البحث
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'البحث باسم الصندوق...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        setState(() => _searchQuery = '');
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: AppTheme.primaryColor),
              ),
            ),
            onChanged: (value) {
              setState(() => _searchQuery = value);
            },
          ),
          const SizedBox(height: 16),

          // فلاتر التاريخ والنوع
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('من تاريخ:',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 4),
                    InkWell(
                      onTap: () => _selectDate(context, true),
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 8),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.calendar_today, size: 16),
                            const SizedBox(width: 8),
                            Text(DateFormat('yyyy-MM-dd').format(_startDate)),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('إلى تاريخ:',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 4),
                    InkWell(
                      onTap: () => _selectDate(context, false),
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 8),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.calendar_today, size: 16),
                            const SizedBox(width: 8),
                            Text(DateFormat('yyyy-MM-dd').format(_endDate)),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // فلتر نوع الصندوق والترتيب
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('نوع الصندوق:',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 4),
                    DropdownButtonFormField<String>(
                      value: _selectedCashboxType,
                      decoration: InputDecoration(
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 8),
                      ),
                      items: const [
                        DropdownMenuItem(
                            value: 'all', child: Text('جميع الأنواع')),
                        DropdownMenuItem(value: 'cash', child: Text('نقدي')),
                        DropdownMenuItem(value: 'diesel', child: Text('ديزل')),
                      ],
                      onChanged: (value) {
                        setState(() => _selectedCashboxType = value!);
                      },
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('ترتيب حسب:',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Expanded(
                          child: DropdownButtonFormField<String>(
                            value: _sortBy,
                            decoration: InputDecoration(
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 8),
                            ),
                            items: const [
                              DropdownMenuItem(
                                  value: 'name', child: Text('الاسم')),
                              DropdownMenuItem(
                                  value: 'balance', child: Text('الرصيد')),
                              DropdownMenuItem(
                                  value: 'type', child: Text('النوع')),
                            ],
                            onChanged: (value) {
                              setState(() => _sortBy = value!);
                            },
                          ),
                        ),
                        const SizedBox(width: 8),
                        IconButton(
                          icon: Icon(
                            _sortAscending
                                ? Icons.arrow_upward
                                : Icons.arrow_downward,
                            color: AppTheme.primaryColor,
                          ),
                          onPressed: () {
                            setState(() => _sortAscending = !_sortAscending);
                          },
                          tooltip: _sortAscending ? 'تصاعدي' : 'تنازلي',
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCards() {
    final totalCashboxes = _filteredCashboxes.length;
    final totalCashBalance = _filteredCashboxes
        .where((c) => c.type == 'cash')
        .fold<double>(0, (sum, cashbox) => sum + cashbox.balance);
    final totalDieselBalance = _filteredCashboxes
        .where((c) => c.type == 'diesel')
        .fold<double>(0, (sum, cashbox) => sum + cashbox.balance);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'ملخص الصناديق',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),
          GridView.count(
            crossAxisCount: 2,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: 1.5,
            children: [
              _buildSummaryCard(
                'إجمالي الصناديق',
                '$totalCashboxes',
                Icons.account_balance_wallet,
                Colors.blue,
                subtitle: 'جميع الأنواع',
              ),
              _buildSummaryCard(
                'إجمالي النقد',
                '${totalCashBalance.toStringAsFixed(0)} ريال',
                Icons.attach_money,
                totalCashBalance >= 0 ? Colors.green : Colors.red,
              ),
              _buildSummaryCard(
                'إجمالي الديزل',
                '${totalDieselBalance.toStringAsFixed(0)} لتر',
                Icons.local_gas_station,
                totalDieselBalance >= 0 ? Colors.orange : Colors.red,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(
    String title,
    String value,
    IconData icon,
    Color color, {
    String? subtitle,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(fontSize: 12, color: Colors.grey),
            textAlign: TextAlign.center,
          ),
          if (subtitle != null) ...[
            const SizedBox(height: 2),
            Text(
              subtitle,
              style: TextStyle(fontSize: 10, color: Colors.grey.shade600),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCashboxesList() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Text(
                'قائمة الصناديق',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              Text(
                '${_filteredCashboxes.length} صندوق',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _filteredCashboxes.length,
            itemBuilder: (context, index) {
              final cashbox = _filteredCashboxes[index];
              return _buildCashboxCard(cashbox);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildCashboxCard(CashboxModel cashbox) {
    final cashboxId = cashbox.id!;
    final isLoading = _transactionsLoading[cashboxId] == true;
    final transactions = _cashboxTransactions[cashboxId] ?? [];

    // حساب الإحصائيات المفصلة
    double totalPayments = 0;
    int countPayments = 0;
    double totalIrrigations = 0;
    int countIrrigations = 0;
    double totalTransferIn = 0;
    int countTransferIn = 0;
    double totalTransferOut = 0;
    int countTransferOut = 0;
    double totalWithdraw = 0;
    int countWithdraw = 0;
    double totalDeposit = 0;
    int countDeposit = 0;
    double finalBalance = cashbox.balance;

    for (final t in transactions) {
      switch (t.type) {
        case CashboxTransactionType.payment_in:
          totalPayments += t.amount;
          countPayments++;
          break;
        case CashboxTransactionType.irrigation_cost:
          totalIrrigations += t.amount.abs();
          countIrrigations++;
          break;
        case CashboxTransactionType.transfer_in:
          totalTransferIn += t.amount;
          countTransferIn++;
          break;
        case CashboxTransactionType.transfer_out:
          totalTransferOut += t.amount.abs();
          countTransferOut++;
          break;
        case CashboxTransactionType.withdraw:
          totalWithdraw += t.amount.abs();
          countWithdraw++;
          break;
        case CashboxTransactionType.deposit:
          totalDeposit += t.amount;
          countDeposit++;
          break;
        default:
          break;
      }
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ExpansionTile(
        leading: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: _getCashboxTypeColor(cashbox.type).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            _getCashboxTypeIcon(cashbox.type),
            color: _getCashboxTypeColor(cashbox.type),
            size: 24,
          ),
        ),
        title: Row(
          children: [
            Expanded(
              child: Text(
                cashbox.name,
                style:
                    const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
              ),
            ),
            const SizedBox(width: 8),
            ElevatedButton.icon(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.indigo.shade50,
                foregroundColor: Colors.indigo.shade800,
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                textStyle: const TextStyle(fontSize: 12),
                elevation: 0,
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8)),
              ),
              icon: const Icon(Icons.picture_as_pdf, size: 18),
              label: const Text('تصدير PDF'),
              onPressed: () async {
                // فلترة المعاملات حسب الفترة المحددة في الفلاتر
                final filteredTransactions = transactions
                    .where((t) =>
                        t.date.isAfter(
                            _startDate.subtract(const Duration(seconds: 1))) &&
                        t.date.isBefore(_endDate.add(const Duration(days: 1))))
                    .toList();
                if (filteredTransactions.isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
                      content: Text('لا توجد معاملات في هذه الفترة.')));
                  return;
                }
                // final file = await pdfService.createCashboxStatementPdf(...);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                      content:
                          Text('ميزة تصدير PDF للصناديق غير متوفرة حالياً'),
                      backgroundColor: Colors.orange),
                );
              },
            ),
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الرصيد الحالي: ${cashbox.balance.toStringAsFixed(2)} ${cashbox.type == 'cash' ? 'ريال' : 'لتر'}',
              style: TextStyle(
                color: _getBalanceColor(cashbox.balance),
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 4),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color:
                    _getCashboxTypeColor(cashbox.type).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                _getCashboxTypeText(cashbox.type),
                style: TextStyle(
                  color: _getCashboxTypeColor(cashbox.type),
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        trailing: isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
            : Icon(
                Icons.expand_more,
                color: Colors.grey.shade600,
              ),
        onExpansionChanged: (expanded) {
          if (expanded && !_cashboxTransactions.containsKey(cashboxId)) {
            _loadCashboxTransactions(cashboxId, cashbox.type);
          }
        },
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            child: isLoading
                ? const Center(child: CircularProgressIndicator())
                : transactions.isEmpty
                    ? Padding(
                        padding: const EdgeInsets.all(16),
                        child: Text('لا توجد معاملات في الفترة المحددة',
                            style: TextStyle(color: Colors.grey.shade600)),
                      )
                    : Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // ملخص الصندوق المفصل
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: Colors.grey[50],
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text('ملخص الصندوق',
                                    style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 14)),
                                const SizedBox(height: 8),
                                Row(
                                  children: [
                                    Expanded(
                                        child: _buildStatItem(
                                            'إجمالي الدفعات',
                                            '${totalPayments.toStringAsFixed(2)}',
                                            Colors.green)),
                                    Expanded(
                                        child: _buildStatItem('عدد الدفعات',
                                            '$countPayments', Colors.green)),
                                  ],
                                ),
                                const SizedBox(height: 8),
                                Row(
                                  children: [
                                    Expanded(
                                        child: _buildStatItem(
                                            'إجمالي التسقيات',
                                            '${totalIrrigations.toStringAsFixed(2)}',
                                            Colors.red)),
                                    Expanded(
                                        child: _buildStatItem('عدد التسقيات',
                                            '$countIrrigations', Colors.red)),
                                  ],
                                ),
                                const SizedBox(height: 8),
                                Row(
                                  children: [
                                    Expanded(
                                        child: _buildStatItem(
                                            'إجمالي التحويلات الواردة',
                                            '${totalTransferIn.toStringAsFixed(2)}',
                                            Colors.blue)),
                                    Expanded(
                                        child: _buildStatItem(
                                            'عدد التحويلات الواردة',
                                            '$countTransferIn',
                                            Colors.blue)),
                                  ],
                                ),
                                const SizedBox(height: 8),
                                Row(
                                  children: [
                                    Expanded(
                                        child: _buildStatItem(
                                            'إجمالي التحويلات الصادرة',
                                            '${totalTransferOut.toStringAsFixed(2)}',
                                            Colors.orange)),
                                    Expanded(
                                        child: _buildStatItem(
                                            'عدد التحويلات الصادرة',
                                            '$countTransferOut',
                                            Colors.orange)),
                                  ],
                                ),
                                const SizedBox(height: 8),
                                Row(
                                  children: [
                                    Expanded(
                                        child: _buildStatItem(
                                            'إجمالي السحب',
                                            '${totalWithdraw.toStringAsFixed(2)}',
                                            Colors.red)),
                                    Expanded(
                                        child: _buildStatItem('عدد السحب',
                                            '$countWithdraw', Colors.red)),
                                  ],
                                ),
                                const SizedBox(height: 8),
                                Row(
                                  children: [
                                    Expanded(
                                        child: _buildStatItem(
                                            'إجمالي الإيداع',
                                            '${totalDeposit.toStringAsFixed(2)}',
                                            Colors.green)),
                                    Expanded(
                                        child: _buildStatItem('عدد الإيداع',
                                            '$countDeposit', Colors.green)),
                                  ],
                                ),
                                const SizedBox(height: 8),
                                _buildStatItem(
                                    'إجمالي الصندوق',
                                    '${finalBalance.toStringAsFixed(2)} ${cashbox.type == 'cash' ? 'ريال' : 'لتر'}',
                                    finalBalance >= 0
                                        ? Colors.green
                                        : Colors.red),
                              ],
                            ),
                          ),
                          const SizedBox(height: 16),
                          // جدول جميع الأحداث بالتفصيل
                          _buildTransactionsTable(transactions),
                        ],
                      ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
          ),
        ),
      ],
    );
  }

  Widget _buildTransactionsTable(List<CashboxTransactionModel> transactions) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: DataTable(
        columns: const [
          DataColumn(
              label: Text('التاريخ',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 15))),
          DataColumn(
              label: Text('النوع',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 15))),
          DataColumn(
              label: Text('الوصف',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 15))),
          DataColumn(
              label: Text('مدين',
                  style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 15,
                      color: Colors.red))),
          DataColumn(
              label: Text('دائن',
                  style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 15,
                      color: Colors.green))),
          DataColumn(
              label: Text('الرصيد قبل',
                  style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 15,
                      color: Colors.orange))),
          DataColumn(
              label: Text('الرصيد بعد',
                  style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 15,
                      color: Colors.blue))),
          DataColumn(
              label: Text('الطرف المرتبط',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 15))),
          DataColumn(
              label: Text('ملاحظات',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 15))),
          DataColumn(
              label: Text('رقم العملية',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 15))),
        ],
        rows: transactions
            .map((t) => DataRow(cells: [
                  DataCell(Text(DateFormat('yyyy-MM-dd').format(t.date),
                      style: const TextStyle(fontSize: 14))),
                  DataCell(Text(t.typeArabicName,
                      style: const TextStyle(fontSize: 14))),
                  DataCell(Text(t.description,
                      style: const TextStyle(fontSize: 14))),
                  DataCell(Text(
                      t.amount < 0 ? t.amount.abs().toStringAsFixed(2) : '',
                      style: const TextStyle(
                          color: Colors.red,
                          fontWeight: FontWeight.bold,
                          fontSize: 14))),
                  DataCell(Text(t.amount > 0 ? t.amount.toStringAsFixed(2) : '',
                      style: const TextStyle(
                          color: Colors.green,
                          fontWeight: FontWeight.bold,
                          fontSize: 14))),
                  DataCell(Text(t.balanceBefore.toStringAsFixed(2),
                      style: const TextStyle(
                          color: Colors.orange,
                          fontWeight: FontWeight.bold,
                          fontSize: 14))),
                  DataCell(Text(t.balanceAfter.toStringAsFixed(2),
                      style: const TextStyle(
                          color: Colors.blue,
                          fontWeight: FontWeight.bold,
                          fontSize: 14))),
                  DataCell(Text(t.clientName ?? '-',
                      style: const TextStyle(fontSize: 14))),
                  DataCell(Text(t.notes ?? '-',
                      style: const TextStyle(fontSize: 14))),
                  DataCell(Text(t.referenceId ?? '-',
                      style:
                          const TextStyle(fontSize: 13, color: Colors.grey))),
                ]))
            .toList(),
      ),
    );
  }

  Widget _buildFloatingActionButtons() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        FloatingActionButton(
          heroTag: "export",
          onPressed: () {
            if (_filteredCashboxes.isNotEmpty) {
              _exportToPDF(_filteredCashboxes.first);
            }
          },
          backgroundColor: Colors.green,
          tooltip: 'تصدير إلى PDF',
          child: const Icon(Icons.picture_as_pdf, color: Colors.white),
        ),
        const SizedBox(height: 8),
        FloatingActionButton(
          heroTag: "refresh",
          onPressed: _loadAllData,
          backgroundColor: AppTheme.primaryColor,
          tooltip: 'تحديث البيانات',
          child: const Icon(Icons.refresh, color: Colors.white),
        ),
      ],
    );
  }

  Color _getBalanceColor(double balance) {
    if (balance > 0) return Colors.green;
    if (balance < 0) return Colors.red;
    return Colors.grey;
  }

  Color _getCashboxTypeColor(String type) {
    switch (type) {
      case 'cash':
        return Colors.green;
      case 'diesel':
        return Colors.orange;
      default:
        return Colors.blue;
    }
  }

  IconData _getCashboxTypeIcon(String type) {
    switch (type) {
      case 'cash':
        return Icons.attach_money;
      case 'diesel':
        return Icons.local_gas_station;
      default:
        return Icons.account_balance_wallet;
    }
  }

  String _getCashboxTypeText(String type) {
    switch (type) {
      case 'cash':
        return 'نقدي';
      case 'diesel':
        return 'ديزل';
      default:
        return 'عام';
    }
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final date = await showDatePicker(
      context: context,
      initialDate: isStartDate ? _startDate : _endDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );

    if (date != null) {
      setState(() {
        if (isStartDate) {
          _startDate = date;
          if (_endDate.isBefore(_startDate)) {
            _endDate = _startDate;
          }
        } else {
          _endDate = date;
          if (_startDate.isAfter(_endDate)) {
            _startDate = _endDate;
          }
        }
      });
    }
  }

  void _resetFilters() {
    setState(() {
      _startDate = DateTime.now().subtract(const Duration(days: 30));
      _endDate = DateTime.now();
      _selectedCashboxType = 'all';
      _sortBy = 'name';
      _sortAscending = true;
      _searchQuery = '';
      _searchController.clear();
    });
  }

  void _exportToExcel() {
    // تنفيذ تصدير Excel
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _printReport() {
    // تنفيذ طباعة التقرير
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تحضير التقرير للطباعة...')),
    );
  }

  void _shareReport() {
    // تنفيذ مشاركة التقرير
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تحضير التقرير للمشاركة...')),
    );
  }

  void _reconcileAllCashboxes() {
    // تنفيذ تسوية جميع الصناديق
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسوية الصناديق'),
        content: const Text('هل تريد تسوية جميع الصناديق؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('تم تسوية الصناديق بنجاح')),
              );
            },
            child: const Text('تأكيد'),
          ),
        ],
      ),
    );
  }

  void _showAllTransactions(
      CashboxModel cashbox, List<CashboxTransactionModel> transactions) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => Scaffold(
          appBar: AppBar(
            title: Text('معاملات ${cashbox.name}'),
            backgroundColor: AppTheme.primaryColor,
            foregroundColor: Colors.white,
          ),
          body: _buildTransactionsTable(transactions),
        ),
      ),
    );
  }

  void _addTransaction(CashboxModel cashbox) async {
    // عرض Dialog لإضافة معاملة جديدة (سحب، إيداع، تحويل، تسوية)
    await showDialog(
      context: context,
      builder: (context) => AddCashboxTransactionDialog(
        cashbox: cashbox,
        onTransactionAdded: () =>
            _loadCashboxTransactions(cashbox.id!, cashbox.type),
      ),
    );
  }

  void _reconcileCashbox(CashboxModel cashbox) {
    // تسوية صندوق محدد
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تسوية ${cashbox.name}'),
        content: const Text('هل تريد تسوية هذا الصندوق؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('تم تسوية ${cashbox.name} بنجاح')),
              );
            },
            child: const Text('تأكيد'),
          ),
        ],
      ),
    );
  }

  void _exportToPDF(CashboxModel cashbox) async {
    final transactions = _cashboxTransactions[cashbox.id] ?? [];
    if (transactions.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('لا توجد معاملات للتصدير.')));
      return;
    }
    // final file = await pdfService.createCashboxStatementPdf(...);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
          content: Text('ميزة تصدير PDF للصناديق غير متوفرة حالياً'),
          backgroundColor: Colors.orange),
    );
  }
}

class AddCashboxTransactionDialog extends StatefulWidget {
  final CashboxModel cashbox;
  final VoidCallback onTransactionAdded;
  const AddCashboxTransactionDialog({
    Key? key,
    required this.cashbox,
    required this.onTransactionAdded,
  }) : super(key: key);

  @override
  State<AddCashboxTransactionDialog> createState() =>
      _AddCashboxTransactionDialogState();
}

class _AddCashboxTransactionDialogState
    extends State<AddCashboxTransactionDialog> {
  final _formKey = GlobalKey<FormState>();
  String _type = 'deposit';
  double _amount = 0.0;
  String? _notes;
  String? _targetCashboxName;
  int? _targetCashboxId;
  bool _isSubmitting = false;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('إضافة معاملة جديدة'),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              DropdownButtonFormField<String>(
                value: _type,
                decoration: const InputDecoration(labelText: 'نوع المعاملة'),
                items: const [
                  DropdownMenuItem(value: 'deposit', child: Text('إيداع')),
                  DropdownMenuItem(value: 'withdraw', child: Text('سحب')),
                  DropdownMenuItem(
                      value: 'transfer', child: Text('تحويل إلى صندوق آخر')),
                  DropdownMenuItem(value: 'settlement', child: Text('تسوية')),
                ],
                onChanged: (val) {
                  setState(() => _type = val!);
                },
              ),
              const SizedBox(height: 12),
              if (_type == 'transfer') ...[
                _buildTargetCashboxDropdown(),
                const SizedBox(height: 12),
              ],
              TextFormField(
                decoration: const InputDecoration(labelText: 'المبلغ'),
                keyboardType: TextInputType.numberWithOptions(decimal: true),
                validator: (val) {
                  final v = double.tryParse(val ?? '');
                  if (v == null || v <= 0) return 'أدخل مبلغًا صحيحًا';
                  return null;
                },
                onSaved: (val) => _amount = double.tryParse(val ?? '') ?? 0.0,
              ),
              const SizedBox(height: 12),
              TextFormField(
                decoration:
                    const InputDecoration(labelText: 'ملاحظات (اختياري)'),
                onSaved: (val) => _notes = val,
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isSubmitting ? null : () => Navigator.pop(context),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _isSubmitting ? null : _submit,
          child: _isSubmitting
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2))
              : const Text('إضافة'),
        ),
      ],
    );
  }

  Widget _buildTargetCashboxDropdown() {
    // ملاظة: يجب جلب قائمة الصناديق من مكان مركزي أو تمريرها للـDialog
    // هنا مثال مبسط (يجب التعديل حسب بنية التطبيق)
    final allCashboxes = (context
                .findAncestorStateOfType<_CashboxStatementsPageState>()
                ?._cashboxes ??
            [])
        .where((c) => c.id != widget.cashbox.id)
        .toList();
    return DropdownButtonFormField<int>(
      value: _targetCashboxId,
      decoration: const InputDecoration(labelText: 'الصندوق المستهدف'),
      items: allCashboxes
          .map((c) => DropdownMenuItem(value: c.id, child: Text(c.name)))
          .toList(),
      onChanged: (val) {
        setState(() {
          _targetCashboxId = val;
          _targetCashboxName = allCashboxes.firstWhere((c) => c.id == val).name;
        });
      },
      validator: (val) {
        if (_type == 'transfer' && val == null) return 'اختر الصندوق المستهدف';
        return null;
      },
    );
  }

  Future<void> _submit() async {
    if (!_formKey.currentState!.validate()) return;
    _formKey.currentState!.save();
    setState(() => _isSubmitting = true);
    try {
      // تنفيذ إضافة المعاملة حسب النوع
      final service = CashboxStatementService();
      if (_type == 'deposit') {
        await service.addDepositTransaction(
          cashboxId: widget.cashbox.id!,
          amount: _amount,
          notes: _notes ?? '',
        );
      } else if (_type == 'withdraw') {
        await service.addWithdrawTransaction(
          cashboxId: widget.cashbox.id!,
          amount: _amount,
          notes: _notes ?? '',
        );
      } else if (_type == 'transfer') {
        if (_targetCashboxId == null)
          throw Exception('يجب اختيار الصندوق المستهدف');
        await service.addTransferTransaction(
          fromCashboxId: widget.cashbox.id!,
          toCashboxId: _targetCashboxId!,
          amount: _amount,
          notes: _notes ?? '',
        );
      } else if (_type == 'settlement') {
        await service.addSettlementTransaction(
          cashboxId: widget.cashbox.id!,
          amount: _amount,
          notes: _notes ?? '',
        );
      }
      widget.onTransactionAdded();
      Navigator.pop(context);
    } catch (e) {
      setState(() => _isSubmitting = false);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ: ${e.toString()}')),
      );
    }
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:untitled/data/models/cashbox_model.dart';
import 'package:untitled/data/models/payment_model.dart';
import 'package:untitled/data/models/client_model.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_bloc.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_event.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_state.dart';
import 'package:untitled/presentation/blocs/payment/payment_bloc.dart';
import 'package:untitled/presentation/blocs/payment/payment_event.dart';
import 'package:untitled/presentation/blocs/payment/payment_state.dart';
import 'package:untitled/presentation/blocs/client/client_bloc.dart';
import 'package:untitled/presentation/blocs/client/client_event.dart';
import 'package:untitled/presentation/blocs/client/client_state.dart';
import 'package:untitled/core/theme/app_theme.dart';

/// صفحة كشف حساب الصناديق المتقدمة
class CashboxStatementsPage extends StatefulWidget {
  const CashboxStatementsPage({super.key});

  @override
  State<CashboxStatementsPage> createState() => _CashboxStatementsPageState();
}

class _CashboxStatementsPageState extends State<CashboxStatementsPage> {
  List<CashboxModel> _cashboxes = [];
  List<PaymentModel> _payments = [];
  List<ClientModel> _clients = [];
  
  bool _isLoading = false;
  int _loadedCount = 0;
  final int _totalDataSources = 3;
  
  // فلاتر
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();
  String _selectedCashboxType = 'all'; // all, cash, diesel
  String _sortBy = 'name'; // name, balance, activity
  bool _sortAscending = true;
  
  // بحث
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadAllData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _loadAllData() {
    setState(() {
      _isLoading = true;
      _loadedCount = 0;
    });
    
    context.read<CashboxBloc>().add(const LoadCashboxes());
    context.read<PaymentBloc>().add(const LoadPayments());
    context.read<ClientBloc>().add(const LoadClients());
  }

  void _checkDataLoaded() {
    _loadedCount++;
    if (_loadedCount >= _totalDataSources) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  List<CashboxModel> get _filteredCashboxes {
    var filtered = _cashboxes.where((cashbox) {
      // فلتر البحث
      if (_searchQuery.isNotEmpty) {
        if (!cashbox.name.toLowerCase().contains(_searchQuery.toLowerCase())) {
          return false;
        }
      }
      
      // فلتر النوع
      if (_selectedCashboxType != 'all') {
        if (cashbox.type != _selectedCashboxType) {
          return false;
        }
      }
      
      return true;
    }).toList();

    // ترتيب
    filtered.sort((a, b) {
      int comparison = 0;
      switch (_sortBy) {
        case 'name':
          comparison = a.name.compareTo(b.name);
          break;
        case 'balance':
          comparison = a.balance.compareTo(b.balance);
          break;
        case 'activity':
          final activityA = _getCashboxActivity(a.id!);
          final activityB = _getCashboxActivity(b.id!);
          comparison = activityA.compareTo(activityB);
          break;
      }
      return _sortAscending ? comparison : -comparison;
    });

    return filtered;
  }

  int _getCashboxActivity(int cashboxId) {
    return _payments.where((payment) => 
      payment.cashboxId == cashboxId &&
      payment.createdAt.isAfter(_startDate) &&
      payment.createdAt.isBefore(_endDate.add(const Duration(days: 1)))
    ).length;
  }

  double _getCashboxTotalIncome(int cashboxId) {
    return _payments.where((payment) => 
      payment.cashboxId == cashboxId &&
      payment.amount > 0 &&
      payment.createdAt.isAfter(_startDate) &&
      payment.createdAt.isBefore(_endDate.add(const Duration(days: 1)))
    ).fold(0.0, (sum, payment) => sum + payment.amount);
  }

  double _getCashboxTotalExpense(int cashboxId) {
    return _payments.where((payment) => 
      payment.cashboxId == cashboxId &&
      payment.amount < 0 &&
      payment.createdAt.isAfter(_startDate) &&
      payment.createdAt.isBefore(_endDate.add(const Duration(days: 1)))
    ).fold(0.0, (sum, payment) => sum + payment.amount.abs());
  }

  List<PaymentModel> _getCashboxTransactions(int cashboxId) {
    return _payments.where((payment) => 
      payment.cashboxId == cashboxId &&
      payment.createdAt.isAfter(_startDate) &&
      payment.createdAt.isBefore(_endDate.add(const Duration(days: 1)))
    ).toList()..sort((a, b) => b.createdAt.compareTo(a.createdAt));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildAppBar(),
      body: MultiBlocListener(
        listeners: _buildBlocListeners(),
        child: _isLoading
            ? _buildLoadingWidget()
            : SingleChildScrollView(
                child: Column(
                  children: [
                    _buildFiltersSection(),
                    _buildSummaryCards(),
                    _buildCashboxesList(),
                    const SizedBox(height: 80), // مساحة إضافية لتجنب تداخل FloatingActionButton
                  ],
                ),
              ),
      ),
      floatingActionButton: _buildFloatingActionButtons(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text(
        'كشف حساب الصناديق',
        style: TextStyle(fontWeight: FontWeight.bold, color: Colors.white),
      ),
      backgroundColor: AppTheme.primaryColor,
      iconTheme: const IconThemeData(color: Colors.white),
      actions: [
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: _loadAllData,
          tooltip: 'تحديث البيانات',
        ),
        IconButton(
          icon: const Icon(Icons.file_download),
          onPressed: _exportToExcel,
          tooltip: 'تصدير إلى Excel',
        ),
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert),
          onSelected: (value) {
            switch (value) {
              case 'print':
                _printReport();
                break;
              case 'share':
                _shareReport();
                break;
              case 'reconcile':
                _reconcileAllCashboxes();
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'print',
              child: Row(
                children: [
                  Icon(Icons.print),
                  SizedBox(width: 8),
                  Text('طباعة التقرير'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'share',
              child: Row(
                children: [
                  Icon(Icons.share),
                  SizedBox(width: 8),
                  Text('مشاركة التقرير'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'reconcile',
              child: Row(
                children: [
                  Icon(Icons.balance),
                  SizedBox(width: 8),
                  Text('تسوية الصناديق'),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  List<BlocListener> _buildBlocListeners() {
    return [
      BlocListener<CashboxBloc, CashboxState>(
        listener: (context, state) {
          if (state is CashboxesLoaded) {
            setState(() => _cashboxes = state.cashboxes);
            _checkDataLoaded();
          }
        },
      ),
      BlocListener<PaymentBloc, PaymentState>(
        listener: (context, state) {
          if (state is PaymentsLoaded) {
            setState(() => _payments = state.payments);
            _checkDataLoaded();
          }
        },
      ),
      BlocListener<ClientBloc, ClientState>(
        listener: (context, state) {
          if (state is ClientsLoaded) {
            setState(() => _clients = state.clients);
            _checkDataLoaded();
          }
        },
      ),
    ];
  }

  Widget _buildLoadingWidget() {
    return Container(
      color: Colors.white,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
          ),
          const SizedBox(height: 16),
          Text(
            'جاري تحميل بيانات الصناديق...',
            style: TextStyle(fontSize: 16, color: Colors.grey.shade600),
          ),
          const SizedBox(height: 8),
          Text(
            'تم تحميل $_loadedCount من $_totalDataSources',
            style: TextStyle(fontSize: 14, color: Colors.grey[500]),
          ),
        ],
      ),
    );
  }

  Widget _buildFiltersSection() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.filter_list, color: AppTheme.primaryColor),
              const SizedBox(width: 8),
              const Text(
                'فلاتر التقرير',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              TextButton(
                onPressed: _resetFilters,
                child: const Text('إعادة تعيين'),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // شريط البحث
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'البحث باسم الصندوق...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        setState(() => _searchQuery = '');
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: AppTheme.primaryColor),
              ),
            ),
            onChanged: (value) {
              setState(() => _searchQuery = value);
            },
          ),
          
          const SizedBox(height: 16),
          
          // فلاتر الفترة الزمنية
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('من تاريخ:', style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 4),
                    InkWell(
                      onTap: () => _selectDate(context, true),
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.calendar_today, size: 16),
                            const SizedBox(width: 8),
                            Text(DateFormat('yyyy-MM-dd').format(_startDate)),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('إلى تاريخ:', style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 4),
                    InkWell(
                      onTap: () => _selectDate(context, false),
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.calendar_today, size: 16),
                            const SizedBox(width: 8),
                            Text(DateFormat('yyyy-MM-dd').format(_endDate)),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // فلاتر إضافية
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('نوع الصندوق:', style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 4),
                    DropdownButtonFormField<String>(
                      value: _selectedCashboxType,
                      decoration: InputDecoration(
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(color: Colors.grey.shade300),
                        ),
                        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      items: const [
                        DropdownMenuItem(value: 'all', child: Text('جميع الأنواع')),
                        DropdownMenuItem(value: 'cash', child: Text('نقدي')),
                        DropdownMenuItem(value: 'diesel', child: Text('ديزل')),
                      ],
                      onChanged: (value) {
                        setState(() => _selectedCashboxType = value!);
                      },
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('ترتيب حسب:', style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 4),
                    DropdownButtonFormField<String>(
                      value: _sortBy,
                      decoration: InputDecoration(
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(color: Colors.grey.shade300),
                        ),
                        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      items: const [
                        DropdownMenuItem(value: 'name', child: Text('الاسم')),
                        DropdownMenuItem(value: 'balance', child: Text('الرصيد')),
                        DropdownMenuItem(value: 'activity', child: Text('النشاط')),
                      ],
                      onChanged: (value) {
                        setState(() => _sortBy = value!);
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCards() {
    final totalCashboxes = _filteredCashboxes.length;
    final totalBalance = _filteredCashboxes.fold(0.0, (sum, cashbox) => sum + cashbox.balance);
    final activeCashboxes = _filteredCashboxes.where((cashbox) => _getCashboxActivity(cashbox.id!) > 0).length;
    final totalTransactions = _filteredCashboxes.fold(0, (sum, cashbox) => sum + _getCashboxActivity(cashbox.id!));

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          Expanded(
            child: _buildSummaryCard(
              'إجمالي الصناديق',
              '$totalCashboxes',
              Icons.account_balance_wallet,
              Colors.blue,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildSummaryCard(
              'إجمالي الرصيد',
              '${totalBalance.toStringAsFixed(2)} ريال',
              Icons.account_balance,
              totalBalance >= 0 ? Colors.green : Colors.red,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildSummaryCard(
              'الصناديق النشطة',
              '$activeCashboxes',
              Icons.trending_up,
              Colors.orange,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildSummaryCard(
              'إجمالي المعاملات',
              '$totalTransactions',
              Icons.receipt_long,
              Colors.purple,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(String title, String value, IconData icon, Color color) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              color.withValues(alpha: 0.1),
              color.withValues(alpha: 0.05),
            ],
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCashboxesList() {
    final filteredCashboxes = _filteredCashboxes;
    
    if (filteredCashboxes.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.account_balance_wallet, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'لا توجد صناديق تطابق المعايير المحددة',
              style: TextStyle(fontSize: 16, color: Colors.grey.shade600),
            ),
            const SizedBox(height: 8),
            TextButton(
              onPressed: _resetFilters,
              child: const Text('إعادة تعيين الفلاتر'),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.all(16),
      itemCount: filteredCashboxes.length,
      itemBuilder: (context, index) {
        final cashbox = filteredCashboxes[index];
        return _buildCashboxCard(cashbox);
      },
    );
  }

  Widget _buildCashboxCard(CashboxModel cashbox) {
    final activity = _getCashboxActivity(cashbox.id!);
    final totalIncome = _getCashboxTotalIncome(cashbox.id!);
    final totalExpense = _getCashboxTotalExpense(cashbox.id!);
    final transactions = _getCashboxTransactions(cashbox.id!);

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: ExpansionTile(
        leading: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: _getCashboxTypeColor(cashbox.type).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            _getCashboxTypeIcon(cashbox.type),
            color: _getCashboxTypeColor(cashbox.type),
            size: 24,
          ),
        ),
        title: Text(
          cashbox.name,
          style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(Icons.account_balance, size: 16, color: Colors.grey.shade600),
                const SizedBox(width: 4),
                Text(
                  'الرصيد: ${cashbox.balance.toStringAsFixed(2)} ريال',
                  style: TextStyle(
                    color: _getBalanceColor(cashbox.balance),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 2),
            Row(
              children: [
                Icon(Icons.trending_up, size: 16, color: Colors.grey.shade600),
                const SizedBox(width: 4),
                Text(
                  'النشاط: $activity معاملة',
                  style: TextStyle(color: Colors.grey.shade600, fontSize: 12),
                ),
              ],
            ),
          ],
        ),
        trailing: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: _getCashboxTypeColor(cashbox.type).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            _getCashboxTypeText(cashbox.type),
            style: TextStyle(
              color: _getCashboxTypeColor(cashbox.type),
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // إحصائيات الفترة
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey[50],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'إحصائيات الفترة (${DateFormat('yyyy-MM-dd').format(_startDate)} - ${DateFormat('yyyy-MM-dd').format(_endDate)})',
                        style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                      ),
                      const SizedBox(height: 12),
                      Row(
                        children: [
                          Expanded(
                            child: _buildStatItem('الإيرادات', '${totalIncome.toStringAsFixed(2)} ريال', Colors.green),
                          ),
                          Expanded(
                            child: _buildStatItem('المصروفات', '${totalExpense.toStringAsFixed(2)} ريال', Colors.red),
                          ),
                          Expanded(
                            child: _buildStatItem('المعاملات', '$activity', Colors.blue),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // المعاملات الأخيرة
                if (transactions.isNotEmpty) ...[
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'المعاملات الأخيرة',
                        style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                      ),
                      TextButton(
                        onPressed: () => _showAllTransactions(cashbox, transactions),
                        child: const Text('عرض الكل'),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  ...transactions.take(3).map((transaction) => _buildTransactionItem(transaction)),
                ] else
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.info_outline, color: Colors.grey.shade600),
                        const SizedBox(width: 8),
                        Text(
                          'لا توجد معاملات في الفترة المحددة',
                          style: TextStyle(color: Colors.grey.shade600),
                        ),
                      ],
                    ),
                  ),
                
                const SizedBox(height: 16),
                
                // أزرار الإجراءات
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () => _addTransaction(cashbox),
                        icon: const Icon(Icons.add),
                        label: const Text('إضافة معاملة'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.primaryColor,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () => _reconcileCashbox(cashbox),
                        icon: const Icon(Icons.balance),
                        label: const Text('تسوية'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: AppTheme.primaryColor,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
          ),
        ),
      ],
    );
  }

  Widget _buildTransactionItem(PaymentModel transaction) {
    final client = _clients.firstWhere(
      (c) => c.id == transaction.clientId,
      orElse: () => ClientModel(
        id: 0,
        name: 'غير محدد',
        phone: '',
        address: '',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    );

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: transaction.amount >= 0 ? Colors.green.withValues(alpha: 0.1) : Colors.red.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              transaction.amount >= 0 ? Icons.arrow_downward : Icons.arrow_upward,
              color: transaction.amount >= 0 ? Colors.green : Colors.red,
              size: 16,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  transaction.notes ?? 'معاملة ${transaction.type}',
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
                const SizedBox(height: 2),
                Text(
                  'العميل: ${client.name}',
                  style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                ),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                '${transaction.amount.abs().toStringAsFixed(2)} ريال',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: transaction.amount >= 0 ? Colors.green : Colors.red,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                DateFormat('MM-dd HH:mm').format(transaction.createdAt),
                style: TextStyle(fontSize: 10, color: Colors.grey[500]),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingActionButtons() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        FloatingActionButton(
          heroTag: "export",
          onPressed: _exportToExcel,
          backgroundColor: Colors.green,
          tooltip: 'تصدير إلى Excel',
          child: const Icon(Icons.file_download, color: Colors.white),
        ),
        const SizedBox(height: 8),
        FloatingActionButton(
          heroTag: "refresh",
          onPressed: _loadAllData,
          backgroundColor: AppTheme.primaryColor,
          tooltip: 'تحديث البيانات',
          child: const Icon(Icons.refresh, color: Colors.white),
        ),
      ],
    );
  }

  Color _getBalanceColor(double balance) {
    if (balance > 0) return Colors.green;
    if (balance < 0) return Colors.red;
    return Colors.grey;
  }

  Color _getCashboxTypeColor(String type) {
    switch (type) {
      case 'cash':
        return Colors.green;
      case 'diesel':
        return Colors.orange;
      default:
        return Colors.blue;
    }
  }

  IconData _getCashboxTypeIcon(String type) {
    switch (type) {
      case 'cash':
        return Icons.attach_money;
      case 'diesel':
        return Icons.local_gas_station;
      default:
        return Icons.account_balance_wallet;
    }
  }

  String _getCashboxTypeText(String type) {
    switch (type) {
      case 'cash':
        return 'نقدي';
      case 'diesel':
        return 'ديزل';
      default:
        return 'عام';
    }
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final date = await showDatePicker(
      context: context,
      initialDate: isStartDate ? _startDate : _endDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    
    if (date != null) {
      setState(() {
        if (isStartDate) {
          _startDate = date;
          if (_endDate.isBefore(_startDate)) {
            _endDate = _startDate;
          }
        } else {
          _endDate = date;
          if (_startDate.isAfter(_endDate)) {
            _startDate = _endDate;
          }
        }
      });
    }
  }

  void _resetFilters() {
    setState(() {
      _startDate = DateTime.now().subtract(const Duration(days: 30));
      _endDate = DateTime.now();
      _selectedCashboxType = 'all';
      _sortBy = 'name';
      _sortAscending = true;
      _searchQuery = '';
      _searchController.clear();
    });
  }

  void _exportToExcel() {
    // تنفيذ تصدير Excel
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تصدير التقرير...')),
    );
  }

  void _printReport() {
    // تنفيذ طباعة التقرير
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تحضير التقرير للطباعة...')),
    );
  }

  void _shareReport() {
    // تنفيذ مشاركة التقرير
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('جاري تحضير التقرير للمشاركة...')),
    );
  }

  void _reconcileAllCashboxes() {
    // تنفيذ تسوية جميع الصناديق
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسوية الصناديق'),
        content: const Text('هل تريد تسوية جميع الصناديق؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('تم تسوية الصناديق بنجاح')),
              );
            },
            child: const Text('تأكيد'),
          ),
        ],
      ),
    );
  }

  void _showAllTransactions(CashboxModel cashbox, List<PaymentModel> transactions) {
    // عرض جميع المعاملات في صفحة منفصلة
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => Scaffold(
          appBar: AppBar(
            title: Text('معاملات ${cashbox.name}'),
            backgroundColor: AppTheme.primaryColor,
            foregroundColor: Colors.white,
          ),
          body: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: transactions.length,
            itemBuilder: (context, index) => _buildTransactionItem(transactions[index]),
          ),
        ),
      ),
    );
  }

  void _addTransaction(CashboxModel cashbox) {
    // إضافة معاملة جديدة
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('إضافة معاملة لصندوق ${cashbox.name}')),
    );
  }

  void _reconcileCashbox(CashboxModel cashbox) {
    // تسوية صندوق محدد
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تسوية ${cashbox.name}'),
        content: const Text('هل تريد تسوية هذا الصندوق؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('تم تسوية ${cashbox.name} بنجاح')),
              );
            },
            child: const Text('تأكيد'),
          ),
        ],
      ),
    );
  }
}

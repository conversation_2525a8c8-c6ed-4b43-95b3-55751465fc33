import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:untitled/core/theme/app_theme.dart';
import 'package:untitled/data/models/client_model.dart';
import 'package:untitled/data/models/client_account_model.dart';
import 'package:untitled/presentation/blocs/client/client_bloc.dart';
import 'package:untitled/presentation/blocs/client/client_event.dart';
import 'package:untitled/presentation/blocs/client/client_state.dart';
import 'package:untitled/presentation/blocs/client_account/client_account_bloc.dart';
import 'package:untitled/presentation/blocs/client_account/client_account_event.dart';
import 'package:untitled/presentation/blocs/client_account/client_account_state.dart';
import 'package:untitled/presentation/widgets/prompt_helper.dart';
import 'package:untitled/presentation/pages/client/client_transactions_page.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:pdf/pdf.dart';
import 'package:untitled/data/datasources/irrigation_datasource.dart';
import 'package:untitled/data/datasources/payment_datasource.dart';
import 'package:untitled/data/datasources/client_transfer_datasource.dart';
import 'package:untitled/data/models/irrigation_model.dart';
import 'package:untitled/data/models/payment_model.dart';
import 'package:untitled/data/models/client_transfer_model.dart';

class ClientsListPage extends StatefulWidget {
  const ClientsListPage({super.key});

  @override
  State<ClientsListPage> createState() => _ClientsListPageState();
}

class _ClientsListPageState extends State<ClientsListPage> {
  List<ClientModel> _clients = [];
  List<ClientAccountModel> _accounts = [];
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  void _loadData() {
    context.read<ClientBloc>().add(const LoadClients());
    context.read<ClientAccountBloc>().add(const LoadAllClientAccounts());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('العملاء'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: const [
          PromptHelper(
            category: 'client_management',
            promptKey: 'clients_list',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث
          _buildSearchBar(),

          // قائمة العملاء
          Expanded(
            child: MultiBlocListener(
              listeners: [
                BlocListener<ClientBloc, ClientState>(
                  listener: (context, state) {
                    if (state is ClientsLoaded) {
                      setState(() {
                        _clients = state.clients;
                      });
                    } else if (state is ClientOperationSuccess) {
                      // إعادة تحميل العملاء بعد إضافة/تعديل/حذف عميل
                      _loadData();
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(state.message),
                          backgroundColor: Colors.green,
                        ),
                      );
                    }
                  },
                ),
                BlocListener<ClientAccountBloc, ClientAccountState>(
                  listener: (context, state) {
                    if (state is AllClientAccountsLoaded) {
                      setState(() {
                        _accounts = state.accounts;
                      });
                    }
                  },
                ),
              ],
              child: BlocBuilder<ClientBloc, ClientState>(
                builder: (context, state) {
                  if (state is ClientLoading) {
                    return const Center(child: CircularProgressIndicator());
                  }

                  if (state is ClientError) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(Icons.error, size: 64, color: Colors.red),
                          const SizedBox(height: 16),
                          Text(state.message),
                          const SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: _loadData,
                            child: const Text('إعادة المحاولة'),
                          ),
                        ],
                      ),
                    );
                  }

                  final filteredClients = _getFilteredClients();

                  if (filteredClients.isEmpty) {
                    return const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.people_outline,
                              size: 64, color: Colors.grey),
                          SizedBox(height: 16),
                          Text('لا توجد عملاء'),
                        ],
                      ),
                    );
                  }

                  return ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: filteredClients.length,
                    itemBuilder: (context, index) {
                      final client = filteredClients[index];
                      final account = _getClientAccount(client.id!);
                      final clientNumber = index + 1; // ترقيم العملاء
                      return _buildClientCard(client, account, clientNumber);
                    },
                  );
                },
              ),
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.pushNamed(context, '/add-client').then((_) => _loadData());
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: TextField(
        decoration: const InputDecoration(
          labelText: 'البحث عن عميل',
          prefixIcon: Icon(Icons.search),
          border: OutlineInputBorder(),
        ),
        onChanged: (value) {
          setState(() {
            _searchQuery = value;
          });
        },
      ),
    );
  }

  List<ClientModel> _getFilteredClients() {
    if (_searchQuery.isEmpty) {
      return _clients;
    }
    return _clients.where((client) {
      return client.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          (client.phone?.contains(_searchQuery) ?? false) ||
          (client.address?.toLowerCase().contains(_searchQuery.toLowerCase()) ??
              false);
    }).toList();
  }

  ClientAccountModel? _getClientAccount(int clientId) {
    try {
      return _accounts.firstWhere((account) => account.clientId == clientId);
    } catch (e) {
      return null;
    }
  }

  Widget _buildClientCard(
      ClientModel client, ClientAccountModel? account, int clientNumber) {
    return Card(
      elevation: 4,
      margin: const EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () {
          Navigator.pushNamed(
            context,
            '/client-details',
            arguments: client.id,
          ).then((_) => _loadData());
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // معلومات العميل مع الترقيم
              Row(
                children: [
                  // رقم العميل
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: const BoxDecoration(
                      color: AppTheme.primaryColor,
                      shape: BoxShape.circle,
                    ),
                    child: Text(
                      '$clientNumber',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  // أيقونة العميل
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor.withValues(alpha: 0.1),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.person,
                      color: AppTheme.primaryColor,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Text(
                              'العميل رقم $clientNumber: ',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey.shade600,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            Expanded(
                              child: Text(
                                client.name,
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ),
                        if (client.phone != null)
                          Text(
                            client.phone!,
                            style: const TextStyle(
                              color: Colors.grey,
                              fontSize: 14,
                            ),
                          ),
                      ],
                    ),
                  ),
                ],
              ),

              if (client.address != null) ...[
                const SizedBox(height: 8),
                Row(
                  children: [
                    const Icon(Icons.location_on, size: 16, color: Colors.grey),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        client.address!,
                        style:
                            const TextStyle(color: Colors.grey, fontSize: 14),
                      ),
                    ),
                  ],
                ),
              ],

              const SizedBox(height: 16),
              const Divider(),
              const SizedBox(height: 8),

              // أرصدة العميل
              if (account != null) ...[
                const Text(
                  'أرصدة العميل:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: _buildBalanceItem(
                        'الرصيد النقدي',
                        '${account.cashBalance.toStringAsFixed(2)} ريال',
                        Icons.attach_money,
                        Colors.green,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: _buildBalanceItem(
                        'رصيد الديزل',
                        '${account.dieselBalance.toStringAsFixed(2)} لتر',
                        Icons.local_gas_station,
                        Colors.orange,
                      ),
                    ),
                  ],
                ),
              ] else ...[
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.orange.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Row(
                    children: [
                      Icon(Icons.warning, color: Colors.orange),
                      SizedBox(width: 8),
                      Text('لم يتم إنشاء حساب لهذا العميل بعد'),
                    ],
                  ),
                ),
              ],

              // أزرار العمليات
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  ElevatedButton.icon(
                    icon: const Icon(Icons.list_alt),
                    label: const Text('عرض المعاملات'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                      foregroundColor: Colors.white,
                    ),
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (_) => ClientTransactionsPage(
                            clientId: client.id,
                            clientName: client.name,
                          ),
                        ),
                      );
                    },
                  ),
                  const SizedBox(width: 12),
                  ElevatedButton.icon(
                    icon: const Icon(Icons.picture_as_pdf),
                    label: const Text('طباعة كشف الحساب'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.redAccent,
                      foregroundColor: Colors.white,
                    ),
                    onPressed: () async {
                      await _exportClientStatementPdf(context, client);
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBalanceItem(
      String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 16, color: color),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 12,
                    color: color,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _exportClientStatementPdf(
      BuildContext context, ClientModel client) async {
    // جلب جميع العمليات للعميل
    final irrigationDataSource = IrrigationDataSource();
    final paymentDataSource = PaymentDataSource();
    final transferDataSource = ClientTransferDataSource();
    final clientId = client.id!;
    final clientName = client.name;
    // جلب البيانات
    final payments = await paymentDataSource.getPaymentsByClientId(clientId);
    final irrigations =
        await irrigationDataSource.getIrrigationsByClientId(clientId);
    final transfers = await transferDataSource.getTransfersByClientId(clientId);
    // بناء قائمة العمليات بنفس منطق صفحة المعاملات
    double cash = 0;
    double diesel = 0;
    final List<TransactionViewModel> txs = [];
    // مدفوعات
    for (final p in payments) {
      final isCash = p.type == 'cash';
      final isDiesel = p.type == 'diesel';
      final cashChange = isCash ? p.amount : 0.0;
      final dieselChange = isDiesel ? p.amount : 0.0;
      String desc = isCash ? 'دفعة نقدية من الصندوق' : 'دفعة ديزل من الصندوق';
      if ((p.notes ?? '').isNotEmpty) desc += ' (${p.notes})';
      txs.add(TransactionViewModel(
        type: 'دفعة',
        date: p.createdAt,
        cashChange: cashChange,
        dieselChange: dieselChange,
        description: desc,
        cashBefore: cash,
        dieselBefore: diesel,
        cashAfter: cash + cashChange,
        dieselAfter: diesel + dieselChange,
      ));
      cash += cashChange;
      diesel += dieselChange;
    }
    // تسقيات
    for (final i in irrigations) {
      txs.add(TransactionViewModel(
        type: 'تسقية',
        date: i.startTime,
        cashChange: -i.cost,
        dieselChange: -i.dieselConsumption,
        description: 'تسقية مزرعة',
        cashBefore: cash,
        dieselBefore: diesel,
        cashAfter: cash - i.cost,
        dieselAfter: diesel - i.dieselConsumption,
      ));
      cash -= i.cost;
      diesel -= i.dieselConsumption;
    }
    // تحويلات
    for (final t in transfers) {
      if (t.fromClientId == clientId) {
        String desc =
            t.toClientId == -1 ? 'تحويل إلى الصندوق' : 'تحويل إلى عميل';
        txs.add(TransactionViewModel(
          type: 'تحويل مرسل',
          date: t.createdAt,
          cashChange: -t.cashAmount,
          dieselChange: -t.dieselAmount,
          description: desc,
          cashBefore: cash,
          dieselBefore: diesel,
          cashAfter: cash - t.cashAmount,
          dieselAfter: diesel - t.dieselAmount,
        ));
        cash -= t.cashAmount;
        diesel -= t.dieselAmount;
      } else if (t.toClientId == clientId) {
        String desc =
            t.fromClientId == -1 ? 'تحويل من الصندوق' : 'تحويل من عميل';
        txs.add(TransactionViewModel(
          type: 'تحويل مستقبل',
          date: t.createdAt,
          cashChange: t.cashAmount,
          dieselChange: t.dieselAmount,
          description: desc,
          cashBefore: cash,
          dieselBefore: diesel,
          cashAfter: cash + t.cashAmount,
          dieselAfter: diesel + t.dieselAmount,
        ));
        cash += t.cashAmount;
        diesel += t.dieselAmount;
      }
    }
    txs.sort((a, b) => a.date.compareTo(b.date));
    // إعادة احتساب الرصيد قبل وبعد لكل عملية
    double runningCash = 0;
    double runningDiesel = 0;
    for (int i = 0; i < txs.length; i++) {
      final tx = txs[i];
      final beforeCash = runningCash;
      final beforeDiesel = runningDiesel;
      runningCash += tx.cashChange;
      runningDiesel += tx.dieselChange;
      txs[i] = TransactionViewModel(
        type: tx.type,
        date: tx.date,
        cashChange: tx.cashChange,
        dieselChange: tx.dieselChange,
        description: tx.description,
        cashBefore: beforeCash,
        dieselBefore: beforeDiesel,
        cashAfter: runningCash,
        dieselAfter: runningDiesel,
      );
    }
    // توليد PDF
    final totalCash = txs.fold<double>(0, (sum, tx) => sum + tx.cashChange);
    final totalDiesel = txs.fold<double>(0, (sum, tx) => sum + tx.dieselChange);
    final font = await PdfGoogleFonts.cairoRegular();
    final boldFont = await PdfGoogleFonts.cairoBold();
    final pdf = pw.Document();
    pdf.addPage(
      pw.MultiPage(
        theme: pw.ThemeData.withFont(
          base: font,
          bold: boldFont,
        ),
        build: (context) => [
          pw.Header(
            level: 0,
            child: pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                pw.Text('كشف حساب عميل',
                    style: pw.TextStyle(
                        fontSize: 22, fontWeight: pw.FontWeight.bold)),
                pw.Text(
                    'تاريخ الطباعة: ${DateTime.now().toString().split(' ')[0]}',
                    style: pw.TextStyle(fontSize: 12)),
              ],
            ),
          ),
          pw.SizedBox(height: 8),
          pw.Text('اسم العميل: $clientName', style: pw.TextStyle(fontSize: 16)),
          pw.SizedBox(height: 4),
          pw.Text('عدد العمليات: ${txs.length}',
              style: pw.TextStyle(fontSize: 12)),
          pw.SizedBox(height: 12),
          pw.Table.fromTextArray(
            cellAlignment: pw.Alignment.center,
            headerStyle:
                pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 12),
            cellStyle: const pw.TextStyle(fontSize: 11),
            headerDecoration:
                pw.BoxDecoration(color: PdfColor.fromInt(0xFFE0E0E0)),
            rowDecoration: pw.BoxDecoration(),
            headers: [
              'التاريخ',
              'النوع',
              'الوصف',
              'نقدي',
              'ديزل',
              'رصيد نقدي',
              'رصيد ديزل',
            ],
            data: [
              ...txs.map((tx) => [
                    tx.date.toString().split(' ')[0],
                    tx.type,
                    _getSimpleDescription(tx.description),
                    (tx.cashChange > 0 ? '+' : '') +
                        tx.cashChange.toStringAsFixed(2),
                    (tx.dieselChange > 0 ? '+' : '') +
                        tx.dieselChange.toStringAsFixed(2),
                    tx.cashAfter.toStringAsFixed(2),
                    tx.dieselAfter.toStringAsFixed(2),
                  ]),
              // صف الإجمالي
              [
                '',
                '',
                'الإجمالي',
                totalCash.toStringAsFixed(2),
                totalDiesel.toStringAsFixed(2),
                '',
                '',
              ]
            ],
            cellAlignments: {
              0: pw.Alignment.center,
              1: pw.Alignment.center,
              2: pw.Alignment.centerRight,
              3: pw.Alignment.center,
              4: pw.Alignment.center,
              5: pw.Alignment.center,
              6: pw.Alignment.center,
            },
          ),
          pw.SizedBox(height: 16),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.end,
            children: [
              pw.Text('توقيع الإدارة: _______________',
                  style: pw.TextStyle(fontSize: 13)),
            ],
          ),
        ],
      ),
    );
    await Printing.layoutPdf(
      onLayout: (PdfPageFormat format) async => pdf.save(),
    );
  }

  String _getSimpleDescription(String desc) {
    if (desc.contains('دفعة نقدية')) return 'دفعة نقدية من الصندوق';
    if (desc.contains('دفعة ديزل')) return 'دفعة ديزل من الصندوق';
    if (desc.contains('مقابل تسقية')) return 'تسقية مزرعة';
    if (desc.contains('تحويل إلى عميل')) return 'تحويل إلى عميل';
    if (desc.contains('تحويل من عميل')) return 'تحويل من عميل';
    if (desc.contains('تحويل إلى الصندوق')) return 'تحويل إلى الصندوق';
    if (desc.contains('تحويل من الصندوق')) return 'تحويل من الصندوق';
    return desc.length > 30 ? desc.substring(0, 30) + '...' : desc;
  }
}

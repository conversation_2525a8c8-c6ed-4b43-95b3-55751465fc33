import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:untitled/data/models/irrigation_model.dart';
import 'package:untitled/data/models/client_model.dart';
import 'package:untitled/data/models/farm_model.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_bloc.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_event.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_state.dart';
import 'package:untitled/presentation/blocs/client/client_bloc.dart';
import 'package:untitled/presentation/blocs/client/client_event.dart';
import 'package:untitled/presentation/blocs/client/client_state.dart';
import 'package:untitled/presentation/blocs/farm/farm_bloc.dart';
import 'package:untitled/presentation/blocs/farm/farm_event.dart';
import 'package:untitled/presentation/blocs/farm/farm_state.dart';
import 'package:untitled/presentation/widgets/irrigation_list_item.dart';
import 'package:untitled/core/theme/app_theme.dart';
import 'package:untitled/services/pdf_service.dart';
import 'package:open_file/open_file.dart';
import 'package:untitled/data/datasources/client_datasource.dart';
import 'package:untitled/data/datasources/farm_datasource.dart';

/// صفحة تقرير التسقيات المتقدمة
class IrrigationReportsPage extends StatefulWidget {
  const IrrigationReportsPage({super.key});

  @override
  State<IrrigationReportsPage> createState() => _IrrigationReportsPageState();
}

class _IrrigationReportsPageState extends State<IrrigationReportsPage> {
  List<IrrigationModel> _irrigations = [];
  List<ClientModel> _clients = [];
  List<FarmModel> _farms = [];

  bool _isLoading = false;
  int _loadedCount = 0;
  final int _totalDataSources = 3;

  // فلاتر
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();
  String _selectedClient = 'all';
  String _selectedFarm = 'all';
  String _selectedStatus = 'all'; // all, completed, in_progress, cancelled
  String _sortBy = 'date'; // date, cost, duration, diesel
  bool _sortAscending = false;

  // بحث
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  bool _isExporting = false;

  @override
  void initState() {
    super.initState();
    _loadAllData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _loadAllData() {
    setState(() {
      _isLoading = true;
      _loadedCount = 0;
    });

    context.read<IrrigationBloc>().add(const LoadIrrigations());
    context.read<ClientBloc>().add(const LoadClients());
    context.read<FarmBloc>().add(const LoadFarms());
  }

  void _checkDataLoaded() {
    _loadedCount++;
    if (_loadedCount >= _totalDataSources) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  List<IrrigationModel> get _filteredIrrigations {
    var filtered = _irrigations.where((irrigation) {
      // فلتر التاريخ
      final startOfDay =
          DateTime(_startDate.year, _startDate.month, _startDate.day);
      final endOfDay =
          DateTime(_endDate.year, _endDate.month, _endDate.day, 23, 59, 59);

      if (irrigation.startTime.isBefore(startOfDay) ||
          irrigation.startTime.isAfter(endOfDay)) {
        return false;
      }

      // فلتر البحث
      if (_searchQuery.isNotEmpty) {
        final client = _getClientById(irrigation.clientId);
        final farm = _getFarmById(irrigation.farmId);
        if (!(client?.name.toLowerCase().contains(_searchQuery.toLowerCase()) ??
                false) &&
            !(farm?.name.toLowerCase().contains(_searchQuery.toLowerCase()) ??
                false)) {
          return false;
        }
      }

      // فلتر العميل
      if (_selectedClient != 'all' &&
          irrigation.clientId.toString() != _selectedClient) {
        return false;
      }

      // فلتر المزرعة
      if (_selectedFarm != 'all' &&
          irrigation.farmId.toString() != _selectedFarm) {
        return false;
      }

      // فلتر الحالة
      if (_selectedStatus != 'all') {
        final status = _getIrrigationStatus(irrigation);
        if (status != _selectedStatus) {
          return false;
        }
      }

      return true;
    }).toList();

    // ترتيب
    filtered.sort((a, b) {
      int comparison = 0;
      switch (_sortBy) {
        case 'date':
          comparison = a.startTime.compareTo(b.startTime);
          break;
        case 'cost':
          comparison = a.cost.compareTo(b.cost);
          break;
        case 'duration':
          comparison = a.duration.compareTo(b.duration);
          break;
        case 'diesel':
          comparison = a.dieselConsumption.compareTo(b.dieselConsumption);
          break;
      }
      return _sortAscending ? comparison : -comparison;
    });

    return filtered;
  }

  ClientModel? _getClientById(int clientId) {
    try {
      return _clients.firstWhere((client) => client.id == clientId);
    } catch (e) {
      return null;
    }
  }

  FarmModel? _getFarmById(int farmId) {
    try {
      return _farms.firstWhere((farm) => farm.id == farmId);
    } catch (e) {
      return null;
    }
  }

  String _getIrrigationStatus(IrrigationModel irrigation) {
    final now = DateTime.now();
    final endTime =
        irrigation.startTime.add(Duration(minutes: irrigation.duration));

    if (endTime.isBefore(now)) {
      return 'completed';
    } else if (irrigation.startTime.isBefore(now) && endTime.isAfter(now)) {
      return 'in_progress';
    } else {
      return 'scheduled';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildAppBar(),
      body: MultiBlocListener(
        listeners: _buildBlocListeners(),
        child: _isLoading
            ? _buildLoadingWidget()
            : SingleChildScrollView(
                child: Column(
                  children: [
                    _buildFiltersSection(),
                    _buildIrrigationSummarySection(),
                    _buildIrrigationTable(),
                    const SizedBox(height: 80),
                  ],
                ),
              ),
      ),
      floatingActionButton: _buildFloatingActionButtons(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text(
        'تقرير التسقيات',
        style: TextStyle(fontWeight: FontWeight.bold, color: Colors.white),
      ),
      backgroundColor: AppTheme.primaryColor,
      iconTheme: const IconThemeData(color: Colors.white),
      actions: [
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: _loadAllData,
          tooltip: 'تحديث البيانات',
        ),
        IconButton(
          icon: const Icon(Icons.picture_as_pdf),
          onPressed: _isExporting ? null : _exportToPdf,
          tooltip: 'تصدير PDF',
        ),
        IconButton(
          icon: const Icon(Icons.file_download),
          onPressed: _exportToExcel,
          tooltip: 'تصدير إلى Excel',
        ),
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert),
          onSelected: (value) {
            switch (value) {
              case 'print':
                _printReport();
                break;
              case 'share':
                _shareReport();
                break;
              case 'analytics':
                _showAnalytics();
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'print',
              child: Row(
                children: [
                  Icon(Icons.print),
                  SizedBox(width: 8),
                  Text('طباعة التقرير'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'share',
              child: Row(
                children: [
                  Icon(Icons.share),
                  SizedBox(width: 8),
                  Text('مشاركة التقرير'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'analytics',
              child: Row(
                children: [
                  Icon(Icons.analytics),
                  SizedBox(width: 8),
                  Text('تحليلات متقدمة'),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  List<BlocListener> _buildBlocListeners() {
    return [
      BlocListener<IrrigationBloc, IrrigationState>(
        listener: (context, state) {
          if (state is IrrigationsLoaded) {
            setState(() => _irrigations = state.irrigations);
            _checkDataLoaded();
          }
        },
      ),
      BlocListener<ClientBloc, ClientState>(
        listener: (context, state) {
          if (state is ClientsLoaded) {
            setState(() => _clients = state.clients);
            _checkDataLoaded();
          }
        },
      ),
      BlocListener<FarmBloc, FarmState>(
        listener: (context, state) {
          if (state is FarmsLoaded) {
            setState(() => _farms = state.farms);
            _checkDataLoaded();
          }
        },
      ),
    ];
  }

  Widget _buildLoadingWidget() {
    return Container(
      color: Colors.white,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
          ),
          const SizedBox(height: 16),
          Text(
            'جاري تحميل بيانات التسقيات...',
            style: TextStyle(fontSize: 16, color: Colors.grey.shade600),
          ),
          const SizedBox(height: 8),
          Text(
            'تم تحميل $_loadedCount من $_totalDataSources',
            style: TextStyle(fontSize: 14, color: Colors.grey[500]),
          ),
        ],
      ),
    );
  }

  Widget _buildFiltersSection() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.filter_list, color: AppTheme.primaryColor),
              const SizedBox(width: 8),
              const Text(
                'فلاتر التقرير',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              TextButton(
                onPressed: _resetFilters,
                child: const Text('إعادة تعيين'),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // شريط البحث
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'البحث بالعميل أو المزرعة...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        setState(() => _searchQuery = '');
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: AppTheme.primaryColor),
              ),
            ),
            onChanged: (value) {
              setState(() => _searchQuery = value);
            },
          ),

          const SizedBox(height: 16),

          // فلاتر الفترة الزمنية
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('من تاريخ:',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 4),
                    InkWell(
                      onTap: () => _selectDate(context, true),
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.calendar_today, size: 16),
                            const SizedBox(width: 8),
                            Text(DateFormat('yyyy-MM-dd').format(_startDate)),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('إلى تاريخ:',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 4),
                    InkWell(
                      onTap: () => _selectDate(context, false),
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.calendar_today, size: 16),
                            const SizedBox(width: 8),
                            Text(DateFormat('yyyy-MM-dd').format(_endDate)),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // فلاتر العميل والمزرعة
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('العميل:',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 4),
                    DropdownButtonFormField<String>(
                      value: _selectedClient,
                      decoration: InputDecoration(
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 8),
                      ),
                      items: [
                        const DropdownMenuItem(
                            value: 'all', child: Text('جميع العملاء')),
                        ..._clients.map((client) => DropdownMenuItem(
                              value: client.id.toString(),
                              child: Text(client.name),
                            )),
                      ],
                      onChanged: (value) {
                        setState(() => _selectedClient = value!);
                      },
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('المزرعة:',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 4),
                    DropdownButtonFormField<String>(
                      value: _selectedFarm,
                      decoration: InputDecoration(
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 8),
                      ),
                      items: [
                        const DropdownMenuItem(
                            value: 'all', child: Text('جميع المزارع')),
                        ..._farms.map((farm) => DropdownMenuItem(
                              value: farm.id.toString(),
                              child: Text(farm.name),
                            )),
                      ],
                      onChanged: (value) {
                        setState(() => _selectedFarm = value!);
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // فلاتر الحالة والترتيب
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('الحالة:',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 4),
                    DropdownButtonFormField<String>(
                      value: _selectedStatus,
                      decoration: InputDecoration(
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 8),
                      ),
                      items: const [
                        DropdownMenuItem(
                            value: 'all', child: Text('جميع الحالات')),
                        DropdownMenuItem(
                            value: 'completed', child: Text('مكتملة')),
                        DropdownMenuItem(
                            value: 'in_progress', child: Text('جارية')),
                        DropdownMenuItem(
                            value: 'scheduled', child: Text('مجدولة')),
                      ],
                      onChanged: (value) {
                        setState(() => _selectedStatus = value!);
                      },
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('ترتيب حسب:',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Expanded(
                          child: DropdownButtonFormField<String>(
                            value: _sortBy,
                            decoration: InputDecoration(
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 8),
                            ),
                            items: const [
                              DropdownMenuItem(
                                  value: 'date', child: Text('التاريخ')),
                              DropdownMenuItem(
                                  value: 'cost', child: Text('التكلفة')),
                              DropdownMenuItem(
                                  value: 'duration', child: Text('المدة')),
                              DropdownMenuItem(
                                  value: 'diesel', child: Text('الديزل')),
                            ],
                            onChanged: (value) {
                              setState(() => _sortBy = value!);
                            },
                          ),
                        ),
                        const SizedBox(width: 8),
                        IconButton(
                          icon: Icon(
                            _sortAscending
                                ? Icons.arrow_upward
                                : Icons.arrow_downward,
                            color: AppTheme.primaryColor,
                          ),
                          onPressed: () {
                            setState(() => _sortAscending = !_sortAscending);
                          },
                          tooltip: _sortAscending ? 'تصاعدي' : 'تنازلي',
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildIrrigationSummarySection() {
    final irrigations = _filteredIrrigations;
    final totalCount = irrigations.length;
    final totalMinutes =
        irrigations.fold<int>(0, (sum, i) => sum + (i.duration));
    final totalHours = totalMinutes / 60.0;
    final totalCash = irrigations.fold<double>(0, (sum, i) => sum + (i.cost));
    final totalDiesel =
        irrigations.fold<double>(0, (sum, i) => sum + (i.dieselConsumption));
    final avgCost = totalCount > 0 ? totalCash / totalCount : 0;
    final avgDuration = totalCount > 0 ? totalMinutes / totalCount : 0;
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildSummaryItem('عدد التسقيات', totalCount.toString()),
            _buildSummaryItem('إجمالي الساعات', totalHours.toStringAsFixed(1)),
            _buildSummaryItem(
                'إجمالي التكلفة (ريال)', totalCash.toStringAsFixed(2)),
            _buildSummaryItem(
                'إجمالي الديزل (لتر)', totalDiesel.toStringAsFixed(2)),
            _buildSummaryItem('متوسط التكلفة', avgCost.toStringAsFixed(2)),
            _buildSummaryItem(
                'متوسط المدة (دقيقة)', avgDuration.toStringAsFixed(1)),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(String label, String value) {
    return Column(
      children: [
        Text(label, style: const TextStyle(fontSize: 12, color: Colors.indigo)),
        const SizedBox(height: 2),
        Text(value,
            style: const TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.bold,
                color: Colors.indigo)),
      ],
    );
  }

  Widget _buildIrrigationTable() {
    final irrigations = _filteredIrrigations;
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: DataTable(
          columns: const [
            DataColumn(label: Text('اسم العميل')),
            DataColumn(label: Text('اسم المزرعة')),
            DataColumn(label: Text('التاريخ')),
            DataColumn(label: Text('المدة (س)')),
            DataColumn(label: Text('نوع الدفع')),
            DataColumn(label: Text('التكلفة (ريال)')),
            DataColumn(label: Text('الديزل (لتر)')),
            DataColumn(label: Text('ملاحظات')),
          ],
          rows: irrigations.map((irr) {
            final client = _clients.firstWhere((c) => c.id == irr.clientId,
                orElse: () => ClientModel(
                    id: irr.clientId,
                    name: 'غير محدد',
                    phone: '',
                    address: '',
                    createdAt: DateTime.now(),
                    updatedAt: DateTime.now()));
            final farm = _farms.firstWhere((f) => f.id == irr.farmId,
                orElse: () => FarmModel(
                    id: irr.farmId,
                    name: 'غير محددة',
                    location: '',
                    area: 0.0,
                    clientId: irr.clientId,
                    createdAt: DateTime.now(),
                    updatedAt: DateTime.now()));
            return DataRow(cells: [
              DataCell(Text(client.name)),
              DataCell(Text(farm.name)),
              DataCell(Text(DateFormat('dd-MM-yyyy').format(irr.startTime))),
              DataCell(Text((irr.duration / 60).toStringAsFixed(2))),
              DataCell(Text(irr.cost > 0 ? 'نقد' : 'ديزل')),
              DataCell(Text(irr.cost.toStringAsFixed(2))),
              DataCell(Text(irr.dieselConsumption.toStringAsFixed(2))),
              DataCell(Text(irr.notes ?? '-')),
            ]);
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildFloatingActionButtons() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        FloatingActionButton(
          heroTag: "analytics",
          onPressed: _showAnalytics,
          backgroundColor: Colors.purple,
          tooltip: 'تحليلات متقدمة',
          child: const Icon(Icons.analytics, color: Colors.white),
        ),
        const SizedBox(height: 8),
        FloatingActionButton(
          heroTag: "export",
          onPressed: _exportToExcel,
          backgroundColor: Colors.green,
          tooltip: 'تصدير إلى Excel',
          child: const Icon(Icons.file_download, color: Colors.white),
        ),
        const SizedBox(height: 8),
        FloatingActionButton(
          heroTag: "refresh",
          onPressed: _loadAllData,
          backgroundColor: AppTheme.primaryColor,
          tooltip: 'تحديث البيانات',
          child: const Icon(Icons.refresh, color: Colors.white),
        ),
      ],
    );
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isStartDate ? _startDate : _endDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
          // التأكد من أن تاريخ البداية قبل تاريخ النهاية
          if (_endDate.isBefore(_startDate)) {
            _endDate = _startDate.add(const Duration(days: 1));
          }
        } else {
          // التأكد من أن تاريخ النهاية بعد تاريخ البداية
          if (picked.isBefore(_startDate)) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تاريخ النهاية يجب أن يكون بعد تاريخ البداية'),
                backgroundColor: Colors.orange,
              ),
            );
            return;
          }
          _endDate = picked;
        }
      });
    }
  }

  void _resetFilters() {
    setState(() {
      _startDate = DateTime.now().subtract(const Duration(days: 30));
      _endDate = DateTime.now();
      _selectedClient = 'all';
      _selectedFarm = 'all';
      _selectedStatus = 'all';
      _sortBy = 'date';
      _sortAscending = false;
      _searchController.clear();
      _searchQuery = '';
    });
  }

  void _showAnalytics() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('التحليلات المتقدمة'),
        content: const Text('سيتم تطوير التحليلات المتقدمة قريباً'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _exportToExcel() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاري تصدير التقرير إلى Excel...'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _printReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاري طباعة التقرير...'),
        backgroundColor: AppTheme.primaryColor,
      ),
    );
  }

  void _shareReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاري مشاركة التقرير...'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  Future<void> _exportToPdf() async {
    try {
      // تحميل العملاء والمزارع إذا لم تكن محملة
      final clients = await ClientDataSource().getAllClients();
      final farms = await FarmDataSource().getAllFarms();
      final pdfFile = await PdfService().createIrrigationStatementPdf(
        irrigations: _filteredIrrigations,
        clients: clients,
        farms: farms,
        fromDate: _startDate,
        toDate: _endDate,
        logoAssetPath: 'assets/images/app_logo.png',
      );
      // ignore: use_build_context_synchronously
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
            content: Text('تم إنشاء تقرير PDF للتسقيات بنجاح!'),
            backgroundColor: Colors.green),
      );
      // يمكنك استخدام مكتبة share_plus أو open_file لمشاركة أو فتح الملف
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
            content: Text('حدث خطأ أثناء تصدير PDF: $e'),
            backgroundColor: Colors.red),
      );
    }
  }
}

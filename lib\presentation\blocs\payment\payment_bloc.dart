import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:untitled/data/datasources/payment_datasource.dart';
import 'package:untitled/data/datasources/client_account_datasource.dart';
import 'package:untitled/data/datasources/cashbox_datasource.dart';
import 'package:untitled/core/services/payment_distribution_service.dart';
import 'package:untitled/presentation/blocs/payment/payment_event.dart';
import 'package:untitled/presentation/blocs/payment/payment_state.dart';

class PaymentBloc extends Bloc<PaymentEvent, PaymentState> {
  final PaymentDataSource _paymentDataSource;
  final PaymentDistributionService _distributionService;

  PaymentBloc(
    this._paymentDataSource, {
    ClientAccountDataSource? clientAccountDataSource,
    CashboxDataSource? cashboxDataSource,
    PaymentDistributionService? distributionService,
  })  : _distributionService = distributionService ?? PaymentDistributionService(
          clientAccountDataSource: clientAccountDataSource ?? ClientAccountDataSource(),
          cashboxDataSource: cashboxDataSource ?? CashboxDataSource(),
          paymentDataSource: _paymentDataSource,
        ),
        super(const PaymentInitial()) {
    on<LoadPayments>(_onLoadPayments);
    on<LoadPaymentsByClientId>(_onLoadPaymentsByClientId);
    on<LoadPaymentsByFarmId>(_onLoadPaymentsByFarmId);
    on<LoadPaymentsByDateRange>(_onLoadPaymentsByDateRange);
    on<LoadPaymentsByType>(_onLoadPaymentsByType);
    on<LoadPaymentsByCashboxId>(_onLoadPaymentsByCashboxId);
    on<AddPayment>(_onAddPayment);
    on<UpdatePayment>(_onUpdatePayment);
    on<DeletePayment>(_onDeletePayment);
    on<GetPaymentById>(_onGetPaymentById);
    on<GetTodayPaymentsCount>(_onGetTodayPaymentsCount);
    on<GetTotalCashPayments>(_onGetTotalCashPayments);
    on<GetTotalDieselPayments>(_onGetTotalDieselPayments);
  }

  Future<void> _onLoadPayments(
    LoadPayments event,
    Emitter<PaymentState> emit,
  ) async {
    emit(const PaymentLoading());
    try {
      final payments = await _paymentDataSource.getAllPayments();
      emit(PaymentsLoaded(payments));
    } catch (e) {
      emit(PaymentError('حدث خطأ أثناء تحميل المدفوعات: $e'));
    }
  }

  Future<void> _onLoadPaymentsByClientId(
    LoadPaymentsByClientId event,
    Emitter<PaymentState> emit,
  ) async {
    emit(const PaymentLoading());
    try {
      final payments =
          await _paymentDataSource.getPaymentsByClientId(event.clientId);
      emit(PaymentsLoaded(payments));
    } catch (e) {
      emit(PaymentError('حدث خطأ أثناء تحميل مدفوعات العميل: $e'));
    }
  }

  Future<void> _onLoadPaymentsByFarmId(
    LoadPaymentsByFarmId event,
    Emitter<PaymentState> emit,
  ) async {
    emit(const PaymentLoading());
    try {
      final payments =
          await _paymentDataSource.getPaymentsByFarmId(event.farmId);
      emit(PaymentsLoaded(payments));
    } catch (e) {
      emit(PaymentError('حدث خطأ أثناء تحميل مدفوعات المزرعة: $e'));
    }
  }

  Future<void> _onLoadPaymentsByDateRange(
    LoadPaymentsByDateRange event,
    Emitter<PaymentState> emit,
  ) async {
    emit(const PaymentLoading());
    try {
      final payments = await _paymentDataSource.getPaymentsByDateRange(
        event.startDate,
        event.endDate,
      );
      emit(PaymentsLoaded(payments));
    } catch (e) {
      emit(PaymentError('حدث خطأ أثناء تحميل المدفوعات في الفترة المحددة: $e'));
    }
  }

  Future<void> _onLoadPaymentsByType(
    LoadPaymentsByType event,
    Emitter<PaymentState> emit,
  ) async {
    emit(const PaymentLoading());
    try {
      final payments = await _paymentDataSource.getPaymentsByType(event.type);
      emit(PaymentsLoaded(payments));
    } catch (e) {
      emit(PaymentError('حدث خطأ أثناء تحميل المدفوعات حسب النوع: $e'));
    }
  }

  Future<void> _onLoadPaymentsByCashboxId(
    LoadPaymentsByCashboxId event,
    Emitter<PaymentState> emit,
  ) async {
    emit(const PaymentLoading());
    try {
      final payments = await _paymentDataSource.getPaymentsByCashboxId(event.cashboxId);
      emit(PaymentsLoaded(payments));
    } catch (e) {
      emit(PaymentError('حدث خطأ أثناء تحميل مدفوعات الصندوق: $e'));
    }
  }

  Future<void> _onAddPayment(
    AddPayment event,
    Emitter<PaymentState> emit,
  ) async {
    emit(const PaymentLoading());
    try {
      debugPrint('🔄 بدء إضافة دفعة جديدة للعميل: ${event.payment.clientId}');
      debugPrint('💰 نوع الدفعة: ${event.payment.type}, المبلغ: ${event.payment.amount}');

      // استخدام PaymentDistributionService لمعالجة الدفعة
      PaymentDistributionResult result;

      if (event.payment.type == 'cash') {
        result = await _distributionService.distributeCashPayment(
          clientId: event.payment.clientId,
          amount: event.payment.amount,
          description: event.payment.notes ?? 'دفعة نقدية',
        );
      } else if (event.payment.type == 'diesel') {
        result = await _distributionService.distributeDieselPayment(
          clientId: event.payment.clientId,
          amount: event.payment.amount,
          description: event.payment.notes ?? 'دفعة ديزل',
        );
      } else {
        emit(PaymentError('نوع الدفعة غير صحيح: ${event.payment.type}'));
        return;
      }

      if (result.isSuccess) {
        debugPrint('✅ تم توزيع الدفعة بنجاح: ${result.message}');
        debugPrint('🆔 معرف الدفعة: ${result.paymentId}');
        debugPrint('🏦 معرف الصندوق: ${result.cashboxId}');

        emit(PaymentOperationSuccess(result.message));

        // إعادة تحميل الدفعات
        final payments = await _paymentDataSource.getAllPayments();
        emit(PaymentsLoaded(payments));
      } else {
        debugPrint('🚨 فشل في توزيع الدفعة: ${result.message}');
        emit(PaymentError(result.message));
      }
    } catch (e) {
      debugPrint('🚨 خطأ في إضافة الدفعة: $e');
      String errorMessage = 'حدث خطأ أثناء إضافة الدفعة';

      if (e.toString().contains('FOREIGN KEY constraint failed')) {
        errorMessage = 'خطأ في البيانات: العميل غير موجود';
      } else if (e.toString().contains('NOT NULL constraint failed')) {
        errorMessage = 'خطأ: جميع البيانات المطلوبة يجب أن تكون مُعبأة';
      }

      emit(PaymentError('$errorMessage: ${e.toString()}'));
    }
  }

  Future<void> _onUpdatePayment(
    UpdatePayment event,
    Emitter<PaymentState> emit,
  ) async {
    emit(const PaymentLoading());
    try {
      await _paymentDataSource.updatePayment(event.payment);
      final payments = await _paymentDataSource.getAllPayments();
      emit(const PaymentOperationSuccess('تم تحديث بيانات الدفعة بنجاح'));
      emit(PaymentsLoaded(payments));
    } catch (e) {
      emit(PaymentError('حدث خطأ أثناء تحديث بيانات الدفعة: $e'));
    }
  }

  Future<void> _onDeletePayment(
    DeletePayment event,
    Emitter<PaymentState> emit,
  ) async {
    emit(const PaymentLoading());
    try {
      await _paymentDataSource.deletePayment(event.paymentId);
      final payments = await _paymentDataSource.getAllPayments();
      emit(const PaymentOperationSuccess('تم حذف الدفعة بنجاح'));
      emit(PaymentsLoaded(payments));
    } catch (e) {
      emit(PaymentError('حدث خطأ أثناء حذف الدفعة: $e'));
    }
  }

  Future<void> _onGetPaymentById(
    GetPaymentById event,
    Emitter<PaymentState> emit,
  ) async {
    emit(const PaymentLoading());
    try {
      final payment = await _paymentDataSource.getPaymentById(event.paymentId);
      if (payment != null) {
        emit(PaymentLoaded(payment));
      } else {
        emit(const PaymentError('الدفعة غير موجودة'));
      }
    } catch (e) {
      emit(PaymentError('حدث خطأ أثناء تحميل بيانات الدفعة: $e'));
    }
  }

  Future<void> _onGetTodayPaymentsCount(
    GetTodayPaymentsCount event,
    Emitter<PaymentState> emit,
  ) async {
    emit(const PaymentLoading());
    try {
      final count = await _paymentDataSource.getTodayPaymentsCount();
      emit(TodayPaymentsCountLoaded(count));
    } catch (e) {
      emit(PaymentError('حدث خطأ أثناء حساب عدد مدفوعات اليوم: $e'));
    }
  }

  Future<void> _onGetTotalCashPayments(
    GetTotalCashPayments event,
    Emitter<PaymentState> emit,
  ) async {
    emit(const PaymentLoading());
    try {
      final totalCash = await _paymentDataSource.getTotalCashPayments();
      emit(TotalCashPaymentsLoaded(totalCash));
    } catch (e) {
      emit(PaymentError('حدث خطأ أثناء حساب إجمالي المدفوعات النقدية: $e'));
    }
  }

  Future<void> _onGetTotalDieselPayments(
    GetTotalDieselPayments event,
    Emitter<PaymentState> emit,
  ) async {
    emit(const PaymentLoading());
    try {
      final totalDiesel = await _paymentDataSource.getTotalDieselPayments();
      emit(TotalDieselPaymentsLoaded(totalDiesel));
    } catch (e) {
      emit(PaymentError('حدث خطأ أثناء حساب إجمالي مدفوعات الديزل: $e'));
    }
  }
}

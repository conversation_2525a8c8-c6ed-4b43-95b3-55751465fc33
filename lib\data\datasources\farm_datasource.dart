import 'package:untitled/data/datasources/database_helper.dart';
import 'package:untitled/data/models/farm_model.dart';

class FarmDataSource {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // إضافة مزرعة جديدة
  Future<int> addFarm(FarmModel farm) async {
    final db = await _databaseHelper.database;
    return await db.insert('farms', farm.toJson());
  }

  // الحصول على جميع المزارع
  Future<List<FarmModel>> getAllFarms() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query('farms');
    return List.generate(maps.length, (i) {
      return FarmModel.fromJson(maps[i]);
    });
  }

  // الحصول على مزرعة بواسطة المعرف
  Future<FarmModel?> getFarmById(int id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'farms',
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return FarmModel.fromJson(maps.first);
    }
    return null;
  }

  // الحصول على مزارع عميل معين
  Future<List<FarmModel>> getFarmsByClientId(int clientId) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'farms',
      where: 'client_id = ?',
      whereArgs: [clientId],
    );
    return List.generate(maps.length, (i) {
      return FarmModel.fromJson(maps[i]);
    });
  }

  // البحث عن مزارع
  Future<List<FarmModel>> searchFarms(String query) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'farms',
      where: 'name LIKE ? OR location LIKE ?',
      whereArgs: ['%$query%', '%$query%'],
    );
    return List.generate(maps.length, (i) {
      return FarmModel.fromJson(maps[i]);
    });
  }

  // تحديث بيانات مزرعة
  Future<int> updateFarm(FarmModel farm) async {
    final db = await _databaseHelper.database;
    return await db.update(
      'farms',
      farm.toJson(),
      where: 'id = ?',
      whereArgs: [farm.id],
    );
  }

  // حذف مزرعة
  Future<int> deleteFarm(int id) async {
    final db = await _databaseHelper.database;
    return await db.delete(
      'farms',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // الحصول على عدد المزارع
  Future<int> getFarmsCount() async {
    final db = await _databaseHelper.database;
    final result = await db.rawQuery('SELECT COUNT(*) FROM farms');
    return result.first.values.first as int? ?? 0;
  }

  // الحصول على عدد مزارع عميل معين
  Future<int> getFarmsCountByClientId(int clientId) async {
    final db = await _databaseHelper.database;
    final result = await db.rawQuery(
      'SELECT COUNT(*) FROM farms WHERE client_id = ?',
      [clientId],
    );
    return result.first.values.first as int? ?? 0;
  }
}

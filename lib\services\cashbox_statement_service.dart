import 'package:sqflite/sqflite.dart';
import 'package:untitled/data/models/account_statement_model.dart';
import 'package:untitled/data/datasources/database_helper.dart';

/// خدمة تقارير كشف حساب الصناديق
class CashboxStatementService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// إنشاء كشف حساب لصندوق محدد
  Future<CashboxStatementModel> generateCashboxStatement({
    required String cashboxId,
    required DateTime fromDate,
    required DateTime toDate,
  }) async {
    final db = await _databaseHelper.database;
    
    // الحصول على بيانات الصندوق
    final cashboxData = await db.query(
      'cashboxes',
      where: 'id = ?',
      whereArgs: [cashboxId],
    );
    
    if (cashboxData.isEmpty) {
      throw Exception('الصندوق غير موجود');
    }
    
    final cashbox = cashboxData.first;
    final cashboxName = cashbox['name'] as String;
    final cashboxType = cashbox['type'] == 'cash' ? CashboxType.cash : CashboxType.diesel;
    
    // حساب الرصيد الابتدائي
    final initialBalance = await _calculateCashboxInitialBalance(cashboxId, fromDate);
    
    // الحصول على جميع المعاملات في الفترة المحددة
    final transactions = await _getCashboxTransactions(cashboxId, cashboxType, fromDate, toDate);
    
    // حساب الإجماليات
    double totalIn = 0;
    double totalOut = 0;
    
    for (final transaction in transactions) {
      if (transaction.amount > 0) {
        totalIn += transaction.amount;
      } else {
        totalOut += transaction.amount.abs();
      }
    }
    
    // حساب الرصيد النهائي
    final finalBalance = initialBalance + totalIn - totalOut;
    
    return CashboxStatementModel(
      id: 'cashbox_stmt_${cashboxId}_${DateTime.now().millisecondsSinceEpoch}',
      cashboxId: cashboxId,
      cashboxName: cashboxName,
      cashboxType: cashboxType,
      fromDate: fromDate,
      toDate: toDate,
      transactions: transactions,
      initialBalance: initialBalance,
      finalBalance: finalBalance,
      totalIn: totalIn,
      totalOut: totalOut,
    );
  }

  /// حساب الرصيد الابتدائي للصندوق قبل تاريخ معين
  Future<double> _calculateCashboxInitialBalance(String cashboxId, DateTime beforeDate) async {
    final db = await _databaseHelper.database;
    
    // الحصول على نوع الصندوق
    final cashboxData = await db.query(
      'cashboxes',
      columns: ['type'],
      where: 'id = ?',
      whereArgs: [cashboxId],
    );
    
    if (cashboxData.isEmpty) return 0.0;
    
    final cashboxType = cashboxData.first['type'] as String;
    
    if (cashboxType == 'cash') {
      return await _calculateCashboxCashBalance(beforeDate);
    } else {
      return await _calculateCashboxDieselBalance(beforeDate);
    }
  }

  /// حساب رصيد الصندوق النقدي
  Future<double> _calculateCashboxCashBalance(DateTime beforeDate) async {
    final db = await _databaseHelper.database;
    
    // إجمالي المدفوعات النقدية المستلمة
    final cashPayments = await db.rawQuery('''
      SELECT COALESCE(SUM(amount), 0) as total
      FROM payments 
      WHERE type = 'cash' AND created_at < ?
    ''', [beforeDate.toIso8601String()]);
    
    // إجمالي تكلفة التسقيات (إيرادات)
    final irrigationRevenue = await db.rawQuery('''
      SELECT COALESCE(SUM(total_cost), 0) as total
      FROM irrigations 
      WHERE created_at < ?
    ''', [beforeDate.toIso8601String()]);
    
    // المصروفات النقدية (إن وجدت)
    final cashExpenses = await db.rawQuery('''
      SELECT COALESCE(SUM(amount), 0) as total
      FROM expenses 
      WHERE type = 'cash' AND created_at < ?
    ''', [beforeDate.toIso8601String()]);
    
    final totalPayments = (cashPayments.first['total'] as num).toDouble();
    final totalRevenue = (irrigationRevenue.first['total'] as num).toDouble();
    final totalExpenses = (cashExpenses.first['total'] as num).toDouble();
    
    return totalPayments + totalRevenue - totalExpenses;
  }

  /// حساب رصيد صندوق الديزل
  Future<double> _calculateCashboxDieselBalance(DateTime beforeDate) async {
    final db = await _databaseHelper.database;
    
    // إجمالي مدفوعات الديزل المستلمة
    final dieselPayments = await db.rawQuery('''
      SELECT COALESCE(SUM(amount), 0) as total
      FROM payments 
      WHERE type = 'diesel' AND created_at < ?
    ''', [beforeDate.toIso8601String()]);
    
    // إجمالي الديزل المستهلك في التسقيات
    final dieselConsumed = await db.rawQuery('''
      SELECT COALESCE(SUM(diesel_consumed), 0) as total
      FROM irrigations 
      WHERE created_at < ?
    ''', [beforeDate.toIso8601String()]);
    
    // مشتريات الديزل (إيرادات للصندوق)
    final dieselPurchases = await db.rawQuery('''
      SELECT COALESCE(SUM(amount), 0) as total
      FROM expenses 
      WHERE type = 'diesel_purchase' AND created_at < ?
    ''', [beforeDate.toIso8601String()]);
    
    final totalPayments = (dieselPayments.first['total'] as num).toDouble();
    final totalConsumed = (dieselConsumed.first['total'] as num).toDouble();
    final totalPurchases = (dieselPurchases.first['total'] as num).toDouble();
    
    return totalPayments + totalPurchases - totalConsumed;
  }

  /// الحصول على معاملات الصندوق في فترة محددة
  Future<List<CashboxTransactionModel>> _getCashboxTransactions(
    String cashboxId,
    CashboxType cashboxType,
    DateTime fromDate,
    DateTime toDate,
  ) async {
    final db = await _databaseHelper.database;
    final transactions = <CashboxTransactionModel>[];
    
    if (cashboxType == CashboxType.cash) {
      // معاملات الصندوق النقدي
      await _addCashTransactions(db, transactions, fromDate, toDate);
    } else {
      // معاملات صندوق الديزل
      await _addDieselTransactions(db, transactions, fromDate, toDate);
    }
    
    // ترتيب المعاملات حسب التاريخ
    transactions.sort((a, b) => a.date.compareTo(b.date));
    
    // حساب الرصيد الجاري
    final initialBalance = await _calculateCashboxInitialBalance(cashboxId, fromDate);
    double runningBalance = initialBalance;
    
    for (final transaction in transactions) {
      runningBalance += transaction.amount;
      // تحديث الرصيد الجاري في المعاملة
      final updatedTransaction = CashboxTransactionModel(
        id: transaction.id,
        date: transaction.date,
        type: transaction.type,
        description: transaction.description,
        amount: transaction.amount,
        runningBalance: runningBalance,
        clientName: transaction.clientName,
        notes: transaction.notes,
        referenceId: transaction.referenceId,
      );
      
      // استبدال المعاملة بالمحدثة
      final index = transactions.indexOf(transaction);
      transactions[index] = updatedTransaction;
    }
    
    return transactions;
  }

  /// إضافة معاملات الصندوق النقدي
  Future<void> _addCashTransactions(
    Database db,
    List<CashboxTransactionModel> transactions,
    DateTime fromDate,
    DateTime toDate,
  ) async {
    // المدفوعات النقدية (إيرادات)
    final cashPayments = await db.rawQuery('''
      SELECT p.*, c.name as client_name
      FROM payments p
      LEFT JOIN clients c ON p.client_id = c.id
      WHERE p.type = 'cash' AND p.created_at BETWEEN ? AND ?
      ORDER BY p.created_at ASC
    ''', [fromDate.toIso8601String(), toDate.toIso8601String()]);
    
    for (final payment in cashPayments) {
      transactions.add(CashboxTransactionModel(
        id: 'cash_pay_${payment['id']}',
        date: DateTime.parse(payment['created_at'] as String),
        type: CashboxTransactionType.income,
        description: 'دفعة نقدية من ${payment['client_name'] ?? 'غير محدد'}',
        amount: (payment['amount'] as num).toDouble(),
        runningBalance: 0, // سيتم حسابه لاحقاً
        clientName: payment['client_name'] as String?,
        notes: payment['notes'] as String?,
        referenceId: payment['id'].toString(),
      ));
    }
    
    // إيرادات التسقيات
    final irrigationRevenue = await db.rawQuery('''
      SELECT i.*, c.name as client_name, f.name as farm_name
      FROM irrigations i
      LEFT JOIN clients c ON i.client_id = c.id
      LEFT JOIN farms f ON i.farm_id = f.id
      WHERE i.created_at BETWEEN ? AND ?
      ORDER BY i.created_at ASC
    ''', [fromDate.toIso8601String(), toDate.toIso8601String()]);
    
    for (final irrigation in irrigationRevenue) {
      transactions.add(CashboxTransactionModel(
        id: 'cash_irr_${irrigation['id']}',
        date: DateTime.parse(irrigation['created_at'] as String),
        type: CashboxTransactionType.income,
        description: 'إيراد تسقية - ${irrigation['farm_name'] ?? 'غير محدد'}',
        amount: (irrigation['total_cost'] as num).toDouble(),
        runningBalance: 0, // سيتم حسابه لاحقاً
        clientName: irrigation['client_name'] as String?,
        notes: irrigation['notes'] as String?,
        referenceId: irrigation['id'].toString(),
      ));
    }
    
    // المصروفات النقدية
    final cashExpenses = await db.rawQuery('''
      SELECT *
      FROM expenses
      WHERE type = 'cash' AND created_at BETWEEN ? AND ?
      ORDER BY created_at ASC
    ''', [fromDate.toIso8601String(), toDate.toIso8601String()]);
    
    for (final expense in cashExpenses) {
      transactions.add(CashboxTransactionModel(
        id: 'cash_exp_${expense['id']}',
        date: DateTime.parse(expense['created_at'] as String),
        type: CashboxTransactionType.expense,
        description: (expense['description'] as String?) ?? 'مصروف نقدي',
        amount: -(expense['amount'] as num).toDouble(),
        runningBalance: 0, // سيتم حسابه لاحقاً
        notes: expense['notes'] as String?,
        referenceId: expense['id'].toString(),
      ));
    }
  }

  /// إضافة معاملات صندوق الديزل
  Future<void> _addDieselTransactions(
    Database db,
    List<CashboxTransactionModel> transactions,
    DateTime fromDate,
    DateTime toDate,
  ) async {
    // مدفوعات الديزل (إيرادات)
    final dieselPayments = await db.rawQuery('''
      SELECT p.*, c.name as client_name
      FROM payments p
      LEFT JOIN clients c ON p.client_id = c.id
      WHERE p.type = 'diesel' AND p.created_at BETWEEN ? AND ?
      ORDER BY p.created_at ASC
    ''', [fromDate.toIso8601String(), toDate.toIso8601String()]);
    
    for (final payment in dieselPayments) {
      transactions.add(CashboxTransactionModel(
        id: 'diesel_pay_${payment['id']}',
        date: DateTime.parse(payment['created_at'] as String),
        type: CashboxTransactionType.income,
        description: 'دفعة ديزل من ${payment['client_name'] ?? 'غير محدد'}',
        amount: (payment['amount'] as num).toDouble(),
        runningBalance: 0, // سيتم حسابه لاحقاً
        clientName: payment['client_name'] as String?,
        notes: payment['notes'] as String?,
        referenceId: payment['id'].toString(),
      ));
    }
    
    // استهلاك الديزل في التسقيات (مصروفات)
    final dieselConsumption = await db.rawQuery('''
      SELECT i.*, c.name as client_name, f.name as farm_name
      FROM irrigations i
      LEFT JOIN clients c ON i.client_id = c.id
      LEFT JOIN farms f ON i.farm_id = f.id
      WHERE i.created_at BETWEEN ? AND ?
      ORDER BY i.created_at ASC
    ''', [fromDate.toIso8601String(), toDate.toIso8601String()]);
    
    for (final irrigation in dieselConsumption) {
      transactions.add(CashboxTransactionModel(
        id: 'diesel_cons_${irrigation['id']}',
        date: DateTime.parse(irrigation['created_at'] as String),
        type: CashboxTransactionType.expense,
        description: 'استهلاك ديزل - ${irrigation['farm_name'] ?? 'غير محدد'}',
        amount: -(irrigation['diesel_consumed'] as num).toDouble(),
        runningBalance: 0, // سيتم حسابه لاحقاً
        clientName: irrigation['client_name'] as String?,
        notes: irrigation['notes'] as String?,
        referenceId: irrigation['id'].toString(),
      ));
    }
    
    // مشتريات الديزل (إيرادات)
    final dieselPurchases = await db.rawQuery('''
      SELECT *
      FROM expenses
      WHERE type = 'diesel_purchase' AND created_at BETWEEN ? AND ?
      ORDER BY created_at ASC
    ''', [fromDate.toIso8601String(), toDate.toIso8601String()]);
    
    for (final purchase in dieselPurchases) {
      transactions.add(CashboxTransactionModel(
        id: 'diesel_purch_${purchase['id']}',
        date: DateTime.parse(purchase['created_at'] as String),
        type: CashboxTransactionType.income,
        description: (purchase['description'] as String?) ?? 'شراء ديزل',
        amount: (purchase['amount'] as num).toDouble(),
        runningBalance: 0, // سيتم حسابه لاحقاً
        notes: purchase['notes'] as String?,
        referenceId: purchase['id'].toString(),
      ));
    }
  }

  /// إنشاء كشف حساب إجمالي لجميع الصناديق
  Future<List<Map<String, dynamic>>> generateAllCashboxesStatement({
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    final db = await _databaseHelper.database;
    
    // الحصول على جميع الصناديق
    final cashboxes = await db.query('cashboxes', orderBy: 'name ASC');
    final statements = <Map<String, dynamic>>[];
    
    for (final cashbox in cashboxes) {
      final cashboxId = cashbox['id'].toString();
      final cashboxName = cashbox['name'] as String;
      final cashboxType = cashbox['type'] as String;
      
      // حساب الرصيد الحالي
      final currentBalance = await _getCurrentCashboxBalance(cashboxId, cashboxType);
      
      statements.add({
        'cashbox_id': cashboxId,
        'cashbox_name': cashboxName,
        'cashbox_type': cashboxType,
        'current_balance': currentBalance,
        'total_transactions': await _getCashboxTransactionsCount(cashboxId, cashboxType, fromDate, toDate),
      });
    }
    
    return statements;
  }

  /// حساب الرصيد الحالي للصندوق
  Future<double> _getCurrentCashboxBalance(String cashboxId, String cashboxType) async {
    if (cashboxType == 'cash') {
      return await _calculateCashboxCashBalance(DateTime.now());
    } else {
      return await _calculateCashboxDieselBalance(DateTime.now());
    }
  }

  /// عدد المعاملات للصندوق في فترة محددة
  Future<int> _getCashboxTransactionsCount(String cashboxId, String cashboxType, DateTime? fromDate, DateTime? toDate) async {
    final db = await _databaseHelper.database;
    
    String dateFilter = '';
    List<dynamic> args = [];
    
    if (fromDate != null && toDate != null) {
      dateFilter = ' AND created_at BETWEEN ? AND ?';
      args = [fromDate.toIso8601String(), toDate.toIso8601String()];
    }
    
    int count = 0;
    
    if (cashboxType == 'cash') {
      // عدد المدفوعات النقدية
      final paymentsResult = await db.rawQuery('''
        SELECT COUNT(*) as count
        FROM payments 
        WHERE type = 'cash'$dateFilter
      ''', args);
      
      // عدد التسقيات
      final irrigationsResult = await db.rawQuery('''
        SELECT COUNT(*) as count
        FROM irrigations 
        WHERE 1=1$dateFilter
      ''', args);
      
      count = (paymentsResult.first['count'] as num).toInt() + 
              (irrigationsResult.first['count'] as num).toInt();
    } else {
      // عدد مدفوعات الديزل
      final paymentsResult = await db.rawQuery('''
        SELECT COUNT(*) as count
        FROM payments 
        WHERE type = 'diesel'$dateFilter
      ''', args);
      
      // عدد التسقيات (استهلاك ديزل)
      final irrigationsResult = await db.rawQuery('''
        SELECT COUNT(*) as count
        FROM irrigations 
        WHERE 1=1$dateFilter
      ''', args);
      
      count = (paymentsResult.first['count'] as num).toInt() + 
              (irrigationsResult.first['count'] as num).toInt();
    }
    
    return count;
  }
}

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:untitled/data/datasources/cashbox_datasource.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_event.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_state.dart';

class CashboxBloc extends Bloc<CashboxEvent, CashboxState> {
  final CashboxDataSource _cashboxDataSource;

  CashboxBloc(this._cashboxDataSource) : super(const CashboxInitial()) {
    on<LoadCashboxes>(_onLoadCashboxes);
    on<LoadCashboxesByType>(_onLoadCashboxesByType);
    on<AddCashbox>(_onAddCashbox);
    on<UpdateCashbox>(_onUpdateCashbox);
    on<UpdateCashboxBalance>(_onUpdateCashboxBalance);
    on<DeleteCashbox>(_onDeleteCashbox);
    on<GetCashboxById>(_onGetCashboxById);
    on<GetTotalCashBalance>(_onGetTotalCashBalance);
    on<GetTotalDieselBalance>(_onGetTotalDieselBalance);
  }

  Future<void> _onLoadCashboxes(
    LoadCashboxes event,
    Emitter<CashboxState> emit,
  ) async {
    emit(const CashboxLoading());
    try {
      final cashboxes = await _cashboxDataSource.getAllCashboxes();
      emit(CashboxesLoaded(cashboxes));
    } catch (e) {
      emit(CashboxError('حدث خطأ أثناء تحميل الصناديق: $e'));
    }
  }

  Future<void> _onLoadCashboxesByType(
    LoadCashboxesByType event,
    Emitter<CashboxState> emit,
  ) async {
    emit(const CashboxLoading());
    try {
      final cashboxes = await _cashboxDataSource.getCashboxesByType(event.type);
      emit(CashboxesLoaded(cashboxes));
    } catch (e) {
      emit(CashboxError('حدث خطأ أثناء تحميل الصناديق حسب النوع: $e'));
    }
  }

  Future<void> _onAddCashbox(
    AddCashbox event,
    Emitter<CashboxState> emit,
  ) async {
    emit(const CashboxLoading());
    try {
      await _cashboxDataSource.addCashbox(event.cashbox);
      final cashboxes = await _cashboxDataSource.getAllCashboxes();
      emit(const CashboxOperationSuccess('تم إضافة الصندوق بنجاح'));
      emit(CashboxesLoaded(cashboxes));
    } catch (e) {
      emit(CashboxError('حدث خطأ أثناء إضافة الصندوق: $e'));
    }
  }

  Future<void> _onUpdateCashbox(
    UpdateCashbox event,
    Emitter<CashboxState> emit,
  ) async {
    emit(const CashboxLoading());
    try {
      await _cashboxDataSource.updateCashbox(event.cashbox);
      final cashboxes = await _cashboxDataSource.getAllCashboxes();
      emit(const CashboxOperationSuccess('تم تحديث بيانات الصندوق بنجاح'));
      emit(CashboxesLoaded(cashboxes));
    } catch (e) {
      emit(CashboxError('حدث خطأ أثناء تحديث بيانات الصندوق: $e'));
    }
  }

  Future<void> _onUpdateCashboxBalance(
    UpdateCashboxBalance event,
    Emitter<CashboxState> emit,
  ) async {
    emit(const CashboxLoading());
    try {
      await _cashboxDataSource.updateCashboxBalance(
          event.cashboxId, event.newBalance);
      final cashboxes = await _cashboxDataSource.getAllCashboxes();
      emit(const CashboxOperationSuccess('تم تحديث رصيد الصندوق بنجاح'));
      emit(CashboxesLoaded(cashboxes));
    } catch (e) {
      emit(CashboxError('حدث خطأ أثناء تحديث رصيد الصندوق: $e'));
    }
  }

  Future<void> _onDeleteCashbox(
    DeleteCashbox event,
    Emitter<CashboxState> emit,
  ) async {
    emit(const CashboxLoading());
    try {
      await _cashboxDataSource.deleteCashbox(event.cashboxId);
      final cashboxes = await _cashboxDataSource.getAllCashboxes();
      emit(const CashboxOperationSuccess('تم حذف الصندوق بنجاح'));
      emit(CashboxesLoaded(cashboxes));
    } catch (e) {
      emit(CashboxError('حدث خطأ أثناء حذف الصندوق: $e'));
    }
  }

  Future<void> _onGetCashboxById(
    GetCashboxById event,
    Emitter<CashboxState> emit,
  ) async {
    emit(const CashboxLoading());
    try {
      final cashbox = await _cashboxDataSource.getCashboxById(event.cashboxId);
      if (cashbox != null) {
        emit(CashboxLoaded(cashbox));
      } else {
        emit(const CashboxError('الصندوق غير موجود'));
      }
    } catch (e) {
      emit(CashboxError('حدث خطأ أثناء تحميل بيانات الصندوق: $e'));
    }
  }

  Future<void> _onGetTotalCashBalance(
    GetTotalCashBalance event,
    Emitter<CashboxState> emit,
  ) async {
    emit(const CashboxLoading());
    try {
      final totalCashBalance = await _cashboxDataSource.getTotalCashBalance();
      emit(TotalCashBalanceLoaded(totalCashBalance));
    } catch (e) {
      emit(CashboxError('حدث خطأ أثناء حساب إجمالي رصيد الصناديق النقدية: $e'));
    }
  }

  Future<void> _onGetTotalDieselBalance(
    GetTotalDieselBalance event,
    Emitter<CashboxState> emit,
  ) async {
    emit(const CashboxLoading());
    try {
      final totalDieselBalance =
          await _cashboxDataSource.getTotalDieselBalance();
      emit(TotalDieselBalanceLoaded(totalDieselBalance));
    } catch (e) {
      emit(CashboxError('حدث خطأ أثناء حساب إجمالي رصيد صناديق الديزل: $e'));
    }
  }
}

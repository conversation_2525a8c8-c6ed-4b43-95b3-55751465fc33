import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:untitled/data/models/client_model.dart';
import 'package:untitled/data/models/cashbox_model.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_bloc.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_event.dart';
import 'package:untitled/presentation/blocs/client_account/client_account_bloc.dart';
import 'package:untitled/presentation/blocs/client_account/client_account_event.dart';
import 'package:untitled/core/theme/app_theme.dart';

/// نافذة التحويل من صندوق لعميل
class CashboxToClientTransferDialog extends StatefulWidget {
  final List<ClientModel> clients;
  final List<CashboxModel> cashboxes;

  const CashboxToClientTransferDialog({
    super.key,
    required this.clients,
    required this.cashboxes,
  });

  @override
  State<CashboxToClientTransferDialog> createState() => _CashboxToClientTransferDialogState();
}

class _CashboxToClientTransferDialogState extends State<CashboxToClientTransferDialog> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  
  int? _cashboxId;
  int? _clientId;

  @override
  void dispose() {
    _amountController.dispose();
    super.dispose();
  }

  void _performTransfer() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_cashboxId == null || _clientId == null) {
      _showErrorMessage('يرجى اختيار الصندوق والعميل');
      return;
    }

    final amount = double.parse(_amountController.text);
    final cashbox = widget.cashboxes.firstWhere((c) => c.id == _cashboxId);

    if (cashbox.balance < amount) {
      _showErrorMessage('رصيد الصندوق غير كافي');
      return;
    }

    // خصم من الصندوق
    context.read<CashboxBloc>().add(UpdateCashboxBalance(
      _cashboxId!,
      cashbox.balance - amount,
    ));

    // إضافة للعميل
    if (cashbox.type == 'cash') {
      context.read<ClientAccountBloc>().add(AddCashBalance(_clientId!, amount));
    } else {
      context.read<ClientAccountBloc>().add(AddDieselBalance(_clientId!, amount));
    }

    final client = widget.clients.firstWhere((c) => c.id == _clientId);
    final unit = cashbox.type == 'cash' ? 'ريال' : 'لتر';

    _showSuccessMessage('تم تحويل ${amount.toStringAsFixed(2)} $unit من ${cashbox.name} إلى ${client.name}');
    Navigator.pop(context);
  }

  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.green),
    );
  }

  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('تحويل من صندوق لعميل'),
      content: SizedBox(
        width: double.maxFinite,
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // اختيار الصندوق
              DropdownButtonFormField<int>(
                decoration: const InputDecoration(
                  labelText: 'الصندوق',
                  border: OutlineInputBorder(),
                ),
                value: _cashboxId,
                items: widget.cashboxes.map((cashbox) {
                  return DropdownMenuItem<int>(
                    value: cashbox.id,
                    child: Text('${cashbox.name} (${cashbox.balance.toStringAsFixed(2)} ${cashbox.type == 'cash' ? 'ريال' : 'لتر'})'),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _cashboxId = value;
                  });
                },
                validator: (value) => value == null ? 'يرجى اختيار الصندوق' : null,
              ),
              const SizedBox(height: 16),
              
              // اختيار العميل
              DropdownButtonFormField<int>(
                decoration: const InputDecoration(
                  labelText: 'العميل',
                  border: OutlineInputBorder(),
                ),
                value: _clientId,
                items: widget.clients.map((client) {
                  return DropdownMenuItem<int>(
                    value: client.id,
                    child: Text(client.name),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _clientId = value;
                  });
                },
                validator: (value) => value == null ? 'يرجى اختيار العميل' : null,
              ),
              const SizedBox(height: 16),
              
              // المبلغ
              TextFormField(
                controller: _amountController,
                decoration: const InputDecoration(
                  labelText: 'المبلغ',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال المبلغ';
                  }
                  final amount = double.tryParse(value);
                  if (amount == null || amount <= 0) {
                    return 'يرجى إدخال مبلغ صحيح';
                  }
                  return null;
                },
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _performTransfer,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.primaryColor,
            foregroundColor: Colors.white,
          ),
          child: const Text('تحويل'),
        ),
      ],
    );
  }
}

/// نافذة التحويل من عميل لصندوق
class ClientToCashboxTransferDialog extends StatefulWidget {
  final List<ClientModel> clients;
  final List<CashboxModel> cashboxes;

  const ClientToCashboxTransferDialog({
    super.key,
    required this.clients,
    required this.cashboxes,
  });

  @override
  State<ClientToCashboxTransferDialog> createState() => _ClientToCashboxTransferDialogState();
}

class _ClientToCashboxTransferDialogState extends State<ClientToCashboxTransferDialog> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  
  int? _clientId;
  int? _cashboxId;

  @override
  void dispose() {
    _amountController.dispose();
    super.dispose();
  }

  void _performTransfer() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_clientId == null || _cashboxId == null) {
      _showErrorMessage('يرجى اختيار العميل والصندوق');
      return;
    }

    final amount = double.parse(_amountController.text);
    final cashbox = widget.cashboxes.firstWhere((c) => c.id == _cashboxId);

    // خصم من العميل
    if (cashbox.type == 'cash') {
      context.read<ClientAccountBloc>().add(DeductCashBalance(_clientId!, amount));
    } else {
      context.read<ClientAccountBloc>().add(DeductDieselBalance(_clientId!, amount));
    }

    // إضافة للصندوق
    context.read<CashboxBloc>().add(UpdateCashboxBalance(
      _cashboxId!,
      cashbox.balance + amount,
    ));

    final client = widget.clients.firstWhere((c) => c.id == _clientId);
    final unit = cashbox.type == 'cash' ? 'ريال' : 'لتر';

    _showSuccessMessage('تم تحويل ${amount.toStringAsFixed(2)} $unit من ${client.name} إلى ${cashbox.name}');
    Navigator.pop(context);
  }

  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.green),
    );
  }

  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('تحويل من عميل لصندوق'),
      content: SizedBox(
        width: double.maxFinite,
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // اختيار العميل
              DropdownButtonFormField<int>(
                decoration: const InputDecoration(
                  labelText: 'العميل',
                  border: OutlineInputBorder(),
                ),
                value: _clientId,
                items: widget.clients.map((client) {
                  return DropdownMenuItem<int>(
                    value: client.id,
                    child: Text(client.name),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _clientId = value;
                  });
                },
                validator: (value) => value == null ? 'يرجى اختيار العميل' : null,
              ),
              const SizedBox(height: 16),
              
              // اختيار الصندوق
              DropdownButtonFormField<int>(
                decoration: const InputDecoration(
                  labelText: 'الصندوق',
                  border: OutlineInputBorder(),
                ),
                value: _cashboxId,
                items: widget.cashboxes.map((cashbox) {
                  return DropdownMenuItem<int>(
                    value: cashbox.id,
                    child: Text('${cashbox.name} (${cashbox.type == 'cash' ? 'نقد' : 'ديزل'})'),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _cashboxId = value;
                  });
                },
                validator: (value) => value == null ? 'يرجى اختيار الصندوق' : null,
              ),
              const SizedBox(height: 16),
              
              // المبلغ
              TextFormField(
                controller: _amountController,
                decoration: const InputDecoration(
                  labelText: 'المبلغ',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال المبلغ';
                  }
                  final amount = double.tryParse(value);
                  if (amount == null || amount <= 0) {
                    return 'يرجى إدخال مبلغ صحيح';
                  }
                  return null;
                },
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _performTransfer,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.primaryColor,
            foregroundColor: Colors.white,
          ),
          child: const Text('تحويل'),
        ),
      ],
    );
  }
}

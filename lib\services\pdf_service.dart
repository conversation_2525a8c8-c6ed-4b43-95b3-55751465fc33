import 'dart:io';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:untitled/data/models/client_model.dart';
import 'package:untitled/data/models/farm_model.dart';
import 'package:untitled/data/models/irrigation_model.dart';
import 'package:untitled/data/models/payment_model.dart';
import 'package:untitled/data/models/account_statement_model.dart';
import 'package:flutter/services.dart' show rootBundle;

/// PDF Service - معطل مؤقتاً لتسريع البناء
/// يمكن تفعيله لاحقاً بإزالة التعليقات وإضافة التبعيات
class PdfService {
  // إنشاء فاتورة تسقية - معطل مؤقتاً لتسريع البناء
  Future<File> createIrrigationInvoice({
    required ClientModel client,
    required FarmModel farm,
    required IrrigationModel irrigation,
    required double cashBalance,
    required double dieselBalance,
  }) async {
    // PDF functionality disabled for faster build
    throw UnimplementedError(
        'PDF functionality is temporarily disabled for faster build');
  }

  // إنشاء فاتورة دفع - معطل مؤقتاً لتسريع البناء
  Future<File> createPaymentInvoice({
    required ClientModel client,
    required FarmModel farm,
    required PaymentModel payment,
    required double cashBalance,
    required double dieselBalance,
  }) async {
    // PDF functionality disabled for faster build
    throw UnimplementedError(
        'PDF functionality is temporarily disabled for faster build');
  }

  // إنشاء تقرير - معطل مؤقتاً لتسريع البناء
  Future<File> createReport({
    required String title,
    required String subtitle,
    required List<Map<String, dynamic>> data,
    required List<String> columns,
    required List<String> columnTitles,
  }) async {
    // PDF functionality disabled for faster build
    throw UnimplementedError(
        'PDF functionality is temporarily disabled for faster build');
  }

  /// توليد كشف حساب عميل PDF احترافي بالكامل بالعربية
  Future<File> createClientStatementPdf({
    required AccountStatementModel statement,
    required String logoAssetPath,
  }) async {
    final pdf = pw.Document();

    // تحميل الخطوط مع معالجة الأخطاء
    pw.Font font;
    pw.Font boldFont;

    try {
      final fontData =
          await rootBundle.load('assets/fonts/static/Cairo-Regular.ttf');
      font = pw.Font.ttf(fontData);
      final boldFontData =
          await rootBundle.load('assets/fonts/static/Cairo-Bold.ttf');
      boldFont = pw.Font.ttf(boldFontData);
    } catch (e) {
      // استخدام الخط الافتراضي في حالة عدم وجود الخطوط العربية
      print('تعذر تحميل الخطوط العربية، سيتم استخدام الخط الافتراضي: $e');
      font = pw.Font.helvetica();
      boldFont = pw.Font.helveticaBold();
    }

    final dateFormat = DateFormat('yyyy/MM/dd', 'ar');

    pdf.addPage(
      pw.MultiPage(
        theme: pw.ThemeData.withFont(
          base: font,
          bold: boldFont,
        ),
        textDirection: pw.TextDirection.rtl,
        build: (context) => [
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.end,
                children: [
                  pw.Text('كشف حساب عميل',
                      style: pw.TextStyle(
                          fontSize: 22, fontWeight: pw.FontWeight.bold)),
                  pw.Text('اسم العميل: ${statement.clientName}',
                      style: pw.TextStyle(fontSize: 16)),
                  pw.Text(
                      'الفترة: ${dateFormat.format(statement.fromDate)} - ${dateFormat.format(statement.toDate)}',
                      style: pw.TextStyle(fontSize: 14)),
                ],
              ),
            ],
          ),
          pw.SizedBox(height: 16),
          pw.Table(
            border: pw.TableBorder.all(color: PdfColors.grey400, width: 0.5),
            children: [
              // رأس الجدول
              pw.TableRow(
                decoration: pw.BoxDecoration(color: PdfColors.blue100),
                children: [
                  pw.Padding(
                      padding: const pw.EdgeInsets.all(4),
                      child: pw.Text('أيقونة',
                          style: pw.TextStyle(font: boldFont, fontSize: 13),
                          textAlign: pw.TextAlign.center)),
                  pw.Padding(
                      padding: const pw.EdgeInsets.all(4),
                      child: pw.Text('التاريخ',
                          style: pw.TextStyle(font: boldFont, fontSize: 13),
                          textAlign: pw.TextAlign.center)),
                  pw.Padding(
                      padding: const pw.EdgeInsets.all(4),
                      child: pw.Text('البيان',
                          style: pw.TextStyle(font: boldFont, fontSize: 13),
                          textAlign: pw.TextAlign.center)),
                  pw.Padding(
                      padding: const pw.EdgeInsets.all(4),
                      child: pw.Text('نوع العملية',
                          style: pw.TextStyle(font: boldFont, fontSize: 13),
                          textAlign: pw.TextAlign.center)),
                  pw.Padding(
                      padding: const pw.EdgeInsets.all(4),
                      child: pw.Text('نقدي (+/-)',
                          style: pw.TextStyle(font: boldFont, fontSize: 13),
                          textAlign: pw.TextAlign.center)),
                  pw.Padding(
                      padding: const pw.EdgeInsets.all(4),
                      child: pw.Text('ديزل (+/-)',
                          style: pw.TextStyle(font: boldFont, fontSize: 13),
                          textAlign: pw.TextAlign.center)),
                  pw.Padding(
                      padding: const pw.EdgeInsets.all(4),
                      child: pw.Text('الرصيد النقدي',
                          style: pw.TextStyle(font: boldFont, fontSize: 13),
                          textAlign: pw.TextAlign.center)),
                  pw.Padding(
                      padding: const pw.EdgeInsets.all(4),
                      child: pw.Text('رصيد الديزل',
                          style: pw.TextStyle(font: boldFont, fontSize: 13),
                          textAlign: pw.TextAlign.center)),
                  pw.Padding(
                      padding: const pw.EdgeInsets.all(4),
                      child: pw.Text('ملاحظات',
                          style: pw.TextStyle(font: boldFont, fontSize: 13),
                          textAlign: pw.TextAlign.center)),
                  pw.Padding(
                      padding: const pw.EdgeInsets.all(4),
                      child: pw.Text('رقم المرجع',
                          style: pw.TextStyle(font: boldFont, fontSize: 13),
                          textAlign: pw.TextAlign.center)),
                ],
              ),
              // صفوف العمليات
              ...statement.transactions.map((t) {
                final isPositive = t.cashAmount > 0 || t.dieselAmount > 0;
                final isNegative = t.cashAmount < 0 || t.dieselAmount < 0;
                final rowColor = isPositive
                    ? PdfColors.green50
                    : isNegative
                        ? PdfColors.red50
                        : PdfColors.white;
                final icon =
                    _getTransactionIcon(t.type, t.cashAmount, t.dieselAmount);
                return pw.TableRow(
                  decoration: pw.BoxDecoration(color: rowColor),
                  children: [
                    pw.Padding(
                        padding: const pw.EdgeInsets.all(2),
                        child:
                            pw.Text(icon, style: pw.TextStyle(fontSize: 16))),
                    pw.Padding(
                        padding: const pw.EdgeInsets.all(2),
                        child: pw.Text(dateFormat.format(t.date),
                            style: pw.TextStyle(font: font, fontSize: 11))),
                    pw.Padding(
                        padding: const pw.EdgeInsets.all(2),
                        child: pw.Text(t.description,
                            style: pw.TextStyle(font: font, fontSize: 11),
                            maxLines: 3)),
                    pw.Padding(
                        padding: const pw.EdgeInsets.all(2),
                        child: pw.Text(_getTransactionTypeLabel(t.type),
                            style: pw.TextStyle(font: font, fontSize: 11))),
                    pw.Padding(
                        padding: const pw.EdgeInsets.all(2),
                        child: pw.Text(
                            t.cashAmount == 0
                                ? '-'
                                : t.cashAmount.toStringAsFixed(2),
                            style: pw.TextStyle(
                                font: font,
                                fontSize: 11,
                                color: t.cashAmount > 0
                                    ? PdfColors.green800
                                    : t.cashAmount < 0
                                        ? PdfColors.red800
                                        : PdfColors.black))),
                    pw.Padding(
                        padding: const pw.EdgeInsets.all(2),
                        child: pw.Text(
                            t.dieselAmount == 0
                                ? '-'
                                : t.dieselAmount.toStringAsFixed(2),
                            style: pw.TextStyle(
                                font: font,
                                fontSize: 11,
                                color: t.dieselAmount > 0
                                    ? PdfColors.green800
                                    : t.dieselAmount < 0
                                        ? PdfColors.red800
                                        : PdfColors.black))),
                    pw.Padding(
                        padding: const pw.EdgeInsets.all(2),
                        child: pw.Text(t.runningCashBalance.toStringAsFixed(2),
                            style: pw.TextStyle(font: font, fontSize: 11))),
                    pw.Padding(
                        padding: const pw.EdgeInsets.all(2),
                        child: pw.Text(
                            t.runningDieselBalance.toStringAsFixed(2),
                            style: pw.TextStyle(font: font, fontSize: 11))),
                    pw.Padding(
                        padding: const pw.EdgeInsets.all(2),
                        child: pw.Text(t.notes ?? '-',
                            style: pw.TextStyle(font: font, fontSize: 11),
                            maxLines: 3)),
                    pw.Padding(
                        padding: const pw.EdgeInsets.all(2),
                        child: pw.Text(t.referenceId ?? '-',
                            style: pw.TextStyle(font: font, fontSize: 11))),
                  ],
                );
              }).toList(),
            ],
          ),
          pw.SizedBox(height: 12),
          pw.Container(
            padding: const pw.EdgeInsets.all(8),
            decoration: pw.BoxDecoration(
              color: PdfColors.amber100,
              borderRadius: pw.BorderRadius.circular(8),
            ),
            child: pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                pw.Text(
                    'إجمالي النقدي: ${statement.finalCashBalance.toStringAsFixed(2)} ريال',
                    style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
                pw.Text(
                    'إجمالي الديزل: ${statement.finalDieselBalance.toStringAsFixed(2)} لتر',
                    style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
              ],
            ),
          ),
          pw.SizedBox(height: 24),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text('توقيع المسؤول:', style: pw.TextStyle(fontSize: 14)),
                  pw.SizedBox(height: 32),
                  pw.Text('............................'),
                ],
              ),
              // QR code أو بيانات إضافية يمكن إضافتها هنا
            ],
          ),
        ],
      ),
    );

    final output = await getTemporaryDirectory();
    final file = File(
        '${output.path}/client_statement_${statement.clientId}_${DateTime.now().millisecondsSinceEpoch}.pdf');
    await file.writeAsBytes(await pdf.save());
    return file;
  }

  // أضف دالة مساعدة لترجمة نوع العملية
  String _getTransactionTypeLabel(TransactionType type) {
    switch (type) {
      case TransactionType.irrigation:
        return 'تسقية';
      case TransactionType.cashPayment:
        return 'دفعة نقدية';
      case TransactionType.dieselPayment:
        return 'دفعة ديزل';
      case TransactionType.transfer:
        return 'تحويل';
      case TransactionType.adjustment:
        return 'تسوية';
    }
  }

  // أضف دالة مساعدة لإرجاع أيقونة حسب نوع العملية
  String _getTransactionIcon(TransactionType type, double cash, double diesel) {
    switch (type) {
      case TransactionType.irrigation:
        return '💧';
      case TransactionType.cashPayment:
        return cash > 0 ? '🟢' : '🔴';
      case TransactionType.dieselPayment:
        return diesel > 0 ? '🟢' : '🔴';
      case TransactionType.transfer:
        return '🔄';
      case TransactionType.adjustment:
        return '💸';
    }
  }
}

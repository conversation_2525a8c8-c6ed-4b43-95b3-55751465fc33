import 'dart:io';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:untitled/data/models/client_model.dart';
import 'package:untitled/data/models/farm_model.dart';
import 'package:untitled/data/models/irrigation_model.dart';
import 'package:untitled/data/models/payment_model.dart';
import 'package:untitled/data/models/account_statement_model.dart';
import 'package:flutter/services.dart' show rootBundle;

/// PDF Service - معطل مؤقتاً لتسريع البناء
/// يمكن تفعيله لاحقاً بإزالة التعليقات وإضافة التبعيات
class PdfService {
  // إنشاء فاتورة تسقية - معطل مؤقتاً لتسريع البناء
  Future<File> createIrrigationInvoice({
    required ClientModel client,
    required FarmModel farm,
    required IrrigationModel irrigation,
    required double cashBalance,
    required double dieselBalance,
  }) async {
    // PDF functionality disabled for faster build
    throw UnimplementedError(
        'PDF functionality is temporarily disabled for faster build');
  }

  // إنشاء فاتورة دفع - معطل مؤقتاً لتسريع البناء
  Future<File> createPaymentInvoice({
    required ClientModel client,
    required FarmModel farm,
    required PaymentModel payment,
    required double cashBalance,
    required double dieselBalance,
  }) async {
    // PDF functionality disabled for faster build
    throw UnimplementedError(
        'PDF functionality is temporarily disabled for faster build');
  }

  // إنشاء تقرير - معطل مؤقتاً لتسريع البناء
  Future<File> createReport({
    required String title,
    required String subtitle,
    required List<Map<String, dynamic>> data,
    required List<String> columns,
    required List<String> columnTitles,
  }) async {
    // PDF functionality disabled for faster build
    throw UnimplementedError(
        'PDF functionality is temporarily disabled for faster build');
  }

  /// توليد كشف حساب عميل PDF احترافي بالكامل بالعربية
  Future<File> createClientStatementPdf({
    required AccountStatementModel statement,
    required String logoAssetPath,
  }) async {
    final pdf = pw.Document();

    // تحميل الخطوط مع معالجة الأخطاء
    pw.Font font;
    pw.Font boldFont;

    try {
      final fontData =
          await rootBundle.load('assets/fonts/static/Cairo-Regular.ttf');
      font = pw.Font.ttf(fontData);
      final boldFontData =
          await rootBundle.load('assets/fonts/static/Cairo-Bold.ttf');
      boldFont = pw.Font.ttf(boldFontData);
    } catch (e) {
      // استخدام الخط الافتراضي في حالة عدم وجود الخطوط العربية
      print('تعذر تحميل الخطوط العربية، سيتم استخدام الخط الافتراضي: $e');
      font = pw.Font.helvetica();
      boldFont = pw.Font.helveticaBold();
    }

    final dateFormat = DateFormat('yyyy/MM/dd', 'ar');

    pdf.addPage(
      pw.MultiPage(
        theme: pw.ThemeData.withFont(
          base: font,
          bold: boldFont,
        ),
        textDirection: pw.TextDirection.rtl,
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(20),
        build: (context) => [
          // رأس الصفحة المحسن
          _buildPdfHeader(statement, dateFormat, boldFont, font),
          pw.SizedBox(height: 20),

          // معلومات الرصيد الابتدائي
          _buildInitialBalanceSection(statement, boldFont, font),
          pw.SizedBox(height: 16),
          // الجدول المحاسبي الاحترافي
          _buildTransactionsTable(statement, dateFormat, boldFont, font),
          pw.SizedBox(height: 24),

          // تذييل الصفحة
          _buildFooter(font),
        ],
      ),
    );

    final output = await getTemporaryDirectory();
    final file = File(
        '${output.path}/client_statement_${statement.clientId}_${DateTime.now().millisecondsSinceEpoch}.pdf');
    await file.writeAsBytes(await pdf.save());
    return file;
  }

  /// بناء رأس التقرير المحاسبي الاحترافي
  pw.Widget _buildPdfHeader(AccountStatementModel statement,
      DateFormat dateFormat, pw.Font boldFont, pw.Font font) {
    final accountNumber = _generateAccountNumber(statement.clientId);
    final englishDateFormat = DateFormat('dd-MM-yyyy', 'en');

    return pw.Column(
      children: [
        // رأس الشركة مع الهاتف
        pw.Container(
          padding: const pw.EdgeInsets.symmetric(vertical: 20),
          child: pw.Column(
            children: [
              pw.Text(
                'مياه المراوح',
                style: pw.TextStyle(
                  font: boldFont,
                  fontSize: 24,
                  color: PdfColors.black,
                ),
              ),
              pw.SizedBox(height: 4),
              pw.Text(
                'هاتف: 966-11-1234567',
                style: pw.TextStyle(
                  font: font,
                  fontSize: 14,
                  color: PdfColors.grey700,
                ),
              ),
            ],
          ),
        ),

        // معلومات الحساب
        pw.Container(
          padding: const pw.EdgeInsets.all(16),
          decoration: pw.BoxDecoration(
            border: pw.Border.all(color: PdfColors.grey400, width: 1),
            borderRadius: pw.BorderRadius.circular(8),
          ),
          child: pw.Column(
            children: [
              // الصف الأول: اسم الحساب ورقم الحساب
              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  // اسم الحساب (يمين)
                  pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.end,
                    children: [
                      pw.Text(
                        'اسم الحساب',
                        style: pw.TextStyle(
                          font: font,
                          fontSize: 12,
                          color: PdfColors.grey700,
                        ),
                      ),
                      pw.SizedBox(height: 4),
                      pw.Text(
                        statement.clientName,
                        style: pw.TextStyle(
                          font: boldFont,
                          fontSize: 16,
                          color: PdfColors.black,
                        ),
                      ),
                    ],
                  ),
                  // رقم الحساب (وسط)
                  pw.Column(
                    children: [
                      pw.Text(
                        'رقم الحساب',
                        style: pw.TextStyle(
                          font: font,
                          fontSize: 12,
                          color: PdfColors.grey700,
                        ),
                      ),
                      pw.SizedBox(height: 4),
                      pw.Text(
                        accountNumber,
                        style: pw.TextStyle(
                          font: boldFont,
                          fontSize: 16,
                          color: PdfColors.blue800,
                        ),
                      ),
                    ],
                  ),
                  // العملة (يسار)
                  pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text(
                        'العملة',
                        style: pw.TextStyle(
                          font: font,
                          fontSize: 12,
                          color: PdfColors.grey700,
                        ),
                      ),
                      pw.SizedBox(height: 4),
                      pw.Text(
                        'ريال يمني (YER)',
                        style: pw.TextStyle(
                          font: boldFont,
                          fontSize: 14,
                          color: PdfColors.green700,
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              pw.SizedBox(height: 16),

              // الصف الثاني: الفترة الزمنية
              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.center,
                children: [
                  pw.Text(
                    'كشف حساب للفترة من ',
                    style: pw.TextStyle(
                      font: font,
                      fontSize: 14,
                      color: PdfColors.grey700,
                    ),
                  ),
                  pw.Text(
                    englishDateFormat.format(statement.fromDate),
                    style: pw.TextStyle(
                      font: boldFont,
                      fontSize: 14,
                      color: PdfColors.blue800,
                    ),
                  ),
                  pw.Text(
                    ' إلى ',
                    style: pw.TextStyle(
                      font: font,
                      fontSize: 14,
                      color: PdfColors.grey700,
                    ),
                  ),
                  pw.Text(
                    englishDateFormat.format(statement.toDate),
                    style: pw.TextStyle(
                      font: boldFont,
                      fontSize: 14,
                      color: PdfColors.blue800,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء قسم الرصيد الابتدائي المحسن
  pw.Widget _buildInitialBalanceSection(
      AccountStatementModel statement, pw.Font boldFont, pw.Font font) {
    return pw.Container(
      margin: const pw.EdgeInsets.symmetric(vertical: 8),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          // عنوان القسم
          pw.Container(
            padding: const pw.EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: pw.BoxDecoration(
              color: PdfColors.indigo800,
              borderRadius: const pw.BorderRadius.only(
                topLeft: pw.Radius.circular(8),
                topRight: pw.Radius.circular(8),
              ),
            ),
            child: pw.Row(
              children: [
                pw.Text(
                  '💰 الرصيد السابق (بداية الفترة)',
                  style: pw.TextStyle(
                    font: boldFont,
                    fontSize: 16,
                    color: PdfColors.white,
                  ),
                ),
              ],
            ),
          ),
          // محتوى الأرصدة
          pw.Container(
            padding: const pw.EdgeInsets.all(16),
            decoration: pw.BoxDecoration(
              color: PdfColors.indigo50,
              borderRadius: const pw.BorderRadius.only(
                bottomLeft: pw.Radius.circular(8),
                bottomRight: pw.Radius.circular(8),
              ),
              border: pw.Border.all(color: PdfColors.indigo200, width: 1),
            ),
            child: pw.Row(
              children: [
                // الرصيد النقدي
                pw.Expanded(
                  child: _buildEnhancedBalanceCard(
                    '💵',
                    'الرصيد النقدي',
                    statement.initialCashBalance,
                    'ريال',
                    PdfColors.green700,
                    PdfColors.green50,
                    boldFont,
                    font,
                  ),
                ),
                pw.SizedBox(width: 16),
                // رصيد الديزل
                pw.Expanded(
                  child: _buildEnhancedBalanceCard(
                    '⛽',
                    'رصيد الديزل',
                    statement.initialDieselBalance,
                    'لتر',
                    PdfColors.blue700,
                    PdfColors.blue50,
                    boldFont,
                    font,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة رصيد محسنة
  pw.Widget _buildEnhancedBalanceCard(
    String icon,
    String title,
    double amount,
    String unit,
    PdfColor amountColor,
    PdfColor backgroundColor,
    pw.Font boldFont,
    pw.Font font,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(12),
      decoration: pw.BoxDecoration(
        color: backgroundColor,
        borderRadius: pw.BorderRadius.circular(8),
        border: pw.Border.all(color: amountColor.shade(0.3), width: 1),
      ),
      child: pw.Column(
        children: [
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.center,
            children: [
              pw.Text(
                icon,
                style: const pw.TextStyle(fontSize: 20),
              ),
              pw.SizedBox(width: 8),
              pw.Text(
                title,
                style: pw.TextStyle(
                  font: font,
                  fontSize: 12,
                  color: PdfColors.grey700,
                ),
              ),
            ],
          ),
          pw.SizedBox(height: 8),
          pw.Text(
            '${amount.toStringAsFixed(2)} $unit',
            style: pw.TextStyle(
              font: boldFont,
              fontSize: 16,
              color: amountColor,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء عنصر رصيد
  pw.Widget _buildBalanceItem(String title, double amount, String unit,
      pw.Font boldFont, pw.Font font, PdfColor color) {
    return pw.Column(
      children: [
        pw.Text(title,
            style: pw.TextStyle(
                font: font, fontSize: 12, color: PdfColors.grey700)),
        pw.SizedBox(height: 4),
        pw.Text(
          '${amount.toStringAsFixed(2)} $unit',
          style: pw.TextStyle(font: boldFont, fontSize: 14, color: color),
        ),
      ],
    );
  }

  /// بناء الجدول المحاسبي الاحترافي
  pw.Widget _buildTransactionsTable(AccountStatementModel statement,
      DateFormat dateFormat, pw.Font boldFont, pw.Font font) {
    final englishDateFormat = DateFormat('dd-MM-yyyy', 'en');

    if (statement.transactions.isEmpty) {
      return pw.Container(
        padding: const pw.EdgeInsets.all(20),
        decoration: pw.BoxDecoration(
          border: pw.Border.all(color: PdfColors.grey400, width: 1),
        ),
        child: pw.Center(
          child: pw.Text(
            'لا توجد معاملات في هذه الفترة',
            style: pw.TextStyle(
                font: font, fontSize: 14, color: PdfColors.grey600),
          ),
        ),
      );
    }

    // إنشاء قائمة المعاملات مع الرصيد الافتتاحي
    final List<pw.TableRow> tableRows = [];

    // رأس الجدول
    tableRows.add(
      pw.TableRow(
        decoration: const pw.BoxDecoration(color: PdfColors.grey300),
        children: [
          _buildAccountingTableHeader('التاريخ', boldFont),
          _buildAccountingTableHeader('نوع العمليه', boldFont),
          _buildAccountingTableHeader('رقم المستند', boldFont),
          _buildAccountingTableHeader('رقم القيد', boldFont),
          _buildAccountingTableHeader('الوصف', boldFont),
          _buildAccountingTableHeader('مدين', boldFont),
          _buildAccountingTableHeader('دائن', boldFont),
          _buildAccountingTableHeader('الرصيد', boldFont),
        ],
      ),
    );

    // الرصيد الافتتاحي للنقد
    tableRows.add(
      pw.TableRow(
        decoration: const pw.BoxDecoration(color: PdfColors.blue50),
        children: [
          _buildAccountingTableCell(_getAccountCreationDate(statement), font),
          _buildAccountingTableCell('رصيد افتتاحي', boldFont),
          _buildAccountingTableCell('-', font),
          _buildAccountingTableCell('-', font),
          _buildAccountingTableCell('[نقد] الرصيد في بداية الفترة', font),
          _buildAccountingTableCell('-', font),
          _buildAccountingTableCell('-', font),
          _buildAccountingTableCell(
              '${_formatCurrency(statement.initialCashBalance)} ريال', boldFont,
              color: PdfColors.blue800),
        ],
      ),
    );

    // الرصيد الافتتاحي للديزل
    tableRows.add(
      pw.TableRow(
        decoration: const pw.BoxDecoration(color: PdfColors.blue50),
        children: [
          _buildAccountingTableCell(_getAccountCreationDate(statement), font),
          _buildAccountingTableCell('رصيد افتتاحي', boldFont),
          _buildAccountingTableCell('-', font),
          _buildAccountingTableCell('-', font),
          _buildAccountingTableCell('[ديزل] الرصيد في بداية الفترة', font),
          _buildAccountingTableCell('-', font),
          _buildAccountingTableCell('-', font),
          _buildAccountingTableCell(
              '${_formatCurrency(statement.initialDieselBalance)} لتر',
              boldFont,
              color: PdfColors.blue800),
        ],
      ),
    );

    // معاملات الحساب
    for (int i = 0; i < statement.transactions.length; i++) {
      final transaction = statement.transactions[i];
      final entryNumber = _generateEntryNumber(i + 1);
      final isEven = i % 2 == 0;

      // معاملة النقدي (إذا كانت موجودة)
      if (transaction.cashAmount != 0) {
        tableRows.add(
          pw.TableRow(
            decoration: pw.BoxDecoration(
              color: isEven ? PdfColors.grey50 : PdfColors.white,
            ),
            children: [
              _buildAccountingTableCell(
                  englishDateFormat.format(transaction.date), font),
              _buildAccountingTableCell(
                  _getTransactionTypeLabel(transaction.type), font),
              _buildAccountingTableCell(transaction.referenceId ?? '-', font),
              _buildAccountingTableCell('${entryNumber}A', font),
              _buildAccountingTableCell(
                  _buildEnhancedTransactionDescription(transaction, 'نقد'),
                  font),
              _buildAccountingTableCell(
                transaction.cashAmount < 0
                    ? '${_formatCurrency(transaction.cashAmount.abs())} ريال'
                    : '-',
                font,
                color: transaction.cashAmount < 0
                    ? PdfColors.red700
                    : PdfColors.black,
              ),
              _buildAccountingTableCell(
                transaction.cashAmount > 0
                    ? '${_formatCurrency(transaction.cashAmount)} ريال'
                    : '-',
                font,
                color: transaction.cashAmount > 0
                    ? PdfColors.green700
                    : PdfColors.black,
              ),
              _buildAccountingTableCell(
                '${_formatCurrency(transaction.runningCashBalance)} ريال',
                boldFont,
                color: PdfColors.blue800,
              ),
            ],
          ),
        );
      }

      // معاملة الديزل (إذا كانت موجودة)
      if (transaction.dieselAmount != 0) {
        tableRows.add(
          pw.TableRow(
            decoration: pw.BoxDecoration(
              color: isEven ? PdfColors.grey50 : PdfColors.white,
            ),
            children: [
              _buildAccountingTableCell(
                  englishDateFormat.format(transaction.date), font),
              _buildAccountingTableCell(
                  _getTransactionTypeLabel(transaction.type), font),
              _buildAccountingTableCell(transaction.referenceId ?? '-', font),
              _buildAccountingTableCell('${entryNumber}B', font),
              _buildAccountingTableCell(
                  _buildEnhancedTransactionDescription(transaction, 'ديزل'),
                  font),
              _buildAccountingTableCell(
                transaction.dieselAmount < 0
                    ? '${_formatCurrency(transaction.dieselAmount.abs())} لتر'
                    : '-',
                font,
                color: transaction.dieselAmount < 0
                    ? PdfColors.red700
                    : PdfColors.black,
              ),
              _buildAccountingTableCell(
                transaction.dieselAmount > 0
                    ? '${_formatCurrency(transaction.dieselAmount)} لتر'
                    : '-',
                font,
                color: transaction.dieselAmount > 0
                    ? PdfColors.green700
                    : PdfColors.black,
              ),
              _buildAccountingTableCell(
                '${_formatCurrency(transaction.runningDieselBalance)} لتر',
                boldFont,
                color: PdfColors.orange700,
              ),
            ],
          ),
        );
      }
    }

    // الرصيد الختامي للنقد
    tableRows.add(
      pw.TableRow(
        decoration: const pw.BoxDecoration(color: PdfColors.green50),
        children: [
          _buildAccountingTableCell(
              englishDateFormat.format(statement.toDate), font),
          _buildAccountingTableCell('رصيد ختامي', boldFont),
          _buildAccountingTableCell('-', font),
          _buildAccountingTableCell('-', font),
          _buildAccountingTableCell('[نقد] الرصيد في نهاية الفترة', font),
          _buildAccountingTableCell('-', font),
          _buildAccountingTableCell('-', font),
          _buildAccountingTableCell(
              '${_formatCurrency(statement.finalCashBalance)} ريال', boldFont,
              color: PdfColors.green800),
        ],
      ),
    );

    // الرصيد الختامي للديزل
    tableRows.add(
      pw.TableRow(
        decoration: const pw.BoxDecoration(color: PdfColors.green50),
        children: [
          _buildAccountingTableCell(
              englishDateFormat.format(statement.toDate), font),
          _buildAccountingTableCell('رصيد ختامي', boldFont),
          _buildAccountingTableCell('-', font),
          _buildAccountingTableCell('-', font),
          _buildAccountingTableCell('[ديزل] الرصيد في نهاية الفترة', font),
          _buildAccountingTableCell('-', font),
          _buildAccountingTableCell('-', font),
          _buildAccountingTableCell(
              '${_formatCurrency(statement.finalDieselBalance)} لتر', boldFont,
              color: PdfColors.green800),
        ],
      ),
    );

    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey400, width: 1),
      columnWidths: const {
        0: pw.FixedColumnWidth(65), // التاريخ
        1: pw.FixedColumnWidth(75), // نوع العمليه
        2: pw.FixedColumnWidth(65), // رقم المستند
        3: pw.FixedColumnWidth(55), // رقم القيد
        4: pw.FlexColumnWidth(3), // الوصف (أوسع)
        5: pw.FixedColumnWidth(75), // مدين
        6: pw.FixedColumnWidth(75), // دائن
        7: pw.FixedColumnWidth(85), // الرصيد
      },
      children: tableRows,
    );
  }

  /// بناء رأس عمود الجدول
  pw.Widget _buildTableHeader(String text, pw.Font boldFont) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          font: boldFont,
          fontSize: 12,
          color: PdfColors.white,
        ),
        textAlign: pw.TextAlign.center,
      ),
    );
  }

  /// بناء خلية الجدول
  pw.Widget _buildTableCell(
    String text,
    pw.Font font, {
    double fontSize = 11,
    PdfColor? color,
    int maxLines = 1,
  }) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(6),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          font: font,
          fontSize: fontSize,
          color: color ?? PdfColors.black,
        ),
        textAlign: pw.TextAlign.center,
        maxLines: maxLines,
      ),
    );
  }

  /// بناء رأس جدول محسن
  pw.Widget _buildEnhancedTableHeader(String text, pw.Font boldFont) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(10),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          font: boldFont,
          fontSize: 11,
          color: PdfColors.white,
        ),
        textAlign: pw.TextAlign.center,
      ),
    );
  }

  /// بناء خلية جدول محسنة
  pw.Widget _buildEnhancedTableCell(
    String text,
    pw.Font font, {
    double fontSize = 11,
    PdfColor? color,
  }) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          font: font,
          fontSize: fontSize,
          color: color ?? PdfColors.black,
        ),
        textAlign: pw.TextAlign.center,
      ),
    );
  }

  /// بناء خلية تفاصيل المعاملة
  pw.Widget _buildTransactionDetailsCell(
    AccountTransactionModel transaction,
    pw.Font font,
    pw.Font boldFont,
  ) {
    final icon = _getTransactionIcon(transaction.type);
    final typeLabel = _getTransactionTypeLabel(transaction.type);

    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Row(
            children: [
              pw.Text(
                icon,
                style: const pw.TextStyle(fontSize: 14),
              ),
              pw.SizedBox(width: 4),
              pw.Text(
                typeLabel,
                style: pw.TextStyle(
                  font: boldFont,
                  fontSize: 10,
                  color: PdfColors.purple800,
                ),
              ),
            ],
          ),
          pw.SizedBox(height: 2),
          pw.Text(
            transaction.description,
            style: pw.TextStyle(
              font: font,
              fontSize: 9,
              color: PdfColors.grey700,
            ),
            maxLines: 2,
          ),
          if (transaction.farmName != null && transaction.farmName!.isNotEmpty)
            pw.Text(
              '🌾 ${transaction.farmName}',
              style: pw.TextStyle(
                font: font,
                fontSize: 8,
                color: PdfColors.green600,
              ),
            ),
          if (transaction.duration != null && transaction.duration! > 0)
            pw.Text(
              '⏱️ ${transaction.duration!.toStringAsFixed(1)} ساعة',
              style: pw.TextStyle(
                font: font,
                fontSize: 8,
                color: PdfColors.blue600,
              ),
            ),
        ],
      ),
    );
  }

  /// بناء خلية تغيير المبلغ مع الأسهم
  pw.Widget _buildAmountChangeCell(
    double amount,
    String unit,
    pw.Font font,
    pw.Font boldFont,
  ) {
    String arrow = '';
    PdfColor color = PdfColors.grey600;
    String text = '-';

    if (amount > 0) {
      arrow = '↗️';
      color = PdfColors.green700;
      text = '+${amount.toStringAsFixed(2)}';
    } else if (amount < 0) {
      arrow = '↘️';
      color = PdfColors.red700;
      text = amount.toStringAsFixed(2);
    }

    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Column(
        children: [
          if (amount != 0)
            pw.Text(
              arrow,
              style: const pw.TextStyle(fontSize: 16),
            ),
          pw.SizedBox(height: 2),
          pw.Text(
            text,
            style: pw.TextStyle(
              font: boldFont,
              fontSize: 11,
              color: color,
            ),
          ),
          if (amount != 0)
            pw.Text(
              unit,
              style: pw.TextStyle(
                font: font,
                fontSize: 8,
                color: PdfColors.grey600,
              ),
            ),
        ],
      ),
    );
  }

  /// بناء خلية الرصيد
  pw.Widget _buildBalanceCell(
    double balance,
    String unit,
    pw.Font font,
    pw.Font boldFont,
    PdfColor color,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      decoration: pw.BoxDecoration(
        color: color.shade(0.1),
        borderRadius: pw.BorderRadius.circular(4),
      ),
      child: pw.Column(
        children: [
          pw.Text(
            balance.toStringAsFixed(2),
            style: pw.TextStyle(
              font: boldFont,
              fontSize: 12,
              color: color,
            ),
          ),
          pw.Text(
            unit,
            style: pw.TextStyle(
              font: font,
              fontSize: 8,
              color: PdfColors.grey600,
            ),
          ),
        ],
      ),
    );
  }

  /// الحصول على أيقونة المعاملة
  String _getTransactionIcon(TransactionType type) {
    switch (type) {
      case TransactionType.irrigation:
        return '💧';
      case TransactionType.cashPayment:
        return '💰';
      case TransactionType.dieselPayment:
        return '⛽';
      case TransactionType.transfer:
        return '🔄';
      case TransactionType.adjustment:
        return '⚖️';
    }
  }

  /// بناء قسم الأرصدة النهائية المحسن
  pw.Widget _buildFinalBalanceSection(
      AccountStatementModel statement, pw.Font boldFont, pw.Font font) {
    return pw.Container(
      margin: const pw.EdgeInsets.symmetric(vertical: 8),
      child: pw.Column(
        children: [
          // عنوان الملخص
          pw.Container(
            padding: const pw.EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: const pw.BoxDecoration(
              color: PdfColors.orange800,
              borderRadius: pw.BorderRadius.only(
                topLeft: pw.Radius.circular(8),
                topRight: pw.Radius.circular(8),
              ),
            ),
            child: pw.Row(
              children: [
                pw.Text(
                  '📈 ملخص الحساب والتغييرات',
                  style: pw.TextStyle(
                    font: boldFont,
                    fontSize: 16,
                    color: PdfColors.white,
                  ),
                ),
              ],
            ),
          ),
          // محتوى الملخص
          pw.Container(
            padding: const pw.EdgeInsets.all(20),
            decoration: pw.BoxDecoration(
              color: PdfColors.orange50,
              borderRadius: const pw.BorderRadius.only(
                bottomLeft: pw.Radius.circular(8),
                bottomRight: pw.Radius.circular(8),
              ),
              border: pw.Border.all(color: PdfColors.orange200, width: 1),
            ),
            child: pw.Column(
              children: [
                // إجماليات الحركة
                pw.Text(
                  '💹 إجماليات الحركة خلال الفترة',
                  style: pw.TextStyle(
                    font: boldFont,
                    fontSize: 14,
                    color: PdfColors.orange900,
                  ),
                ),
                pw.SizedBox(height: 12),
                pw.Row(
                  children: [
                    pw.Expanded(
                      child: _buildSummaryCard(
                        '📈 الداخل',
                        [
                          (
                            '💰 نقدي',
                            statement.totalCashIn,
                            'ريال',
                            PdfColors.green700
                          ),
                          (
                            '⛽ ديزل',
                            statement.totalDieselIn,
                            'لتر',
                            PdfColors.blue700
                          ),
                        ],
                        PdfColors.green50,
                        PdfColors.green200,
                        boldFont,
                        font,
                      ),
                    ),
                    pw.SizedBox(width: 16),
                    pw.Expanded(
                      child: _buildSummaryCard(
                        '📉 الخارج',
                        [
                          (
                            '💸 نقدي',
                            statement.totalCashOut,
                            'ريال',
                            PdfColors.red700
                          ),
                          (
                            '🛢️ ديزل',
                            statement.totalDieselOut,
                            'لتر',
                            PdfColors.orange700
                          ),
                        ],
                        PdfColors.red50,
                        PdfColors.red200,
                        boldFont,
                        font,
                      ),
                    ),
                  ],
                ),
                pw.SizedBox(height: 16),
                // خط فاصل
                pw.Container(
                  height: 2,
                  decoration: pw.BoxDecoration(
                    gradient: const pw.LinearGradient(
                      colors: [
                        PdfColors.orange300,
                        PdfColors.orange600,
                        PdfColors.orange300
                      ],
                    ),
                    borderRadius: pw.BorderRadius.circular(1),
                  ),
                ),
                pw.SizedBox(height: 16),
                // الأرصدة النهائية
                pw.Text(
                  '🏦 الأرصدة النهائية (نهاية الفترة)',
                  style: pw.TextStyle(
                    font: boldFont,
                    fontSize: 14,
                    color: PdfColors.orange900,
                  ),
                ),
                pw.SizedBox(height: 12),
                pw.Row(
                  children: [
                    pw.Expanded(
                      child: _buildFinalBalanceCard(
                        '💵',
                        'الرصيد النقدي النهائي',
                        statement.finalCashBalance,
                        'ريال',
                        PdfColors.green700,
                        boldFont,
                        font,
                      ),
                    ),
                    pw.SizedBox(width: 16),
                    pw.Expanded(
                      child: _buildFinalBalanceCard(
                        '🛢️',
                        'رصيد الديزل النهائي',
                        statement.finalDieselBalance,
                        'لتر',
                        PdfColors.blue700,
                        boldFont,
                        font,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء عنصر ملخص
  pw.Widget _buildSummaryItem(String title, double amount, String unit,
      pw.Font boldFont, pw.Font font, PdfColor color) {
    return pw.Column(
      children: [
        pw.Text(title,
            style: pw.TextStyle(
                font: font, fontSize: 11, color: PdfColors.grey700)),
        pw.SizedBox(height: 4),
        pw.Text(
          '${amount.toStringAsFixed(2)} $unit',
          style: pw.TextStyle(font: boldFont, fontSize: 13, color: color),
        ),
      ],
    );
  }

  /// بناء بطاقة ملخص
  pw.Widget _buildSummaryCard(
    String title,
    List<(String, double, String, PdfColor)> items,
    PdfColor backgroundColor,
    PdfColor borderColor,
    pw.Font boldFont,
    pw.Font font,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(12),
      decoration: pw.BoxDecoration(
        color: backgroundColor,
        borderRadius: pw.BorderRadius.circular(8),
        border: pw.Border.all(color: borderColor, width: 1),
      ),
      child: pw.Column(
        children: [
          pw.Text(
            title,
            style: pw.TextStyle(
              font: boldFont,
              fontSize: 12,
              color: PdfColors.grey800,
            ),
          ),
          pw.SizedBox(height: 8),
          ...items.map((item) => pw.Padding(
                padding: const pw.EdgeInsets.symmetric(vertical: 2),
                child: pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  children: [
                    pw.Text(
                      item.$1,
                      style: pw.TextStyle(
                        font: font,
                        fontSize: 10,
                        color: PdfColors.grey700,
                      ),
                    ),
                    pw.Text(
                      '${item.$2.toStringAsFixed(2)} ${item.$3}',
                      style: pw.TextStyle(
                        font: boldFont,
                        fontSize: 10,
                        color: item.$4,
                      ),
                    ),
                  ],
                ),
              )),
        ],
      ),
    );
  }

  /// بناء بطاقة الرصيد النهائي
  pw.Widget _buildFinalBalanceCard(
    String icon,
    String title,
    double amount,
    String unit,
    PdfColor color,
    pw.Font boldFont,
    pw.Font font,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        gradient: pw.LinearGradient(
          colors: [color.shade(0.1), color.shade(0.05)],
          begin: pw.Alignment.topLeft,
          end: pw.Alignment.bottomRight,
        ),
        borderRadius: pw.BorderRadius.circular(12),
        border: pw.Border.all(color: color.shade(0.3), width: 2),
      ),
      child: pw.Column(
        children: [
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.center,
            children: [
              pw.Text(
                icon,
                style: const pw.TextStyle(fontSize: 24),
              ),
              pw.SizedBox(width: 8),
              pw.Expanded(
                child: pw.Text(
                  title,
                  style: pw.TextStyle(
                    font: font,
                    fontSize: 11,
                    color: PdfColors.grey700,
                  ),
                  textAlign: pw.TextAlign.center,
                ),
              ),
            ],
          ),
          pw.SizedBox(height: 12),
          pw.Text(
            '${amount.toStringAsFixed(2)} $unit',
            style: pw.TextStyle(
              font: boldFont,
              fontSize: 18,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء قسم التوقيع المحسن
  pw.Widget _buildSignatureSection(pw.Font font) {
    return pw.Container(
      margin: const pw.EdgeInsets.only(top: 20),
      child: pw.Column(
        children: [
          // خط فاصل
          pw.Container(
            height: 1,
            color: PdfColors.grey300,
            margin: const pw.EdgeInsets.symmetric(vertical: 16),
          ),
          // قسم التوقيع
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              // توقيع المسؤول
              pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    '✍️ توقيع المسؤول',
                    style: pw.TextStyle(
                      font: font,
                      fontSize: 12,
                      color: PdfColors.grey700,
                    ),
                  ),
                  pw.SizedBox(height: 24),
                  pw.Container(
                    width: 150,
                    height: 2,
                    decoration: pw.BoxDecoration(
                      color: PdfColors.grey400,
                      borderRadius: pw.BorderRadius.circular(1),
                    ),
                  ),
                  pw.SizedBox(height: 4),
                  pw.Text(
                    'الاسم: ________________',
                    style: pw.TextStyle(
                      font: font,
                      fontSize: 10,
                      color: PdfColors.grey600,
                    ),
                  ),
                ],
              ),
              // ختم المؤسسة
              pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.end,
                children: [
                  pw.Text(
                    '🏢 ختم المؤسسة',
                    style: pw.TextStyle(
                      font: font,
                      fontSize: 12,
                      color: PdfColors.grey700,
                    ),
                  ),
                  pw.SizedBox(height: 8),
                  pw.Container(
                    width: 80,
                    height: 80,
                    decoration: pw.BoxDecoration(
                      border: pw.Border.all(color: PdfColors.grey400, width: 2),
                      borderRadius: pw.BorderRadius.circular(8),
                    ),
                    child: pw.Center(
                      child: pw.Text(
                        'الختم\nالرسمي',
                        style: pw.TextStyle(
                          font: font,
                          fontSize: 8,
                          color: PdfColors.grey500,
                        ),
                        textAlign: pw.TextAlign.center,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
          pw.SizedBox(height: 16),
          // تذييل احترافي
          pw.Container(
            padding: const pw.EdgeInsets.all(12),
            decoration: pw.BoxDecoration(
              color: PdfColors.grey100,
              borderRadius: pw.BorderRadius.circular(6),
            ),
            child: pw.Column(
              children: [
                pw.Text(
                  '📞 للاستفسارات: 966-11-1234567 | 📧 <EMAIL> | 🌐 www.farm.com',
                  style: pw.TextStyle(
                    font: font,
                    fontSize: 9,
                    color: PdfColors.grey600,
                  ),
                  textAlign: pw.TextAlign.center,
                ),
                pw.SizedBox(height: 4),
                pw.Text(
                  'تم إنشاء هذا التقرير بواسطة نظام إدارة المزارع الذكي',
                  style: pw.TextStyle(
                    font: font,
                    fontSize: 8,
                    color: PdfColors.grey500,
                  ),
                  textAlign: pw.TextAlign.center,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// تنسيق وصف المعاملة
  String _formatDescription(AccountTransactionModel transaction) {
    String description = transaction.description;
    if (transaction.farmName != null && transaction.farmName!.isNotEmpty) {
      description += ' - ${transaction.farmName}';
    }
    if (transaction.duration != null && transaction.duration! > 0) {
      description += ' (${transaction.duration!.toStringAsFixed(1)} ساعة)';
    }
    return description;
  }

  /// تنسيق المبلغ
  String _formatAmount(double amount) {
    if (amount == 0) return '-';
    return amount > 0
        ? '+${amount.toStringAsFixed(2)}'
        : amount.toStringAsFixed(2);
  }

  /// الحصول على لون المبلغ
  PdfColor _getAmountColor(double amount) {
    if (amount > 0) return PdfColors.green700;
    if (amount < 0) return PdfColors.red700;
    return PdfColors.grey600;
  }

  /// ترجمة نوع العملية
  String _getTransactionTypeLabel(TransactionType type) {
    switch (type) {
      case TransactionType.irrigation:
        return 'تسقية';
      case TransactionType.cashPayment:
        return 'دفعة نقدية';
      case TransactionType.dieselPayment:
        return 'دفعة ديزل';
      case TransactionType.transfer:
        return 'تحويل';
      case TransactionType.adjustment:
        return 'تسوية';
    }
  }

  /// إنشاء رقم حساب فريد
  String _generateAccountNumber(String clientId) {
    final baseNumber = '122010';
    final paddedId = clientId.padLeft(3, '0');
    return '$baseNumber$paddedId';
  }

  /// إنشاء رقم قيد محاسبي
  String _generateEntryNumber(int index) {
    return index.toString().padLeft(4, '0');
  }

  /// تنسيق العملة
  String _formatCurrency(double amount) {
    final formatter = NumberFormat('#,##0.00', 'en');
    return formatter.format(amount);
  }

  /// بناء وصف المعاملة
  String _buildTransactionDescription(AccountTransactionModel transaction) {
    String description = transaction.description;
    if (transaction.farmName != null && transaction.farmName!.isNotEmpty) {
      description += ' - ${transaction.farmName}';
    }
    if (transaction.duration != null && transaction.duration! > 0) {
      description += ' (${transaction.duration!.toStringAsFixed(1)} ساعة)';
    }
    if (transaction.notes != null && transaction.notes!.isNotEmpty) {
      description += ' - ${transaction.notes}';
    }
    return description;
  }

  /// بناء وصف المعاملة المحسن بدون رموز خاصة
  String _buildEnhancedTransactionDescription(
      AccountTransactionModel transaction, String type) {
    String typeLabel = '';
    String operationLabel = '';

    // تحديد نوع المعاملة
    if (type == 'نقد') {
      typeLabel = '[نقد]';
    } else if (type == 'ديزل') {
      typeLabel = '[ديزل]';
    }

    // تحديد نوع العملية
    switch (transaction.type) {
      case TransactionType.irrigation:
        operationLabel = 'تسقية';
        break;
      case TransactionType.cashPayment:
        operationLabel = 'دفعة نقدية';
        break;
      case TransactionType.dieselPayment:
        operationLabel = 'دفعة ديزل';
        break;
      case TransactionType.transfer:
        operationLabel = 'تحويل';
        break;
      case TransactionType.adjustment:
        operationLabel = 'تسوية';
        break;
    }

    String description = '$typeLabel $operationLabel';

    // إضافة تفاصيل المزرعة
    if (transaction.farmName != null && transaction.farmName!.isNotEmpty) {
      description += ' - مزرعة ${transaction.farmName}';
    }

    // إضافة مدة التسقية
    if (transaction.duration != null && transaction.duration! > 0) {
      description += ' (${transaction.duration!.toStringAsFixed(1)} ساعة)';
    }

    // تحسين وصف التحويل
    if (transaction.type == TransactionType.transfer) {
      if (transaction.notes != null && transaction.notes!.isNotEmpty) {
        // تحديد اتجاه التحويل
        if (transaction.cashAmount < 0 || transaction.dieselAmount < 0) {
          description = '$typeLabel تحويل الى: ${transaction.notes}';
        } else {
          description = '$typeLabel تحويل من: ${transaction.notes}';
        }
      } else {
        description = '$typeLabel تحويل الى عميل آخر';
      }
    } else if (transaction.notes != null && transaction.notes!.isNotEmpty) {
      description += ' - ${transaction.notes}';
    }

    // إضافة وصف المعاملة الأساسي إذا كان مختلفاً
    if (transaction.description.isNotEmpty &&
        !transaction.description.contains(operationLabel)) {
      description += ' - ${transaction.description}';
    }

    return description;
  }

  /// الحصول على تاريخ إنشاء الحساب
  String _getAccountCreationDate(AccountStatementModel statement) {
    // يمكن تحسين هذا ليأخذ التاريخ الفعلي من قاعدة البيانات
    // حالياً سنستخدم تاريخ بداية الفترة كمثال
    final englishDateFormat = DateFormat('dd-MM-yyyy', 'en');
    return englishDateFormat.format(statement.fromDate);
  }

  /// بناء رأس الجدول المحاسبي
  pw.Widget _buildAccountingTableHeader(String text, pw.Font boldFont) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      decoration: const pw.BoxDecoration(
        border: pw.Border(
          bottom: pw.BorderSide(color: PdfColors.grey600, width: 2),
        ),
      ),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          font: boldFont,
          fontSize: 12,
          color: PdfColors.black,
        ),
        textAlign: pw.TextAlign.center,
      ),
    );
  }

  /// بناء خلية الجدول المحاسبي
  pw.Widget _buildAccountingTableCell(
    String text,
    pw.Font font, {
    PdfColor? color,
  }) {
    return pw.Container(
      padding: const pw.EdgeInsets.symmetric(horizontal: 4, vertical: 6),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          font: font,
          fontSize: 9,
          color: color ?? PdfColors.black,
        ),
        textAlign: pw.TextAlign.center,
        maxLines: 3,
        overflow: pw.TextOverflow.clip,
      ),
    );
  }

  /// بناء تذييل الصفحة
  pw.Widget _buildFooter(pw.Font font) {
    final now = DateTime.now();
    final printTime = DateFormat('dd-MM-yyyy HH:mm', 'en').format(now);

    return pw.Container(
      padding: const pw.EdgeInsets.symmetric(vertical: 16),
      decoration: const pw.BoxDecoration(
        border: pw.Border(
          top: pw.BorderSide(color: PdfColors.grey400, width: 1),
        ),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          // اسم الشركة (يسار)
          pw.Text(
            'مياه المراوح',
            style: pw.TextStyle(
              font: font,
              fontSize: 10,
              color: PdfColors.grey600,
            ),
          ),
          // تاريخ الطباعة (وسط)
          pw.Text(
            'تاريخ الطباعة: $printTime',
            style: pw.TextStyle(
              font: font,
              fontSize: 10,
              color: PdfColors.grey600,
            ),
          ),
          // رقم الصفحة (يمين)
          pw.Text(
            'صفحة 1 من 1',
            style: pw.TextStyle(
              font: font,
              fontSize: 10,
              color: PdfColors.grey600,
            ),
          ),
        ],
      ),
    );
  }
}

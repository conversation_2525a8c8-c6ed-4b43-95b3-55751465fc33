import 'dart:io';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:untitled/data/models/client_model.dart';
import 'package:untitled/data/models/farm_model.dart';
import 'package:untitled/data/models/irrigation_model.dart';
import 'package:untitled/data/models/payment_model.dart';
import 'package:untitled/data/models/account_statement_model.dart';
import 'package:flutter/services.dart' show rootBundle;

/// PDF Service - معطل مؤقتاً لتسريع البناء
/// يمكن تفعيله لاحقاً بإزالة التعليقات وإضافة التبعيات
class PdfService {
  // إنشاء فاتورة تسقية - معطل مؤقتاً لتسريع البناء
  Future<File> createIrrigationInvoice({
    required ClientModel client,
    required FarmModel farm,
    required IrrigationModel irrigation,
    required double cashBalance,
    required double dieselBalance,
  }) async {
    // PDF functionality disabled for faster build
    throw UnimplementedError(
        'PDF functionality is temporarily disabled for faster build');
  }

  // إنشاء فاتورة دفع - معطل مؤقتاً لتسريع البناء
  Future<File> createPaymentInvoice({
    required ClientModel client,
    required FarmModel farm,
    required PaymentModel payment,
    required double cashBalance,
    required double dieselBalance,
  }) async {
    // PDF functionality disabled for faster build
    throw UnimplementedError(
        'PDF functionality is temporarily disabled for faster build');
  }

  // إنشاء تقرير - معطل مؤقتاً لتسريع البناء
  Future<File> createReport({
    required String title,
    required String subtitle,
    required List<Map<String, dynamic>> data,
    required List<String> columns,
    required List<String> columnTitles,
  }) async {
    // PDF functionality disabled for faster build
    throw UnimplementedError(
        'PDF functionality is temporarily disabled for faster build');
  }

  /// توليد كشف حساب عميل PDF احترافي بالكامل بالعربية
  Future<File> createClientStatementPdf({
    required AccountStatementModel statement,
    required String logoAssetPath,
  }) async {
    final pdf = pw.Document();

    // تحميل الخطوط مع معالجة الأخطاء
    pw.Font font;
    pw.Font boldFont;

    try {
      final fontData =
          await rootBundle.load('assets/fonts/static/Cairo-Regular.ttf');
      font = pw.Font.ttf(fontData);
      final boldFontData =
          await rootBundle.load('assets/fonts/static/Cairo-Bold.ttf');
      boldFont = pw.Font.ttf(boldFontData);
    } catch (e) {
      // استخدام الخط الافتراضي في حالة عدم وجود الخطوط العربية
      print('تعذر تحميل الخطوط العربية، سيتم استخدام الخط الافتراضي: $e');
      font = pw.Font.helvetica();
      boldFont = pw.Font.helveticaBold();
    }

    final dateFormat = DateFormat('yyyy/MM/dd', 'ar');

    pdf.addPage(
      pw.MultiPage(
        theme: pw.ThemeData.withFont(
          base: font,
          bold: boldFont,
        ),
        textDirection: pw.TextDirection.rtl,
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(20),
        build: (context) => [
          // رأس الصفحة المحسن
          _buildPdfHeader(statement, dateFormat, boldFont, font),
          pw.SizedBox(height: 20),

          // معلومات الرصيد الابتدائي
          _buildInitialBalanceSection(statement, boldFont, font),
          pw.SizedBox(height: 16),
          // جدول المعاملات المحسن
          _buildTransactionsTable(statement, dateFormat, boldFont, font),
          pw.SizedBox(height: 16),

          // ملخص الأرصدة النهائية
          _buildFinalBalanceSection(statement, boldFont, font),
          pw.SizedBox(height: 24),

          // توقيع وختام
          _buildSignatureSection(font),
        ],
      ),
    );

    final output = await getTemporaryDirectory();
    final file = File(
        '${output.path}/client_statement_${statement.clientId}_${DateTime.now().millisecondsSinceEpoch}.pdf');
    await file.writeAsBytes(await pdf.save());
    return file;
  }

  /// بناء رأس الصفحة المحسن مع معلومات الشركة
  pw.Widget _buildPdfHeader(AccountStatementModel statement,
      DateFormat dateFormat, pw.Font boldFont, pw.Font font) {
    final reportNumber =
        'RPT-${DateTime.now().millisecondsSinceEpoch.toString().substring(8)}';

    return pw.Container(
      decoration: pw.BoxDecoration(
        gradient: pw.LinearGradient(
          colors: [PdfColors.blue800, PdfColors.blue600],
          begin: pw.Alignment.topLeft,
          end: pw.Alignment.bottomRight,
        ),
        borderRadius: pw.BorderRadius.circular(12),
      ),
      child: pw.Column(
        children: [
          // رأس الشركة
          pw.Container(
            padding: const pw.EdgeInsets.all(20),
            child: pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                // معلومات الشركة
                pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text(
                      'شركة إدارة المزارع',
                      style: pw.TextStyle(
                        font: boldFont,
                        fontSize: 18,
                        color: PdfColors.white,
                      ),
                    ),
                    pw.SizedBox(height: 4),
                    pw.Text(
                      'هاتف: 966-11-1234567 | البريد: <EMAIL>',
                      style: pw.TextStyle(
                        font: font,
                        fontSize: 10,
                        color: PdfColors.blue100,
                      ),
                    ),
                  ],
                ),
                // رقم التقرير والتاريخ
                pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.end,
                  children: [
                    pw.Text(
                      'رقم التقرير: $reportNumber',
                      style: pw.TextStyle(
                        font: font,
                        fontSize: 10,
                        color: PdfColors.blue100,
                      ),
                    ),
                    pw.SizedBox(height: 2),
                    pw.Text(
                      'تاريخ الإنشاء: ${DateFormat('yyyy/MM/dd HH:mm').format(DateTime.now())}',
                      style: pw.TextStyle(
                        font: font,
                        fontSize: 10,
                        color: PdfColors.blue100,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          // عنوان التقرير ومعلومات العميل
          pw.Container(
            padding:
                const pw.EdgeInsets.symmetric(horizontal: 20, vertical: 16),
            decoration: const pw.BoxDecoration(
              color: PdfColors.white,
              borderRadius: pw.BorderRadius.only(
                bottomLeft: pw.Radius.circular(12),
                bottomRight: pw.Radius.circular(12),
              ),
            ),
            child: pw.Column(
              children: [
                pw.Text(
                  '📊 كشف حساب العميل',
                  style: pw.TextStyle(
                    font: boldFont,
                    fontSize: 26,
                    color: PdfColors.blue900,
                  ),
                ),
                pw.SizedBox(height: 12),
                pw.Container(
                  padding: const pw.EdgeInsets.all(12),
                  decoration: pw.BoxDecoration(
                    color: PdfColors.blue50,
                    borderRadius: pw.BorderRadius.circular(8),
                    border: pw.Border.all(color: PdfColors.blue200, width: 1),
                  ),
                  child: pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          pw.Text(
                            '👤 اسم العميل',
                            style: pw.TextStyle(
                              font: font,
                              fontSize: 12,
                              color: PdfColors.grey600,
                            ),
                          ),
                          pw.SizedBox(height: 4),
                          pw.Text(
                            statement.clientName,
                            style: pw.TextStyle(
                              font: boldFont,
                              fontSize: 16,
                              color: PdfColors.blue900,
                            ),
                          ),
                        ],
                      ),
                      pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.end,
                        children: [
                          pw.Text(
                            '📅 فترة التقرير',
                            style: pw.TextStyle(
                              font: font,
                              fontSize: 12,
                              color: PdfColors.grey600,
                            ),
                          ),
                          pw.SizedBox(height: 4),
                          pw.Text(
                            '${dateFormat.format(statement.fromDate)} - ${dateFormat.format(statement.toDate)}',
                            style: pw.TextStyle(
                              font: boldFont,
                              fontSize: 14,
                              color: PdfColors.blue900,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء قسم الرصيد الابتدائي المحسن
  pw.Widget _buildInitialBalanceSection(
      AccountStatementModel statement, pw.Font boldFont, pw.Font font) {
    return pw.Container(
      margin: const pw.EdgeInsets.symmetric(vertical: 8),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          // عنوان القسم
          pw.Container(
            padding: const pw.EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: pw.BoxDecoration(
              color: PdfColors.indigo800,
              borderRadius: const pw.BorderRadius.only(
                topLeft: pw.Radius.circular(8),
                topRight: pw.Radius.circular(8),
              ),
            ),
            child: pw.Row(
              children: [
                pw.Text(
                  '💰 الرصيد السابق (بداية الفترة)',
                  style: pw.TextStyle(
                    font: boldFont,
                    fontSize: 16,
                    color: PdfColors.white,
                  ),
                ),
              ],
            ),
          ),
          // محتوى الأرصدة
          pw.Container(
            padding: const pw.EdgeInsets.all(16),
            decoration: pw.BoxDecoration(
              color: PdfColors.indigo50,
              borderRadius: const pw.BorderRadius.only(
                bottomLeft: pw.Radius.circular(8),
                bottomRight: pw.Radius.circular(8),
              ),
              border: pw.Border.all(color: PdfColors.indigo200, width: 1),
            ),
            child: pw.Row(
              children: [
                // الرصيد النقدي
                pw.Expanded(
                  child: _buildEnhancedBalanceCard(
                    '💵',
                    'الرصيد النقدي',
                    statement.initialCashBalance,
                    'ريال',
                    PdfColors.green700,
                    PdfColors.green50,
                    boldFont,
                    font,
                  ),
                ),
                pw.SizedBox(width: 16),
                // رصيد الديزل
                pw.Expanded(
                  child: _buildEnhancedBalanceCard(
                    '⛽',
                    'رصيد الديزل',
                    statement.initialDieselBalance,
                    'لتر',
                    PdfColors.blue700,
                    PdfColors.blue50,
                    boldFont,
                    font,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة رصيد محسنة
  pw.Widget _buildEnhancedBalanceCard(
    String icon,
    String title,
    double amount,
    String unit,
    PdfColor amountColor,
    PdfColor backgroundColor,
    pw.Font boldFont,
    pw.Font font,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(12),
      decoration: pw.BoxDecoration(
        color: backgroundColor,
        borderRadius: pw.BorderRadius.circular(8),
        border: pw.Border.all(color: amountColor.shade(0.3), width: 1),
      ),
      child: pw.Column(
        children: [
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.center,
            children: [
              pw.Text(
                icon,
                style: const pw.TextStyle(fontSize: 20),
              ),
              pw.SizedBox(width: 8),
              pw.Text(
                title,
                style: pw.TextStyle(
                  font: font,
                  fontSize: 12,
                  color: PdfColors.grey700,
                ),
              ),
            ],
          ),
          pw.SizedBox(height: 8),
          pw.Text(
            '${amount.toStringAsFixed(2)} $unit',
            style: pw.TextStyle(
              font: boldFont,
              fontSize: 16,
              color: amountColor,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء عنصر رصيد
  pw.Widget _buildBalanceItem(String title, double amount, String unit,
      pw.Font boldFont, pw.Font font, PdfColor color) {
    return pw.Column(
      children: [
        pw.Text(title,
            style: pw.TextStyle(
                font: font, fontSize: 12, color: PdfColors.grey700)),
        pw.SizedBox(height: 4),
        pw.Text(
          '${amount.toStringAsFixed(2)} $unit',
          style: pw.TextStyle(font: boldFont, fontSize: 14, color: color),
        ),
      ],
    );
  }

  /// بناء جدول المعاملات المحسن
  pw.Widget _buildTransactionsTable(AccountStatementModel statement,
      DateFormat dateFormat, pw.Font boldFont, pw.Font font) {
    if (statement.transactions.isEmpty) {
      return pw.Container(
        padding: const pw.EdgeInsets.all(20),
        decoration: pw.BoxDecoration(
          color: PdfColors.grey100,
          borderRadius: pw.BorderRadius.circular(8),
        ),
        child: pw.Center(
          child: pw.Text(
            'لا توجد معاملات في هذه الفترة',
            style: pw.TextStyle(
                font: font, fontSize: 14, color: PdfColors.grey600),
          ),
        ),
      );
    }

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        // عنوان الجدول
        pw.Container(
          padding: const pw.EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: const pw.BoxDecoration(
            color: PdfColors.purple800,
            borderRadius: pw.BorderRadius.only(
              topLeft: pw.Radius.circular(8),
              topRight: pw.Radius.circular(8),
            ),
          ),
          child: pw.Row(
            children: [
              pw.Text(
                '📊 سجل المعاملات والتغييرات',
                style: pw.TextStyle(
                  font: boldFont,
                  fontSize: 16,
                  color: PdfColors.white,
                ),
              ),
            ],
          ),
        ),
        // الجدول المحسن
        pw.Table(
          border: pw.TableBorder.all(color: PdfColors.purple300, width: 1),
          columnWidths: const {
            0: pw.FixedColumnWidth(50), // التاريخ
            1: pw.FlexColumnWidth(2.5), // البيان والتفاصيل
            2: pw.FixedColumnWidth(80), // التغيير النقدي
            3: pw.FixedColumnWidth(80), // التغيير في الديزل
            4: pw.FixedColumnWidth(90), // الرصيد النقدي الجديد
            5: pw.FixedColumnWidth(90), // رصيد الديزل الجديد
          },
          children: [
            // رأس الجدول المحسن
            pw.TableRow(
              decoration: const pw.BoxDecoration(color: PdfColors.purple700),
              children: [
                _buildEnhancedTableHeader('📅 التاريخ', boldFont),
                _buildEnhancedTableHeader('📝 تفاصيل المعاملة', boldFont),
                _buildEnhancedTableHeader('💰 التغيير النقدي', boldFont),
                _buildEnhancedTableHeader('⛽ التغيير في الديزل', boldFont),
                _buildEnhancedTableHeader('💵 الرصيد النقدي', boldFont),
                _buildEnhancedTableHeader('🛢️ رصيد الديزل', boldFont),
              ],
            ),
            // صفوف البيانات المحسنة
            ...statement.transactions.asMap().entries.map((entry) {
              final index = entry.key;
              final transaction = entry.value;
              final isEven = index % 2 == 0;

              return pw.TableRow(
                decoration: pw.BoxDecoration(
                  color: isEven ? PdfColors.purple50 : PdfColors.white,
                ),
                children: [
                  _buildEnhancedTableCell(
                    dateFormat.format(transaction.date),
                    font,
                    fontSize: 10,
                  ),
                  _buildTransactionDetailsCell(transaction, font, boldFont),
                  _buildAmountChangeCell(
                    transaction.cashAmount,
                    'ريال',
                    font,
                    boldFont,
                  ),
                  _buildAmountChangeCell(
                    transaction.dieselAmount,
                    'لتر',
                    font,
                    boldFont,
                  ),
                  _buildBalanceCell(
                    transaction.runningCashBalance,
                    'ريال',
                    font,
                    boldFont,
                    PdfColors.green700,
                  ),
                  _buildBalanceCell(
                    transaction.runningDieselBalance,
                    'لتر',
                    font,
                    boldFont,
                    PdfColors.blue700,
                  ),
                ],
              );
            }),
          ],
        ),
      ],
    );
  }

  /// بناء رأس عمود الجدول
  pw.Widget _buildTableHeader(String text, pw.Font boldFont) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          font: boldFont,
          fontSize: 12,
          color: PdfColors.white,
        ),
        textAlign: pw.TextAlign.center,
      ),
    );
  }

  /// بناء خلية الجدول
  pw.Widget _buildTableCell(
    String text,
    pw.Font font, {
    double fontSize = 11,
    PdfColor? color,
    int maxLines = 1,
  }) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(6),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          font: font,
          fontSize: fontSize,
          color: color ?? PdfColors.black,
        ),
        textAlign: pw.TextAlign.center,
        maxLines: maxLines,
      ),
    );
  }

  /// بناء رأس جدول محسن
  pw.Widget _buildEnhancedTableHeader(String text, pw.Font boldFont) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(10),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          font: boldFont,
          fontSize: 11,
          color: PdfColors.white,
        ),
        textAlign: pw.TextAlign.center,
      ),
    );
  }

  /// بناء خلية جدول محسنة
  pw.Widget _buildEnhancedTableCell(
    String text,
    pw.Font font, {
    double fontSize = 11,
    PdfColor? color,
  }) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          font: font,
          fontSize: fontSize,
          color: color ?? PdfColors.black,
        ),
        textAlign: pw.TextAlign.center,
      ),
    );
  }

  /// بناء خلية تفاصيل المعاملة
  pw.Widget _buildTransactionDetailsCell(
    AccountTransactionModel transaction,
    pw.Font font,
    pw.Font boldFont,
  ) {
    final icon = _getTransactionIcon(transaction.type);
    final typeLabel = _getTransactionTypeLabel(transaction.type);

    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Row(
            children: [
              pw.Text(
                icon,
                style: const pw.TextStyle(fontSize: 14),
              ),
              pw.SizedBox(width: 4),
              pw.Text(
                typeLabel,
                style: pw.TextStyle(
                  font: boldFont,
                  fontSize: 10,
                  color: PdfColors.purple800,
                ),
              ),
            ],
          ),
          pw.SizedBox(height: 2),
          pw.Text(
            transaction.description,
            style: pw.TextStyle(
              font: font,
              fontSize: 9,
              color: PdfColors.grey700,
            ),
            maxLines: 2,
          ),
          if (transaction.farmName != null && transaction.farmName!.isNotEmpty)
            pw.Text(
              '🌾 ${transaction.farmName}',
              style: pw.TextStyle(
                font: font,
                fontSize: 8,
                color: PdfColors.green600,
              ),
            ),
          if (transaction.duration != null && transaction.duration! > 0)
            pw.Text(
              '⏱️ ${transaction.duration!.toStringAsFixed(1)} ساعة',
              style: pw.TextStyle(
                font: font,
                fontSize: 8,
                color: PdfColors.blue600,
              ),
            ),
        ],
      ),
    );
  }

  /// بناء خلية تغيير المبلغ مع الأسهم
  pw.Widget _buildAmountChangeCell(
    double amount,
    String unit,
    pw.Font font,
    pw.Font boldFont,
  ) {
    String arrow = '';
    PdfColor color = PdfColors.grey600;
    String text = '-';

    if (amount > 0) {
      arrow = '↗️';
      color = PdfColors.green700;
      text = '+${amount.toStringAsFixed(2)}';
    } else if (amount < 0) {
      arrow = '↘️';
      color = PdfColors.red700;
      text = amount.toStringAsFixed(2);
    }

    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Column(
        children: [
          if (amount != 0)
            pw.Text(
              arrow,
              style: const pw.TextStyle(fontSize: 16),
            ),
          pw.SizedBox(height: 2),
          pw.Text(
            text,
            style: pw.TextStyle(
              font: boldFont,
              fontSize: 11,
              color: color,
            ),
          ),
          if (amount != 0)
            pw.Text(
              unit,
              style: pw.TextStyle(
                font: font,
                fontSize: 8,
                color: PdfColors.grey600,
              ),
            ),
        ],
      ),
    );
  }

  /// بناء خلية الرصيد
  pw.Widget _buildBalanceCell(
    double balance,
    String unit,
    pw.Font font,
    pw.Font boldFont,
    PdfColor color,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      decoration: pw.BoxDecoration(
        color: color.shade(0.1),
        borderRadius: pw.BorderRadius.circular(4),
      ),
      child: pw.Column(
        children: [
          pw.Text(
            balance.toStringAsFixed(2),
            style: pw.TextStyle(
              font: boldFont,
              fontSize: 12,
              color: color,
            ),
          ),
          pw.Text(
            unit,
            style: pw.TextStyle(
              font: font,
              fontSize: 8,
              color: PdfColors.grey600,
            ),
          ),
        ],
      ),
    );
  }

  /// الحصول على أيقونة المعاملة
  String _getTransactionIcon(TransactionType type) {
    switch (type) {
      case TransactionType.irrigation:
        return '💧';
      case TransactionType.cashPayment:
        return '💰';
      case TransactionType.dieselPayment:
        return '⛽';
      case TransactionType.transfer:
        return '🔄';
      case TransactionType.adjustment:
        return '⚖️';
    }
  }

  /// بناء قسم الأرصدة النهائية المحسن
  pw.Widget _buildFinalBalanceSection(
      AccountStatementModel statement, pw.Font boldFont, pw.Font font) {
    return pw.Container(
      margin: const pw.EdgeInsets.symmetric(vertical: 8),
      child: pw.Column(
        children: [
          // عنوان الملخص
          pw.Container(
            padding: const pw.EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: const pw.BoxDecoration(
              color: PdfColors.orange800,
              borderRadius: pw.BorderRadius.only(
                topLeft: pw.Radius.circular(8),
                topRight: pw.Radius.circular(8),
              ),
            ),
            child: pw.Row(
              children: [
                pw.Text(
                  '📈 ملخص الحساب والتغييرات',
                  style: pw.TextStyle(
                    font: boldFont,
                    fontSize: 16,
                    color: PdfColors.white,
                  ),
                ),
              ],
            ),
          ),
          // محتوى الملخص
          pw.Container(
            padding: const pw.EdgeInsets.all(20),
            decoration: pw.BoxDecoration(
              color: PdfColors.orange50,
              borderRadius: const pw.BorderRadius.only(
                bottomLeft: pw.Radius.circular(8),
                bottomRight: pw.Radius.circular(8),
              ),
              border: pw.Border.all(color: PdfColors.orange200, width: 1),
            ),
            child: pw.Column(
              children: [
                // إجماليات الحركة
                pw.Text(
                  '💹 إجماليات الحركة خلال الفترة',
                  style: pw.TextStyle(
                    font: boldFont,
                    fontSize: 14,
                    color: PdfColors.orange900,
                  ),
                ),
                pw.SizedBox(height: 12),
                pw.Row(
                  children: [
                    pw.Expanded(
                      child: _buildSummaryCard(
                        '📈 الداخل',
                        [
                          (
                            '💰 نقدي',
                            statement.totalCashIn,
                            'ريال',
                            PdfColors.green700
                          ),
                          (
                            '⛽ ديزل',
                            statement.totalDieselIn,
                            'لتر',
                            PdfColors.blue700
                          ),
                        ],
                        PdfColors.green50,
                        PdfColors.green200,
                        boldFont,
                        font,
                      ),
                    ),
                    pw.SizedBox(width: 16),
                    pw.Expanded(
                      child: _buildSummaryCard(
                        '📉 الخارج',
                        [
                          (
                            '💸 نقدي',
                            statement.totalCashOut,
                            'ريال',
                            PdfColors.red700
                          ),
                          (
                            '🛢️ ديزل',
                            statement.totalDieselOut,
                            'لتر',
                            PdfColors.orange700
                          ),
                        ],
                        PdfColors.red50,
                        PdfColors.red200,
                        boldFont,
                        font,
                      ),
                    ),
                  ],
                ),
                pw.SizedBox(height: 16),
                // خط فاصل
                pw.Container(
                  height: 2,
                  decoration: pw.BoxDecoration(
                    gradient: const pw.LinearGradient(
                      colors: [
                        PdfColors.orange300,
                        PdfColors.orange600,
                        PdfColors.orange300
                      ],
                    ),
                    borderRadius: pw.BorderRadius.circular(1),
                  ),
                ),
                pw.SizedBox(height: 16),
                // الأرصدة النهائية
                pw.Text(
                  '🏦 الأرصدة النهائية (نهاية الفترة)',
                  style: pw.TextStyle(
                    font: boldFont,
                    fontSize: 14,
                    color: PdfColors.orange900,
                  ),
                ),
                pw.SizedBox(height: 12),
                pw.Row(
                  children: [
                    pw.Expanded(
                      child: _buildFinalBalanceCard(
                        '💵',
                        'الرصيد النقدي النهائي',
                        statement.finalCashBalance,
                        'ريال',
                        PdfColors.green700,
                        boldFont,
                        font,
                      ),
                    ),
                    pw.SizedBox(width: 16),
                    pw.Expanded(
                      child: _buildFinalBalanceCard(
                        '🛢️',
                        'رصيد الديزل النهائي',
                        statement.finalDieselBalance,
                        'لتر',
                        PdfColors.blue700,
                        boldFont,
                        font,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء عنصر ملخص
  pw.Widget _buildSummaryItem(String title, double amount, String unit,
      pw.Font boldFont, pw.Font font, PdfColor color) {
    return pw.Column(
      children: [
        pw.Text(title,
            style: pw.TextStyle(
                font: font, fontSize: 11, color: PdfColors.grey700)),
        pw.SizedBox(height: 4),
        pw.Text(
          '${amount.toStringAsFixed(2)} $unit',
          style: pw.TextStyle(font: boldFont, fontSize: 13, color: color),
        ),
      ],
    );
  }

  /// بناء بطاقة ملخص
  pw.Widget _buildSummaryCard(
    String title,
    List<(String, double, String, PdfColor)> items,
    PdfColor backgroundColor,
    PdfColor borderColor,
    pw.Font boldFont,
    pw.Font font,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(12),
      decoration: pw.BoxDecoration(
        color: backgroundColor,
        borderRadius: pw.BorderRadius.circular(8),
        border: pw.Border.all(color: borderColor, width: 1),
      ),
      child: pw.Column(
        children: [
          pw.Text(
            title,
            style: pw.TextStyle(
              font: boldFont,
              fontSize: 12,
              color: PdfColors.grey800,
            ),
          ),
          pw.SizedBox(height: 8),
          ...items.map((item) => pw.Padding(
                padding: const pw.EdgeInsets.symmetric(vertical: 2),
                child: pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  children: [
                    pw.Text(
                      item.$1,
                      style: pw.TextStyle(
                        font: font,
                        fontSize: 10,
                        color: PdfColors.grey700,
                      ),
                    ),
                    pw.Text(
                      '${item.$2.toStringAsFixed(2)} ${item.$3}',
                      style: pw.TextStyle(
                        font: boldFont,
                        fontSize: 10,
                        color: item.$4,
                      ),
                    ),
                  ],
                ),
              )),
        ],
      ),
    );
  }

  /// بناء بطاقة الرصيد النهائي
  pw.Widget _buildFinalBalanceCard(
    String icon,
    String title,
    double amount,
    String unit,
    PdfColor color,
    pw.Font boldFont,
    pw.Font font,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        gradient: pw.LinearGradient(
          colors: [color.shade(0.1), color.shade(0.05)],
          begin: pw.Alignment.topLeft,
          end: pw.Alignment.bottomRight,
        ),
        borderRadius: pw.BorderRadius.circular(12),
        border: pw.Border.all(color: color.shade(0.3), width: 2),
      ),
      child: pw.Column(
        children: [
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.center,
            children: [
              pw.Text(
                icon,
                style: const pw.TextStyle(fontSize: 24),
              ),
              pw.SizedBox(width: 8),
              pw.Expanded(
                child: pw.Text(
                  title,
                  style: pw.TextStyle(
                    font: font,
                    fontSize: 11,
                    color: PdfColors.grey700,
                  ),
                  textAlign: pw.TextAlign.center,
                ),
              ),
            ],
          ),
          pw.SizedBox(height: 12),
          pw.Text(
            '${amount.toStringAsFixed(2)} $unit',
            style: pw.TextStyle(
              font: boldFont,
              fontSize: 18,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء قسم التوقيع المحسن
  pw.Widget _buildSignatureSection(pw.Font font) {
    return pw.Container(
      margin: const pw.EdgeInsets.only(top: 20),
      child: pw.Column(
        children: [
          // خط فاصل
          pw.Container(
            height: 1,
            color: PdfColors.grey300,
            margin: const pw.EdgeInsets.symmetric(vertical: 16),
          ),
          // قسم التوقيع
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              // توقيع المسؤول
              pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    '✍️ توقيع المسؤول',
                    style: pw.TextStyle(
                      font: font,
                      fontSize: 12,
                      color: PdfColors.grey700,
                    ),
                  ),
                  pw.SizedBox(height: 24),
                  pw.Container(
                    width: 150,
                    height: 2,
                    decoration: pw.BoxDecoration(
                      color: PdfColors.grey400,
                      borderRadius: pw.BorderRadius.circular(1),
                    ),
                  ),
                  pw.SizedBox(height: 4),
                  pw.Text(
                    'الاسم: ________________',
                    style: pw.TextStyle(
                      font: font,
                      fontSize: 10,
                      color: PdfColors.grey600,
                    ),
                  ),
                ],
              ),
              // ختم المؤسسة
              pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.end,
                children: [
                  pw.Text(
                    '🏢 ختم المؤسسة',
                    style: pw.TextStyle(
                      font: font,
                      fontSize: 12,
                      color: PdfColors.grey700,
                    ),
                  ),
                  pw.SizedBox(height: 8),
                  pw.Container(
                    width: 80,
                    height: 80,
                    decoration: pw.BoxDecoration(
                      border: pw.Border.all(color: PdfColors.grey400, width: 2),
                      borderRadius: pw.BorderRadius.circular(8),
                    ),
                    child: pw.Center(
                      child: pw.Text(
                        'الختم\nالرسمي',
                        style: pw.TextStyle(
                          font: font,
                          fontSize: 8,
                          color: PdfColors.grey500,
                        ),
                        textAlign: pw.TextAlign.center,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
          pw.SizedBox(height: 16),
          // تذييل احترافي
          pw.Container(
            padding: const pw.EdgeInsets.all(12),
            decoration: pw.BoxDecoration(
              color: PdfColors.grey100,
              borderRadius: pw.BorderRadius.circular(6),
            ),
            child: pw.Column(
              children: [
                pw.Text(
                  '📞 للاستفسارات: 966-11-1234567 | 📧 <EMAIL> | 🌐 www.farm.com',
                  style: pw.TextStyle(
                    font: font,
                    fontSize: 9,
                    color: PdfColors.grey600,
                  ),
                  textAlign: pw.TextAlign.center,
                ),
                pw.SizedBox(height: 4),
                pw.Text(
                  'تم إنشاء هذا التقرير بواسطة نظام إدارة المزارع الذكي',
                  style: pw.TextStyle(
                    font: font,
                    fontSize: 8,
                    color: PdfColors.grey500,
                  ),
                  textAlign: pw.TextAlign.center,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// تنسيق وصف المعاملة
  String _formatDescription(AccountTransactionModel transaction) {
    String description = transaction.description;
    if (transaction.farmName != null && transaction.farmName!.isNotEmpty) {
      description += ' - ${transaction.farmName}';
    }
    if (transaction.duration != null && transaction.duration! > 0) {
      description += ' (${transaction.duration!.toStringAsFixed(1)} ساعة)';
    }
    return description;
  }

  /// تنسيق المبلغ
  String _formatAmount(double amount) {
    if (amount == 0) return '-';
    return amount > 0
        ? '+${amount.toStringAsFixed(2)}'
        : amount.toStringAsFixed(2);
  }

  /// الحصول على لون المبلغ
  PdfColor _getAmountColor(double amount) {
    if (amount > 0) return PdfColors.green700;
    if (amount < 0) return PdfColors.red700;
    return PdfColors.grey600;
  }

  /// ترجمة نوع العملية
  String _getTransactionTypeLabel(TransactionType type) {
    switch (type) {
      case TransactionType.irrigation:
        return 'تسقية';
      case TransactionType.cashPayment:
        return 'دفعة نقدية';
      case TransactionType.dieselPayment:
        return 'دفعة ديزل';
      case TransactionType.transfer:
        return 'تحويل';
      case TransactionType.adjustment:
        return 'تسوية';
    }
  }
}

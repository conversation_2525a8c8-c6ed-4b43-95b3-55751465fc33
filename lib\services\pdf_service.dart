import 'dart:io';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:untitled/data/models/client_model.dart';
import 'package:untitled/data/models/farm_model.dart';
import 'package:untitled/data/models/irrigation_model.dart';
import 'package:untitled/data/models/payment_model.dart';
import 'package:untitled/data/models/account_statement_model.dart';
import 'package:flutter/services.dart' show rootBundle;

/// PDF Service - معطل مؤقتاً لتسريع البناء
/// يمكن تفعيله لاحقاً بإزالة التعليقات وإضافة التبعيات
class PdfService {
  // إنشاء فاتورة تسقية - معطل مؤقتاً لتسريع البناء
  Future<File> createIrrigationInvoice({
    required ClientModel client,
    required FarmModel farm,
    required IrrigationModel irrigation,
    required double cashBalance,
    required double dieselBalance,
  }) async {
    // PDF functionality disabled for faster build
    throw UnimplementedError(
        'PDF functionality is temporarily disabled for faster build');
  }

  // إنشاء فاتورة دفع - معطل مؤقتاً لتسريع البناء
  Future<File> createPaymentInvoice({
    required ClientModel client,
    required FarmModel farm,
    required PaymentModel payment,
    required double cashBalance,
    required double dieselBalance,
  }) async {
    // PDF functionality disabled for faster build
    throw UnimplementedError(
        'PDF functionality is temporarily disabled for faster build');
  }

  // إنشاء تقرير - معطل مؤقتاً لتسريع البناء
  Future<File> createReport({
    required String title,
    required String subtitle,
    required List<Map<String, dynamic>> data,
    required List<String> columns,
    required List<String> columnTitles,
  }) async {
    // PDF functionality disabled for faster build
    throw UnimplementedError(
        'PDF functionality is temporarily disabled for faster build');
  }

  /// توليد كشف حساب عميل PDF احترافي بالكامل بالعربية
  Future<File> createClientStatementPdf({
    required AccountStatementModel statement,
    required String logoAssetPath,
  }) async {
    final pdf = pw.Document();

    // تحميل الخطوط مع معالجة الأخطاء
    pw.Font font;
    pw.Font boldFont;

    try {
      final fontData =
          await rootBundle.load('assets/fonts/static/Cairo-Regular.ttf');
      font = pw.Font.ttf(fontData);
      final boldFontData =
          await rootBundle.load('assets/fonts/static/Cairo-Bold.ttf');
      boldFont = pw.Font.ttf(boldFontData);
    } catch (e) {
      // استخدام الخط الافتراضي في حالة عدم وجود الخطوط العربية
      print('تعذر تحميل الخطوط العربية، سيتم استخدام الخط الافتراضي: $e');
      font = pw.Font.helvetica();
      boldFont = pw.Font.helveticaBold();
    }

    final dateFormat = DateFormat('yyyy/MM/dd', 'ar');

    pdf.addPage(
      pw.MultiPage(
        theme: pw.ThemeData.withFont(
          base: font,
          bold: boldFont,
        ),
        textDirection: pw.TextDirection.rtl,
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(20),
        build: (context) => [
          // رأس الصفحة المحسن
          _buildPdfHeader(statement, dateFormat, boldFont, font),
          pw.SizedBox(height: 20),

          // معلومات الرصيد الابتدائي
          _buildInitialBalanceSection(statement, boldFont, font),
          pw.SizedBox(height: 16),
          // جدول المعاملات المحسن
          _buildTransactionsTable(statement, dateFormat, boldFont, font),
          pw.SizedBox(height: 16),

          // ملخص الأرصدة النهائية
          _buildFinalBalanceSection(statement, boldFont, font),
          pw.SizedBox(height: 24),

          // توقيع وختام
          _buildSignatureSection(font),
        ],
      ),
    );

    final output = await getTemporaryDirectory();
    final file = File(
        '${output.path}/client_statement_${statement.clientId}_${DateTime.now().millisecondsSinceEpoch}.pdf');
    await file.writeAsBytes(await pdf.save());
    return file;
  }

  /// بناء رأس الصفحة المحسن
  pw.Widget _buildPdfHeader(AccountStatementModel statement,
      DateFormat dateFormat, pw.Font boldFont, pw.Font font) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        color: PdfColors.blue50,
        borderRadius: pw.BorderRadius.circular(8),
        border: pw.Border.all(color: PdfColors.blue200, width: 1),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.end,
        children: [
          pw.Text(
            'كشف حساب عميل',
            style: pw.TextStyle(
              font: boldFont,
              fontSize: 24,
              color: PdfColors.blue900,
            ),
          ),
          pw.SizedBox(height: 8),
          pw.Text(
            'اسم العميل: ${statement.clientName}',
            style: pw.TextStyle(font: boldFont, fontSize: 16),
          ),
          pw.SizedBox(height: 4),
          pw.Text(
            'الفترة: ${dateFormat.format(statement.fromDate)} - ${dateFormat.format(statement.toDate)}',
            style: pw.TextStyle(
                font: font, fontSize: 14, color: PdfColors.grey700),
          ),
          pw.SizedBox(height: 4),
          pw.Text(
            'تاريخ الإنشاء: ${DateFormat('yyyy/MM/dd HH:mm').format(DateTime.now())}',
            style: pw.TextStyle(
                font: font, fontSize: 12, color: PdfColors.grey600),
          ),
        ],
      ),
    );
  }

  /// بناء قسم الرصيد الابتدائي
  pw.Widget _buildInitialBalanceSection(
      AccountStatementModel statement, pw.Font boldFont, pw.Font font) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(12),
      decoration: pw.BoxDecoration(
        color: PdfColors.grey100,
        borderRadius: pw.BorderRadius.circular(6),
        border: pw.Border.all(color: PdfColors.grey300, width: 0.5),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceEvenly,
        children: [
          _buildBalanceItem(
              'الرصيد النقدي الابتدائي',
              statement.initialCashBalance,
              'ريال',
              boldFont,
              font,
              PdfColors.green700),
          _buildBalanceItem(
              'رصيد الديزل الابتدائي',
              statement.initialDieselBalance,
              'لتر',
              boldFont,
              font,
              PdfColors.blue700),
        ],
      ),
    );
  }

  /// بناء عنصر رصيد
  pw.Widget _buildBalanceItem(String title, double amount, String unit,
      pw.Font boldFont, pw.Font font, PdfColor color) {
    return pw.Column(
      children: [
        pw.Text(title,
            style: pw.TextStyle(
                font: font, fontSize: 12, color: PdfColors.grey700)),
        pw.SizedBox(height: 4),
        pw.Text(
          '${amount.toStringAsFixed(2)} $unit',
          style: pw.TextStyle(font: boldFont, fontSize: 14, color: color),
        ),
      ],
    );
  }

  /// بناء جدول المعاملات المحسن
  pw.Widget _buildTransactionsTable(AccountStatementModel statement,
      DateFormat dateFormat, pw.Font boldFont, pw.Font font) {
    if (statement.transactions.isEmpty) {
      return pw.Container(
        padding: const pw.EdgeInsets.all(20),
        decoration: pw.BoxDecoration(
          color: PdfColors.grey100,
          borderRadius: pw.BorderRadius.circular(8),
        ),
        child: pw.Center(
          child: pw.Text(
            'لا توجد معاملات في هذه الفترة',
            style: pw.TextStyle(
                font: font, fontSize: 14, color: PdfColors.grey600),
          ),
        ),
      );
    }

    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey400, width: 0.8),
      columnWidths: {
        0: const pw.FixedColumnWidth(40), // التاريخ
        1: const pw.FlexColumnWidth(2), // البيان
        2: const pw.FixedColumnWidth(60), // النوع
        3: const pw.FixedColumnWidth(60), // نقدي
        4: const pw.FixedColumnWidth(60), // ديزل
        5: const pw.FixedColumnWidth(70), // رصيد نقدي
        6: const pw.FixedColumnWidth(70), // رصيد ديزل
      },
      children: [
        // رأس الجدول المحسن
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.blue800),
          children: [
            _buildTableHeader('التاريخ', boldFont),
            _buildTableHeader('البيان', boldFont),
            _buildTableHeader('النوع', boldFont),
            _buildTableHeader('نقدي', boldFont),
            _buildTableHeader('ديزل', boldFont),
            _buildTableHeader('رصيد نقدي', boldFont),
            _buildTableHeader('رصيد ديزل', boldFont),
          ],
        ),
        // صفوف البيانات
        ...statement.transactions.asMap().entries.map((entry) {
          final index = entry.key;
          final transaction = entry.value;
          final isEven = index % 2 == 0;

          return pw.TableRow(
            decoration: pw.BoxDecoration(
              color: isEven ? PdfColors.grey50 : PdfColors.white,
            ),
            children: [
              _buildTableCell(dateFormat.format(transaction.date), font,
                  fontSize: 10),
              _buildTableCell(_formatDescription(transaction), font,
                  fontSize: 10, maxLines: 2),
              _buildTableCell(_getTransactionTypeLabel(transaction.type), font,
                  fontSize: 9),
              _buildTableCell(_formatAmount(transaction.cashAmount), font,
                  fontSize: 10, color: _getAmountColor(transaction.cashAmount)),
              _buildTableCell(_formatAmount(transaction.dieselAmount), font,
                  fontSize: 10,
                  color: _getAmountColor(transaction.dieselAmount)),
              _buildTableCell(
                  transaction.runningCashBalance.toStringAsFixed(2), font,
                  fontSize: 10),
              _buildTableCell(
                  transaction.runningDieselBalance.toStringAsFixed(2), font,
                  fontSize: 10),
            ],
          );
        }),
      ],
    );
  }

  /// بناء رأس عمود الجدول
  pw.Widget _buildTableHeader(String text, pw.Font boldFont) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          font: boldFont,
          fontSize: 12,
          color: PdfColors.white,
        ),
        textAlign: pw.TextAlign.center,
      ),
    );
  }

  /// بناء خلية الجدول
  pw.Widget _buildTableCell(
    String text,
    pw.Font font, {
    double fontSize = 11,
    PdfColor? color,
    int maxLines = 1,
  }) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(6),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          font: font,
          fontSize: fontSize,
          color: color ?? PdfColors.black,
        ),
        textAlign: pw.TextAlign.center,
        maxLines: maxLines,
      ),
    );
  }

  /// بناء قسم الأرصدة النهائية
  pw.Widget _buildFinalBalanceSection(
      AccountStatementModel statement, pw.Font boldFont, pw.Font font) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        color: PdfColors.amber50,
        borderRadius: pw.BorderRadius.circular(8),
        border: pw.Border.all(color: PdfColors.amber200, width: 1),
      ),
      child: pw.Column(
        children: [
          pw.Text(
            'ملخص الحساب',
            style: pw.TextStyle(
                font: boldFont, fontSize: 16, color: PdfColors.amber900),
          ),
          pw.SizedBox(height: 12),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceEvenly,
            children: [
              _buildSummaryItem('إجمالي النقدي الداخل', statement.totalCashIn,
                  'ريال', boldFont, font, PdfColors.green700),
              _buildSummaryItem('إجمالي النقدي الخارج', statement.totalCashOut,
                  'ريال', boldFont, font, PdfColors.red700),
            ],
          ),
          pw.SizedBox(height: 8),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceEvenly,
            children: [
              _buildSummaryItem('إجمالي الديزل الداخل', statement.totalDieselIn,
                  'لتر', boldFont, font, PdfColors.blue700),
              _buildSummaryItem(
                  'إجمالي الديزل الخارج',
                  statement.totalDieselOut,
                  'لتر',
                  boldFont,
                  font,
                  PdfColors.orange700),
            ],
          ),
          pw.SizedBox(height: 12),
          pw.Divider(color: PdfColors.amber300),
          pw.SizedBox(height: 8),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceEvenly,
            children: [
              _buildBalanceItem(
                  'الرصيد النقدي النهائي',
                  statement.finalCashBalance,
                  'ريال',
                  boldFont,
                  font,
                  PdfColors.green800),
              _buildBalanceItem(
                  'رصيد الديزل النهائي',
                  statement.finalDieselBalance,
                  'لتر',
                  boldFont,
                  font,
                  PdfColors.blue800),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء عنصر ملخص
  pw.Widget _buildSummaryItem(String title, double amount, String unit,
      pw.Font boldFont, pw.Font font, PdfColor color) {
    return pw.Column(
      children: [
        pw.Text(title,
            style: pw.TextStyle(
                font: font, fontSize: 11, color: PdfColors.grey700)),
        pw.SizedBox(height: 4),
        pw.Text(
          '${amount.toStringAsFixed(2)} $unit',
          style: pw.TextStyle(font: boldFont, fontSize: 13, color: color),
        ),
      ],
    );
  }

  /// بناء قسم التوقيع
  pw.Widget _buildSignatureSection(pw.Font font) {
    return pw.Row(
      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
      children: [
        pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Text('توقيع المسؤول:',
                style: pw.TextStyle(font: font, fontSize: 14)),
            pw.SizedBox(height: 32),
            pw.Container(
              width: 150,
              height: 1,
              color: PdfColors.grey400,
            ),
          ],
        ),
        pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.end,
          children: [
            pw.Text('ختم المؤسسة:',
                style: pw.TextStyle(font: font, fontSize: 14)),
            pw.SizedBox(height: 32),
            pw.Container(
              width: 100,
              height: 60,
              decoration: pw.BoxDecoration(
                border: pw.Border.all(color: PdfColors.grey400, width: 1),
                borderRadius: pw.BorderRadius.circular(4),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// تنسيق وصف المعاملة
  String _formatDescription(AccountTransactionModel transaction) {
    String description = transaction.description;
    if (transaction.farmName != null && transaction.farmName!.isNotEmpty) {
      description += ' - ${transaction.farmName}';
    }
    if (transaction.duration != null && transaction.duration! > 0) {
      description += ' (${transaction.duration!.toStringAsFixed(1)} ساعة)';
    }
    return description;
  }

  /// تنسيق المبلغ
  String _formatAmount(double amount) {
    if (amount == 0) return '-';
    return amount > 0
        ? '+${amount.toStringAsFixed(2)}'
        : amount.toStringAsFixed(2);
  }

  /// الحصول على لون المبلغ
  PdfColor _getAmountColor(double amount) {
    if (amount > 0) return PdfColors.green700;
    if (amount < 0) return PdfColors.red700;
    return PdfColors.grey600;
  }

  /// ترجمة نوع العملية
  String _getTransactionTypeLabel(TransactionType type) {
    switch (type) {
      case TransactionType.irrigation:
        return 'تسقية';
      case TransactionType.cashPayment:
        return 'دفعة نقدية';
      case TransactionType.dieselPayment:
        return 'دفعة ديزل';
      case TransactionType.transfer:
        return 'تحويل';
      case TransactionType.adjustment:
        return 'تسوية';
    }
  }
}

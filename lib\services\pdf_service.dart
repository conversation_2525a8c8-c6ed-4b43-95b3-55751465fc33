import 'dart:io';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:untitled/data/models/client_model.dart';
import 'package:untitled/data/models/farm_model.dart';
import 'package:untitled/data/models/irrigation_model.dart';
import 'package:untitled/data/models/payment_model.dart';
import 'package:untitled/data/models/account_statement_model.dart';
import 'package:untitled/data/models/cashbox_model.dart';
import 'package:flutter/services.dart' show rootBundle;

/// PDF Service - معطل مؤقتاً لتسريع البناء
/// يمكن تفعيله لاحقاً بإزالة التعليقات وإضافة التبعيات
class PdfService {
  // إنشاء فاتورة تسقية - معطل مؤقتاً لتسريع البناء
  Future<File> createIrrigationInvoice({
    required ClientModel client,
    required FarmModel farm,
    required IrrigationModel irrigation,
    required double cashBalance,
    required double dieselBalance,
  }) async {
    // PDF functionality disabled for faster build
    throw UnimplementedError(
        'PDF functionality is temporarily disabled for faster build');
  }

  // إنشاء فاتورة دفع - معطل مؤقتاً لتسريع البناء
  Future<File> createPaymentInvoice({
    required ClientModel client,
    required FarmModel farm,
    required PaymentModel payment,
    required double cashBalance,
    required double dieselBalance,
  }) async {
    // PDF functionality disabled for faster build
    throw UnimplementedError(
        'PDF functionality is temporarily disabled for faster build');
  }

  // إنشاء تقرير - معطل مؤقتاً لتسريع البناء
  Future<File> createReport({
    required String title,
    required String subtitle,
    required List<Map<String, dynamic>> data,
    required List<String> columns,
    required List<String> columnTitles,
  }) async {
    // PDF functionality disabled for faster build
    throw UnimplementedError(
        'PDF functionality is temporarily disabled for faster build');
  }

  /// توليد كشف حساب عميل PDF احترافي بالكامل بالعربية
  Future<File> createClientStatementPdf({
    required AccountStatementModel statement,
    required String logoAssetPath,
  }) async {
    final pdf = pw.Document();

    // تحميل الخطوط مع معالجة الأخطاء
    pw.Font font;
    pw.Font boldFont;

    try {
      final fontData =
          await rootBundle.load('assets/fonts/static/Cairo-Regular.ttf');
      font = pw.Font.ttf(fontData);
      final boldFontData =
          await rootBundle.load('assets/fonts/static/Cairo-Bold.ttf');
      boldFont = pw.Font.ttf(boldFontData);
    } catch (e) {
      // استخدام الخط الافتراضي في حالة عدم وجود الخطوط العربية
      print('تعذر تحميل الخطوط العربية، سيتم استخدام الخط الافتراضي: $e');
      font = pw.Font.helvetica();
      boldFont = pw.Font.helveticaBold();
    }

    final dateFormat = DateFormat('yyyy/MM/dd', 'ar');

    pdf.addPage(
      pw.MultiPage(
        theme: pw.ThemeData.withFont(
          base: font,
          bold: boldFont,
        ),
        textDirection: pw.TextDirection.rtl,
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(20),
        build: (context) => [
          // رأس الصفحة المحسن
          _buildPdfHeader(statement, dateFormat, boldFont, font),
          pw.SizedBox(height: 20),

          // معلومات الرصيد الابتدائي
          _buildInitialBalanceSection(statement, boldFont, font),
          pw.SizedBox(height: 16),
          // الجدول المحاسبي الاحترافي
          _buildTransactionsTable(statement, dateFormat, boldFont, font),
          pw.SizedBox(height: 24),

          // تذييل الصفحة
          _buildFooter(font),
        ],
      ),
    );

    final output = await getTemporaryDirectory();
    final file = File(
        '${output.path}/client_statement_${statement.clientId}_${DateTime.now().millisecondsSinceEpoch}.pdf');
    await file.writeAsBytes(await pdf.save());
    return file;
  }

  /// توليد تقرير مالي PDF احترافي بالكامل بالعربية
  Future<File> createFinancialReportPdf({
    required String reportTitle,
    required DateTime fromDate,
    required DateTime toDate,
    required Map<String, double> summaryStats, // نفس مفاتيح _financialStats
    required List<Map<String, dynamic>>
        transactions, // كل عملية: التاريخ، النوع، الوصف، الإيراد، مصروف، رصيد
    String? logoAssetPath,
  }) async {
    final pdf = pw.Document();
    pw.Font font;
    pw.Font boldFont;
    pw.ImageProvider? logoImage;
    try {
      final fontData =
          await rootBundle.load('assets/fonts/static/Cairo-Regular.ttf');
      font = pw.Font.ttf(fontData);
      final boldFontData =
          await rootBundle.load('assets/fonts/static/Cairo-Bold.ttf');
      boldFont = pw.Font.ttf(boldFontData);
      if (logoAssetPath != null) {
        final logoBytes =
            (await rootBundle.load(logoAssetPath)).buffer.asUint8List();
        logoImage = pw.MemoryImage(logoBytes);
      }
    } catch (e) {
      font = pw.Font.helvetica();
      boldFont = pw.Font.helveticaBold();
      logoImage = null;
    }
    final dateFormat = DateFormat('yyyy/MM/dd', 'ar');

    pdf.addPage(
      pw.MultiPage(
        theme: pw.ThemeData.withFont(base: font, bold: boldFont),
        textDirection: pw.TextDirection.rtl,
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(20),
        build: (context) {
          final List<pw.Widget> widgets = [];
          final List<pw.Widget> headerChildren = [];
          if (logoImage != null) {
            headerChildren.add(
              pw.Container(
                height: 60,
                child: pw.Image(logoImage!, fit: pw.BoxFit.contain),
              ),
            );
          }
          headerChildren.addAll([
            pw.Text(reportTitle,
                style: pw.TextStyle(
                    font: boldFont, fontSize: 22, color: PdfColors.indigo800)),
            pw.SizedBox(height: 4),
            pw.Text(
                'الفترة: من ${dateFormat.format(fromDate)} إلى ${dateFormat.format(toDate)}',
                style: pw.TextStyle(
                    font: font, fontSize: 13, color: PdfColors.grey700)),
          ]);
          widgets.add(pw.Column(children: headerChildren));
          widgets.add(pw.SizedBox(height: 16));
          widgets
              .add(_buildFinancialSummarySection(summaryStats, boldFont, font));
          widgets.add(pw.SizedBox(height: 16));
          widgets.add(_buildFinancialTransactionsTable(
              transactions, dateFormat, boldFont, font));
          widgets.add(pw.SizedBox(height: 24));
          widgets.add(_buildFooter(font));
          return widgets;
        },
      ),
    );
    final output = await getTemporaryDirectory();
    final file = File(
      '${output.path}/financial_report_${DateTime.now().millisecondsSinceEpoch}.pdf',
    );
    await file.writeAsBytes(await pdf.save());
    return file;
  }

  /// توليد تقرير تسقيات PDF احترافي بالكامل بالعربية
  Future<File> createIrrigationStatementPdf({
    required List irrigations, // قائمة التسقيات
    required List clients, // قائمة العملاء
    required List farms, // قائمة المزارع
    required DateTime fromDate,
    required DateTime toDate,
    String? logoAssetPath,
  }) async {
    final pdf = pw.Document();
    pw.Font font;
    pw.Font boldFont;
    pw.ImageProvider? logoImage;
    try {
      final fontData =
          await rootBundle.load('assets/fonts/static/Cairo-Regular.ttf');
      font = pw.Font.ttf(fontData);
      final boldFontData =
          await rootBundle.load('assets/fonts/static/Cairo-Bold.ttf');
      boldFont = pw.Font.ttf(boldFontData);
      if (logoAssetPath != null) {
        final logoBytes =
            (await rootBundle.load(logoAssetPath)).buffer.asUint8List();
        logoImage = pw.MemoryImage(logoBytes);
      }
    } catch (e) {
      font = pw.Font.helvetica();
      boldFont = pw.Font.helveticaBold();
      logoImage = null;
    }
    final dateFormat = DateFormat('yyyy/MM/dd', 'ar');
    // بناء قواميس الأسماء
    final Map<int, String> clientNames = {
      for (final c in clients)
        if (c.id != null) c.id!: c.name
    };
    final Map<int, String> farmNames = {
      for (final f in farms)
        if (f.id != null) f.id!: f.name
    };
    // ملخص إحصائي
    final totalCount = irrigations.length;
    final totalHours =
        irrigations.fold<double>(0.0, (sum, i) => sum + (i.duration / 60.0));
    final totalCost = irrigations.fold<double>(0.0, (sum, i) => sum + i.cost);
    final totalDiesel =
        irrigations.fold<double>(0.0, (sum, i) => sum + i.dieselConsumption);
    // بناء الجدول
    final rows = <pw.TableRow>[];
    rows.add(
      pw.TableRow(
        decoration: const pw.BoxDecoration(color: PdfColors.grey300),
        children: [
          _buildAccountingTableHeader('اسم العميل', boldFont),
          _buildAccountingTableHeader('اسم المزرعة', boldFont),
          _buildAccountingTableHeader('التاريخ', boldFont),
          _buildAccountingTableHeader('المدة (ساعة)', boldFont),
          _buildAccountingTableHeader('نوع الدفع', boldFont),
          _buildAccountingTableHeader('التكلفة', boldFont),
          _buildAccountingTableHeader('كمية الديزل', boldFont),
        ],
      ),
    );
    for (var i = 0; i < irrigations.length; i++) {
      final irr = irrigations[i];
      final isEven = i % 2 == 0;
      rows.add(
        pw.TableRow(
          decoration: pw.BoxDecoration(
              color: isEven ? PdfColors.grey50 : PdfColors.white),
          children: [
            _buildAccountingTableCell(
                clientNames[irr.clientId] ?? 'غير محدد', font),
            _buildAccountingTableCell(
                farmNames[irr.farmId] ?? 'غير محدد', font),
            _buildAccountingTableCell(dateFormat.format(irr.startTime), font),
            _buildAccountingTableCell(
                (irr.duration / 60.0).toStringAsFixed(1), font),
            _buildAccountingTableCell('نقدي', font),
            _buildAccountingTableCell(_formatCurrency(irr.cost), font,
                color: PdfColors.green700),
            _buildAccountingTableCell(
                irr.dieselConsumption.toStringAsFixed(2), font),
          ],
        ),
      );
    }
    pdf.addPage(
      pw.MultiPage(
        theme: pw.ThemeData.withFont(base: font, bold: boldFont),
        textDirection: pw.TextDirection.rtl,
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(20),
        build: (context) {
          return [
            if (logoImage != null)
              pw.Container(
                height: 60,
                child: pw.Image(logoImage!, fit: pw.BoxFit.contain),
              ),
            pw.Text('تقرير التسقيات',
                style: pw.TextStyle(
                    font: boldFont, fontSize: 22, color: PdfColors.indigo800)),
            pw.SizedBox(height: 4),
            pw.Text(
                'الفترة: من ${dateFormat.format(fromDate)} إلى ${dateFormat.format(toDate)}',
                style: pw.TextStyle(
                    font: font, fontSize: 13, color: PdfColors.grey700)),
            pw.SizedBox(height: 16),
            _buildFinancialSummarySection({
              'عدد التسقيات': totalCount.toDouble(),
              'عدد الساعات': totalHours,
              'إجمالي التكلفة': totalCost,
              'كمية الديزل': totalDiesel,
            }, boldFont, font),
            pw.SizedBox(height: 16),
            pw.Text('جدول التسقيات',
                style: pw.TextStyle(
                    font: boldFont, fontSize: 15, color: PdfColors.indigo900)),
            pw.SizedBox(height: 8),
            pw.Table(
              border: pw.TableBorder.all(color: PdfColors.grey400, width: 1),
              columnWidths: const {
                0: pw.FixedColumnWidth(90),
                1: pw.FixedColumnWidth(90),
                2: pw.FixedColumnWidth(80),
                3: pw.FixedColumnWidth(60),
                4: pw.FixedColumnWidth(60),
                5: pw.FixedColumnWidth(80),
                6: pw.FixedColumnWidth(70),
              },
              children: rows,
            ),
            pw.SizedBox(height: 24),
            _buildFooter(font),
          ];
        },
      ),
    );
    final output = await getTemporaryDirectory();
    final file = File(
      '${output.path}/irrigation_statement_${DateTime.now().millisecondsSinceEpoch}.pdf',
    );
    await file.writeAsBytes(await pdf.save());
    return file;
  }

  /// بناء رأس التقرير المحاسبي الاحترافي
  pw.Widget _buildPdfHeader(AccountStatementModel statement,
      DateFormat dateFormat, pw.Font boldFont, pw.Font font) {
    final accountNumber = _generateAccountNumber(statement.clientId);
    final englishDateFormat = DateFormat('yyyy-MM-dd', 'en');

    return pw.Column(
      children: [
        // رأس الشركة مع الهاتف
        pw.Container(
          padding: const pw.EdgeInsets.symmetric(vertical: 20),
          child: pw.Column(
            children: [
              pw.Text(
                'مياه المراوح',
                style: pw.TextStyle(
                  font: boldFont,
                  fontSize: 24,
                  color: PdfColors.black,
                ),
              ),
              pw.SizedBox(height: 4),
              pw.Text(
                'هاتف: 966-11-1234567',
                style: pw.TextStyle(
                  font: font,
                  fontSize: 14,
                  color: PdfColors.grey700,
                ),
              ),
            ],
          ),
        ),

        // معلومات الحساب
        pw.Container(
          padding: const pw.EdgeInsets.all(16),
          decoration: pw.BoxDecoration(
            border: pw.Border.all(color: PdfColors.grey400, width: 1),
            borderRadius: pw.BorderRadius.circular(8),
          ),
          child: pw.Column(
            children: [
              // الصف الأول: اسم الحساب ورقم الحساب
              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  // اسم الحساب (يمين)
                  pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.end,
                    children: [
                      pw.Text(
                        'اسم الحساب',
                        style: pw.TextStyle(
                          font: font,
                          fontSize: 12,
                          color: PdfColors.grey700,
                        ),
                      ),
                      pw.SizedBox(height: 4),
                      pw.Text(
                        statement.clientName,
                        style: pw.TextStyle(
                          font: boldFont,
                          fontSize: 16,
                          color: PdfColors.black,
                        ),
                      ),
                    ],
                  ),
                  // رقم الحساب (وسط)
                  pw.Column(
                    children: [
                      pw.Text(
                        'رقم الحساب',
                        style: pw.TextStyle(
                          font: font,
                          fontSize: 12,
                          color: PdfColors.grey700,
                        ),
                      ),
                      pw.SizedBox(height: 4),
                      pw.Text(
                        accountNumber,
                        style: pw.TextStyle(
                          font: boldFont,
                          fontSize: 16,
                          color: PdfColors.blue800,
                        ),
                      ),
                    ],
                  ),
                  // العملة (يسار)
                  pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text(
                        'العملة',
                        style: pw.TextStyle(
                          font: font,
                          fontSize: 12,
                          color: PdfColors.grey700,
                        ),
                      ),
                      pw.SizedBox(height: 4),
                      pw.Text(
                        'ريال يمني (YER)',
                        style: pw.TextStyle(
                          font: boldFont,
                          fontSize: 14,
                          color: PdfColors.green700,
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              pw.SizedBox(height: 16),

              // الصف الثاني: الفترة الزمنية
              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.center,
                children: [
                  pw.Text(
                    'كشف حساب للفترة من ',
                    style: pw.TextStyle(
                      font: font,
                      fontSize: 14,
                      color: PdfColors.grey700,
                    ),
                  ),
                  pw.Text(
                    englishDateFormat.format(statement.fromDate),
                    style: pw.TextStyle(
                      font: boldFont,
                      fontSize: 14,
                      color: PdfColors.blue800,
                    ),
                  ),
                  pw.Text(
                    ' إلى ',
                    style: pw.TextStyle(
                      font: font,
                      fontSize: 14,
                      color: PdfColors.grey700,
                    ),
                  ),
                  pw.Text(
                    englishDateFormat.format(statement.toDate),
                    style: pw.TextStyle(
                      font: boldFont,
                      fontSize: 14,
                      color: PdfColors.blue800,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء قسم الرصيد الابتدائي المحسن
  pw.Widget _buildInitialBalanceSection(
      AccountStatementModel statement, pw.Font boldFont, pw.Font font) {
    return pw.Container(
      margin: const pw.EdgeInsets.symmetric(vertical: 8),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          // عنوان القسم
          pw.Container(
            padding: const pw.EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: pw.BoxDecoration(
              color: PdfColors.indigo800,
              borderRadius: const pw.BorderRadius.only(
                topLeft: pw.Radius.circular(8),
                topRight: pw.Radius.circular(8),
              ),
            ),
            child: pw.Row(
              children: [
                pw.Text(
                  '💰 الرصيد السابق (بداية الفترة)',
                  style: pw.TextStyle(
                    font: boldFont,
                    fontSize: 16,
                    color: PdfColors.white,
                  ),
                ),
              ],
            ),
          ),
          // محتوى الأرصدة
          pw.Container(
            padding: const pw.EdgeInsets.all(16),
            decoration: pw.BoxDecoration(
              color: PdfColors.indigo50,
              borderRadius: const pw.BorderRadius.only(
                bottomLeft: pw.Radius.circular(8),
                bottomRight: pw.Radius.circular(8),
              ),
              border: pw.Border.all(color: PdfColors.indigo200, width: 1),
            ),
            child: pw.Row(
              children: [
                // الرصيد النقدي
                pw.Expanded(
                  child: _buildEnhancedBalanceCard(
                    '💵',
                    'الرصيد النقدي',
                    statement.initialCashBalance,
                    'ريال',
                    PdfColors.green700,
                    PdfColors.green50,
                    boldFont,
                    font,
                  ),
                ),
                pw.SizedBox(width: 16),
                // رصيد الديزل
                pw.Expanded(
                  child: _buildEnhancedBalanceCard(
                    '⛽',
                    'رصيد الديزل',
                    statement.initialDieselBalance,
                    'لتر',
                    PdfColors.blue700,
                    PdfColors.blue50,
                    boldFont,
                    font,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة رصيد محسنة
  pw.Widget _buildEnhancedBalanceCard(
    String icon,
    String title,
    double amount,
    String unit,
    PdfColor amountColor,
    PdfColor backgroundColor,
    pw.Font boldFont,
    pw.Font font,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(12),
      decoration: pw.BoxDecoration(
        color: backgroundColor,
        borderRadius: pw.BorderRadius.circular(8),
        border: pw.Border.all(color: amountColor.shade(0.3), width: 1),
      ),
      child: pw.Column(
        children: [
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.center,
            children: [
              pw.Text(
                icon,
                style: const pw.TextStyle(fontSize: 20),
              ),
              pw.SizedBox(width: 8),
              pw.Text(
                title,
                style: pw.TextStyle(
                  font: font,
                  fontSize: 12,
                  color: PdfColors.grey700,
                ),
              ),
            ],
          ),
          pw.SizedBox(height: 8),
          pw.Text(
            '${amount.toStringAsFixed(2)} $unit',
            style: pw.TextStyle(
              font: boldFont,
              fontSize: 16,
              color: amountColor,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء عنصر رصيد
  pw.Widget _buildBalanceItem(String title, double amount, String unit,
      pw.Font boldFont, pw.Font font, PdfColor color) {
    return pw.Column(
      children: [
        pw.Text(title,
            style: pw.TextStyle(
                font: font, fontSize: 12, color: PdfColors.grey700)),
        pw.SizedBox(height: 4),
        pw.Text(
          '${amount.toStringAsFixed(2)} $unit',
          style: pw.TextStyle(font: boldFont, fontSize: 14, color: color),
        ),
      ],
    );
  }

  /// بناء الجدول المحاسبي الاحترافي
  pw.Widget _buildTransactionsTable(AccountStatementModel statement,
      DateFormat dateFormat, pw.Font boldFont, pw.Font font) {
    final englishDateFormat = DateFormat('yyyy-MM-dd', 'en');

    if (statement.transactions.isEmpty) {
      return pw.Container(
        padding: const pw.EdgeInsets.all(20),
        decoration: pw.BoxDecoration(
          border: pw.Border.all(color: PdfColors.grey400, width: 1),
        ),
        child: pw.Center(
          child: pw.Text(
            'لا توجد معاملات في هذه الفترة',
            style: pw.TextStyle(
                font: font, fontSize: 14, color: PdfColors.grey600),
          ),
        ),
      );
    }

    final List<pw.TableRow> tableRows = [];
    tableRows.add(
      pw.TableRow(
        decoration: const pw.BoxDecoration(color: PdfColors.grey300),
        children: [
          _buildAccountingTableHeader('التاريخ', boldFont),
          _buildAccountingTableHeader('نوع العملية', boldFont),
          _buildAccountingTableHeader('رقم المستند', boldFont),
          _buildAccountingTableHeader('رقم القيد', boldFont),
          _buildAccountingTableHeader('الوصف', boldFont),
          _buildAccountingTableHeader('مدين', boldFont),
          _buildAccountingTableHeader('دائن', boldFont),
          _buildAccountingTableHeader('الرصيد', boldFont),
        ],
      ),
    );

    // الرصيد الافتتاحي للنقد
    tableRows.add(
      pw.TableRow(
        decoration: const pw.BoxDecoration(color: PdfColors.blue50),
        children: [
          _buildAccountingTableCell(_getAccountCreationDate(statement), font),
          _buildAccountingTableCell('رصيد افتتاحي', boldFont),
          _buildAccountingTableCell('-', font),
          _buildAccountingTableCell('-', font),
          _buildAccountingTableCell('الرصيد النقدي في بداية الفترة', font),
          _buildAccountingTableCell('-', font),
          _buildAccountingTableCell('-', font),
          _buildAccountingTableCell(
              '${_formatCurrency(statement.initialCashBalance)} ريال', boldFont,
              color: PdfColors.blue800),
        ],
      ),
    );

    // الرصيد الافتتاحي للديزل
    tableRows.add(
      pw.TableRow(
        decoration: const pw.BoxDecoration(color: PdfColors.blue50),
        children: [
          _buildAccountingTableCell(_getAccountCreationDate(statement), font),
          _buildAccountingTableCell('رصيد افتتاحي', boldFont),
          _buildAccountingTableCell('-', font),
          _buildAccountingTableCell('-', font),
          _buildAccountingTableCell('رصيد الديزل في بداية الفترة', font),
          _buildAccountingTableCell('-', font),
          _buildAccountingTableCell('-', font),
          _buildAccountingTableCell(
              '${_formatCurrency(statement.initialDieselBalance)} لتر',
              boldFont,
              color: PdfColors.blue800),
        ],
      ),
    );

    for (int i = 0; i < statement.transactions.length; i++) {
      final transaction = statement.transactions[i];
      final entryNumber = _generateEntryNumber(i + 1);
      final isEven = i % 2 == 0;

      // معاملة النقدي (إذا كانت موجودة)
      if (transaction.cashAmount != 0) {
        tableRows.add(
          pw.TableRow(
            decoration: pw.BoxDecoration(
              color: isEven ? PdfColors.grey50 : PdfColors.white,
            ),
            children: [
              _buildAccountingTableCell(
                  englishDateFormat.format(transaction.date), font),
              _buildAccountingTableCell(
                  _getTransactionTypeLabel(transaction.type), font),
              _buildAccountingTableCell(transaction.referenceId ?? '-', font),
              _buildAccountingTableCell('${entryNumber}A', font),
              _buildEnhancedDescriptionCell(transaction, 'نقد', font, boldFont),
              _buildAccountingTableCell(
                transaction.cashAmount < 0
                    ? '${_formatCurrency(transaction.cashAmount.abs())} ريال'
                    : '-',
                font,
                color: transaction.cashAmount < 0
                    ? PdfColors.red700
                    : PdfColors.black,
              ),
              _buildAccountingTableCell(
                transaction.cashAmount > 0
                    ? '${_formatCurrency(transaction.cashAmount)} ريال'
                    : '-',
                font,
                color: transaction.cashAmount > 0
                    ? PdfColors.green700
                    : PdfColors.black,
              ),
              _buildAccountingTableCell(
                '${_formatCurrency(transaction.runningCashBalance)} ريال',
                boldFont,
                color: PdfColors.blue800,
              ),
            ],
          ),
        );
      }

      // معاملة الديزل (إذا كانت موجودة)
      if (transaction.dieselAmount != 0) {
        tableRows.add(
          pw.TableRow(
            decoration: pw.BoxDecoration(
              color: isEven ? PdfColors.grey50 : PdfColors.white,
            ),
            children: [
              _buildAccountingTableCell(
                  englishDateFormat.format(transaction.date), font),
              _buildAccountingTableCell(
                  _getTransactionTypeLabel(transaction.type), font),
              _buildAccountingTableCell(transaction.referenceId ?? '-', font),
              _buildAccountingTableCell('${entryNumber}B', font),
              _buildEnhancedDescriptionCell(
                  transaction, 'ديزل', font, boldFont),
              _buildAccountingTableCell(
                transaction.dieselAmount < 0
                    ? '${_formatCurrency(transaction.dieselAmount.abs())} لتر'
                    : '-',
                font,
                color: transaction.dieselAmount < 0
                    ? PdfColors.red700
                    : PdfColors.black,
              ),
              _buildAccountingTableCell(
                transaction.dieselAmount > 0
                    ? '${_formatCurrency(transaction.dieselAmount)} لتر'
                    : '-',
                font,
                color: transaction.dieselAmount > 0
                    ? PdfColors.green700
                    : PdfColors.black,
              ),
              _buildAccountingTableCell(
                '${_formatCurrency(transaction.runningDieselBalance)} لتر',
                boldFont,
                color: PdfColors.orange700,
              ),
            ],
          ),
        );
      }
    }

    // الرصيد الختامي للنقد
    tableRows.add(
      pw.TableRow(
        decoration: const pw.BoxDecoration(color: PdfColors.green50),
        children: [
          _buildAccountingTableCell(
              englishDateFormat.format(statement.toDate), font),
          _buildAccountingTableCell('رصيد ختامي', boldFont),
          _buildAccountingTableCell('-', font),
          _buildAccountingTableCell('-', font),
          _buildAccountingTableCell('الرصيد النقدي في نهاية الفترة', font),
          _buildAccountingTableCell('-', font),
          _buildAccountingTableCell('-', font),
          _buildAccountingTableCell(
              '${_formatCurrency(statement.finalCashBalance)} ريال', boldFont,
              color: PdfColors.green800),
        ],
      ),
    );

    // الرصيد الختامي للديزل
    tableRows.add(
      pw.TableRow(
        decoration: const pw.BoxDecoration(color: PdfColors.green50),
        children: [
          _buildAccountingTableCell(
              englishDateFormat.format(statement.toDate), font),
          _buildAccountingTableCell('رصيد ختامي', boldFont),
          _buildAccountingTableCell('-', font),
          _buildAccountingTableCell('-', font),
          _buildAccountingTableCell('الرصيد الديزل في نهاية الفترة', font),
          _buildAccountingTableCell('-', font),
          _buildAccountingTableCell('-', font),
          _buildAccountingTableCell(
              '${_formatCurrency(statement.finalDieselBalance)} لتر', boldFont,
              color: PdfColors.green800),
        ],
      ),
    );

    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey400, width: 1),
      columnWidths: const {
        0: pw.FixedColumnWidth(80), // التاريخ (أوسع)
        1: pw.FixedColumnWidth(90), // نوع العملية (أوسع)
        2: pw.FixedColumnWidth(70), // رقم المستند
        3: pw.FixedColumnWidth(60), // رقم القيد
        4: pw.FlexColumnWidth(6), // الوصف (أعرض بكثير)
        5: pw.FixedColumnWidth(80), // مدين
        6: pw.FixedColumnWidth(80), // دائن
        7: pw.FixedColumnWidth(95), // الرصيد
      },
      children: tableRows,
    );
  }

  /// بناء رأس عمود الجدول
  pw.Widget _buildTableHeader(String text, pw.Font boldFont) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          font: boldFont,
          fontSize: 12,
          color: PdfColors.white,
        ),
        textAlign: pw.TextAlign.center,
      ),
    );
  }

  /// بناء خلية الجدول
  pw.Widget _buildTableCell(
    String text,
    pw.Font font, {
    double fontSize = 11,
    PdfColor? color,
    int maxLines = 1,
  }) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(6),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          font: font,
          fontSize: fontSize,
          color: color ?? PdfColors.black,
        ),
        textAlign: pw.TextAlign.center,
        maxLines: maxLines,
      ),
    );
  }

  /// بناء رأس جدول محسن
  pw.Widget _buildEnhancedTableHeader(String text, pw.Font boldFont) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(10),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          font: boldFont,
          fontSize: 11,
          color: PdfColors.white,
        ),
        textAlign: pw.TextAlign.center,
      ),
    );
  }

  /// بناء خلية جدول محسنة
  pw.Widget _buildEnhancedTableCell(
    String text,
    pw.Font font, {
    double fontSize = 11,
    PdfColor? color,
  }) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          font: font,
          fontSize: fontSize,
          color: color ?? PdfColors.black,
        ),
        textAlign: pw.TextAlign.center,
      ),
    );
  }

  /// بناء خلية تفاصيل المعاملة
  pw.Widget _buildEnhancedDescriptionCell(AccountTransactionModel transaction,
      String type, pw.Font font, pw.Font boldFont) {
    final icon = _getTransactionIcon(transaction.type);
    final description = _buildEnhancedTransactionDescription(transaction, type);
    return pw.Row(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(icon, style: pw.TextStyle(fontSize: 15)),
        pw.SizedBox(width: 6),
        pw.Expanded(
          child: pw.Text(
            description,
            style: pw.TextStyle(
                font: font, fontSize: 12, color: PdfColors.grey800),
            maxLines: 6,
            textDirection: pw.TextDirection.rtl,
            softWrap: true,
          ),
        ),
      ],
    );
  }

  /// بناء خلية الرصيد
  pw.Widget _buildBalanceCell(
    double balance,
    String unit,
    pw.Font font,
    pw.Font boldFont,
    PdfColor color,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      decoration: pw.BoxDecoration(
        color: color.shade(0.1),
        borderRadius: pw.BorderRadius.circular(4),
      ),
      child: pw.Column(
        children: [
          pw.Text(
            balance.toStringAsFixed(2),
            style: pw.TextStyle(
              font: boldFont,
              fontSize: 12,
              color: color,
            ),
          ),
          pw.Text(
            unit,
            style: pw.TextStyle(
              font: font,
              fontSize: 8,
              color: PdfColors.grey600,
            ),
          ),
        ],
      ),
    );
  }

  /// الحصول على أيقونة المعاملة
  String _getTransactionIcon(TransactionType type) {
    switch (type) {
      case TransactionType.irrigation:
        return '💧';
      case TransactionType.cashPayment:
        return '💰';
      case TransactionType.dieselPayment:
        return '⛽';
      case TransactionType.transfer:
        return '🔄';
      case TransactionType.adjustment:
        return '⚖️';
    }
  }

  /// تنسيق وصف المعاملة
  String _formatDescription(AccountTransactionModel transaction) {
    String description = transaction.description;
    if (transaction.farmName != null && transaction.farmName!.isNotEmpty) {
      description += ' - ${transaction.farmName}';
    }
    if (transaction.duration != null && transaction.duration! > 0) {
      description += ' (${transaction.duration!.toStringAsFixed(1)} ساعة)';
    }
    return description;
  }

  /// تنسيق المبلغ
  String _formatAmount(double amount) {
    if (amount == 0) return '-';
    return amount > 0
        ? '+${amount.toStringAsFixed(2)}'
        : amount.toStringAsFixed(2);
  }

  /// الحصول على لون المبلغ
  PdfColor _getAmountColor(double amount) {
    if (amount > 0) return PdfColors.green700;
    if (amount < 0) return PdfColors.red700;
    return PdfColors.grey600;
  }

  /// ترجمة نوع العملية
  String _getTransactionTypeLabel(TransactionType type) {
    switch (type) {
      case TransactionType.irrigation:
        return 'تسقية';
      case TransactionType.cashPayment:
        return 'دفعة نقدية';
      case TransactionType.dieselPayment:
        return 'دفعة ديزل';
      case TransactionType.transfer:
        return 'تحويل';
      case TransactionType.adjustment:
        return 'تسوية';
    }
  }

  /// إنشاء رقم حساب فريد
  String _generateAccountNumber(String clientId) {
    final baseNumber = '122010';
    final paddedId = clientId.padLeft(3, '0');
    return '$baseNumber$paddedId';
  }

  /// إنشاء رقم قيد محاسبي
  String _generateEntryNumber(int index) {
    return index.toString().padLeft(4, '0');
  }

  /// تنسيق العملة
  String _formatCurrency(double amount) {
    final formatter = NumberFormat('#,##0.00', 'en');
    return formatter.format(amount);
  }

  /// بناء وصف المعاملة
  String _buildTransactionDescription(AccountTransactionModel transaction) {
    String description = transaction.description;
    if (transaction.farmName != null && transaction.farmName!.isNotEmpty) {
      description += ' - ${transaction.farmName}';
    }
    if (transaction.duration != null && transaction.duration! > 0) {
      description += ' (${transaction.duration!.toStringAsFixed(1)} ساعة)';
    }
    if (transaction.notes != null && transaction.notes!.isNotEmpty) {
      description += ' - ${transaction.notes}';
    }
    return description;
  }

  /// بناء وصف المعاملة المحسن والواضح
  String _buildEnhancedTransactionDescription(
      AccountTransactionModel transaction, String type) {
    String description = '';

    // تحديد نوع العملية مع نوع المعاملة
    switch (transaction.type) {
      case TransactionType.irrigation:
        description = _buildIrrigationDescription(transaction, type);
        break;
      case TransactionType.cashPayment:
        description = _buildPaymentDescription(transaction, type);
        break;
      case TransactionType.dieselPayment:
        description = _buildPaymentDescription(transaction, type);
        break;
      case TransactionType.transfer:
        description = _buildTransferDescription(transaction, type);
        break;
      case TransactionType.adjustment:
        description = _buildAdjustmentDescription(transaction, type);
        break;
    }

    return description;
  }

  /// بناء وصف التسقية
  String _buildIrrigationDescription(
      AccountTransactionModel transaction, String type) {
    String description = 'تسقية ${type == 'نقد' ? 'نقدية' : 'ديزل'}';

    // إضافة اسم المزرعة
    if (transaction.farmName != null && transaction.farmName!.isNotEmpty) {
      description += ' - مزرعة ${transaction.farmName}';
    }

    // إضافة مدة التسقية
    if (transaction.duration != null && transaction.duration! > 0) {
      description += ' (${transaction.duration!.toStringAsFixed(1)} ساعة)';
    }

    // إضافة ملاحظات إضافية
    if (transaction.notes != null && transaction.notes!.isNotEmpty) {
      description += ' - ${transaction.notes}';
    }

    return description;
  }

  /// بناء وصف الدفعات
  String _buildPaymentDescription(
      AccountTransactionModel transaction, String type) {
    String description = 'دفعة ${type == 'نقد' ? 'نقدية' : 'ديزل'}';

    // إضافة تفاصيل الدفعة
    if (transaction.notes != null && transaction.notes!.isNotEmpty) {
      description += ' - ${transaction.notes}';
    } else {
      description += ' من العميل';
    }

    return description;
  }

  /// بناء وصف التحويلات
  String _buildTransferDescription(
      AccountTransactionModel transaction, String type) {
    String transferType = type == 'نقد' ? 'نقدي' : 'ديزل';
    String description = '';

    // تحديد اتجاه التحويل بناءً على الإشارة
    bool isOutgoing = (type == 'نقد' && transaction.cashAmount < 0) ||
        (type == 'ديزل' && transaction.dieselAmount < 0);

    if (transaction.notes != null && transaction.notes!.isNotEmpty) {
      // تحديد نوع التحويل (عميل أم صندوق)
      if (_isBoxTransfer(transaction.notes!)) {
        // تحويل بين الصناديق
        if (isOutgoing) {
          description =
              'تحويل $transferType إلى صندوق: ${_extractBoxName(transaction.notes!)}';
        } else {
          description =
              'تحويل $transferType من صندوق: ${_extractBoxName(transaction.notes!)}';
        }
      } else {
        // تحويل بين العملاء
        if (isOutgoing) {
          description = 'تحويل $transferType إلى: ${transaction.notes}';
        } else {
          description = 'تحويل $transferType من: ${transaction.notes}';
        }
      }
    } else {
      // تحويل عام بدون تفاصيل
      if (isOutgoing) {
        description = 'تحويل $transferType إلى عميل آخر';
      } else {
        description = 'تحويل $transferType من عميل آخر';
      }
    }

    return description;
  }

  /// بناء وصف التسويات
  String _buildAdjustmentDescription(
      AccountTransactionModel transaction, String type) {
    String description = 'تسوية ${type == 'نقد' ? 'نقدية' : 'ديزل'}';

    // إضافة سبب التسوية
    if (transaction.notes != null && transaction.notes!.isNotEmpty) {
      description += ' - ${transaction.notes}';
    }

    return description;
  }

  /// تحديد ما إذا كان التحويل بين الصناديق
  bool _isBoxTransfer(String notes) {
    return notes.contains('صندوق') ||
        notes.contains('محطة') ||
        notes.contains('مستودع') ||
        notes.contains('خزان');
  }

  /// استخراج اسم الصندوق من الملاحظات
  String _extractBoxName(String notes) {
    // إزالة كلمات مثل "صندوق" أو "محطة" للحصول على الاسم فقط
    return notes
        .replaceAll('صندوق', '')
        .replaceAll('محطة', '')
        .replaceAll('مستودع', '')
        .replaceAll('خزان', '')
        .trim();
  }

  /// الحصول على تاريخ إنشاء الحساب
  String _getAccountCreationDate(AccountStatementModel statement) {
    // يمكن تحسين هذا ليأخذ التاريخ الفعلي من قاعدة البيانات
    // حالياً سنستخدم تاريخ بداية الفترة كمثال
    final englishDateFormat = DateFormat('yyyy-MM-dd', 'en');
    return englishDateFormat.format(statement.fromDate);
  }

  /// بناء رأس الجدول المحاسبي
  pw.Widget _buildAccountingTableHeader(String text, pw.Font boldFont) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      decoration: const pw.BoxDecoration(
        border: pw.Border(
          bottom: pw.BorderSide(color: PdfColors.grey600, width: 2),
        ),
      ),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          font: boldFont,
          fontSize: 12,
          color: PdfColors.black,
        ),
        textAlign: pw.TextAlign.center,
      ),
    );
  }

  /// بناء خلية الجدول المحاسبي
  pw.Widget _buildAccountingTableCell(
    String text,
    pw.Font font, {
    PdfColor? color,
    double fontSize = 9,
    int maxLines = 3,
  }) {
    return pw.Container(
      padding: const pw.EdgeInsets.symmetric(horizontal: 4, vertical: 6),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          font: font,
          fontSize: fontSize,
          color: color ?? PdfColors.black,
        ),
        textAlign: pw.TextAlign.center,
        maxLines: maxLines,
        overflow: pw.TextOverflow.clip,
      ),
    );
  }

  /// بناء تذييل الصفحة
  pw.Widget _buildFooter(pw.Font font) {
    final now = DateTime.now();
    final printTime = DateFormat('dd-MM-yyyy HH:mm', 'en').format(now);

    return pw.Container(
      padding: const pw.EdgeInsets.symmetric(vertical: 16),
      decoration: const pw.BoxDecoration(
        border: pw.Border(
          top: pw.BorderSide(color: PdfColors.grey400, width: 1),
        ),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          // اسم الشركة (يسار)
          pw.Text(
            'مياه المراوح',
            style: pw.TextStyle(
              font: font,
              fontSize: 10,
              color: PdfColors.grey600,
            ),
          ),
          // تاريخ الطباعة (وسط)
          pw.Text(
            'تاريخ الطباعة: $printTime',
            style: pw.TextStyle(
              font: font,
              fontSize: 10,
              color: PdfColors.grey600,
            ),
          ),
          // رقم الصفحة (يمين)
          pw.Text(
            'صفحة 1 من 1',
            style: pw.TextStyle(
              font: font,
              fontSize: 10,
              color: PdfColors.grey600,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء ملخص مالي شامل
  pw.Widget _buildFinancialSummarySection(
      Map<String, double> stats, pw.Font boldFont, pw.Font font) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(12),
      decoration: pw.BoxDecoration(
        color: PdfColors.indigo50,
        borderRadius: pw.BorderRadius.circular(8),
        border: pw.Border.all(color: PdfColors.indigo200, width: 1),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text('ملخص مالي للفترة',
              style: pw.TextStyle(
                  font: boldFont, fontSize: 15, color: PdfColors.indigo900)),
          pw.SizedBox(height: 8),
          pw.Wrap(
            spacing: 16,
            runSpacing: 8,
            children: [
              _buildSummaryItem('إجمالي الإيرادات',
                  _formatCurrency(stats['totalRevenue'] ?? 0), boldFont, font),
              _buildSummaryItem('إجمالي المدفوعات',
                  _formatCurrency(stats['totalPayments'] ?? 0), boldFont, font),
              _buildSummaryItem('تكلفة الديزل',
                  _formatCurrency(stats['dieselCost'] ?? 0), boldFont, font),
              _buildSummaryItem(
                  'المصروفات التشغيلية',
                  _formatCurrency(stats['operationalExpenses'] ?? 0),
                  boldFont,
                  font),
              _buildSummaryItem('صافي الربح',
                  _formatCurrency(stats['netProfit'] ?? 0), boldFont, font),
              _buildSummaryItem(
                  'رصيد النقد',
                  _formatCurrency(stats['totalCashBalance'] ?? 0),
                  boldFont,
                  font),
              _buildSummaryItem(
                  'رصيد الديزل',
                  _formatCurrency(stats['totalDieselBalance'] ?? 0),
                  boldFont,
                  font),
              _buildSummaryItem(
                  'ديون العملاء',
                  _formatCurrency(stats['totalClientDebt'] ?? 0),
                  boldFont,
                  font),
              _buildSummaryItem(
                  'دائن العملاء',
                  _formatCurrency(stats['totalClientCredit'] ?? 0),
                  boldFont,
                  font),
              _buildSummaryItem('التدفق النقدي',
                  _formatCurrency(stats['cashFlow'] ?? 0), boldFont, font),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء جدول العمليات المالية
  pw.Widget _buildFinancialTransactionsTable(
      List<Map<String, dynamic>> transactions,
      DateFormat dateFormat,
      pw.Font boldFont,
      pw.Font font) {
    final rows = <pw.TableRow>[];
    // رأس الجدول
    rows.add(
      pw.TableRow(
        decoration: const pw.BoxDecoration(color: PdfColors.grey300),
        children: [
          _buildAccountingTableHeader('التاريخ', boldFont),
          _buildAccountingTableHeader('نوع العملية', boldFont),
          _buildAccountingTableHeader('الوصف', boldFont),
          _buildAccountingTableHeader('إيراد', boldFont),
          _buildAccountingTableHeader('مصروف', boldFont),
          _buildAccountingTableHeader('الرصيد', boldFont),
        ],
      ),
    );
    for (var i = 0; i < transactions.length; i++) {
      final t = transactions[i];
      final isEven = i % 2 == 0;
      rows.add(
        pw.TableRow(
          decoration: pw.BoxDecoration(
              color: isEven ? PdfColors.grey50 : PdfColors.white),
          children: [
            _buildAccountingTableCell(dateFormat.format(t['date']), font),
            _buildAccountingTableCell(t['type'] ?? '-', font),
            _buildAccountingTableCell(t['description'] ?? '-', font,
                fontSize: 11, maxLines: 4),
            _buildAccountingTableCell(_formatCurrency(t['revenue'] ?? 0), font,
                color: PdfColors.green700),
            _buildAccountingTableCell(_formatCurrency(t['expense'] ?? 0), font,
                color: PdfColors.red700),
            _buildAccountingTableCell(_formatCurrency(t['balance'] ?? 0), font,
                color: PdfColors.blue800),
          ],
        ),
      );
    }
    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey400, width: 1),
      columnWidths: const {
        0: pw.FixedColumnWidth(80), // التاريخ
        1: pw.FixedColumnWidth(90), // نوع العملية
        2: pw.FlexColumnWidth(6), // الوصف
        3: pw.FixedColumnWidth(70), // إيراد
        4: pw.FixedColumnWidth(70), // مصروف
        5: pw.FixedColumnWidth(90), // الرصيد
      },
      children: rows,
    );
  }

  pw.Widget _buildSummaryItem(
      String label, String value, pw.Font boldFont, pw.Font font) {
    return pw.Column(
      children: [
        pw.Text(label,
            style: pw.TextStyle(
                font: font, fontSize: 10, color: PdfColors.indigo800)),
        pw.SizedBox(height: 2),
        pw.Text(value,
            style: pw.TextStyle(
                font: boldFont, fontSize: 13, color: PdfColors.indigo900)),
      ],
    );
  }
}

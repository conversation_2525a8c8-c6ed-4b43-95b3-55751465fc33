import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'dart:async';
import 'package:untitled/data/models/client_model.dart';
import 'package:untitled/data/models/farm_model.dart';
import 'package:untitled/data/models/irrigation_model.dart';
import 'package:untitled/data/models/cashbox_model.dart';
import 'package:untitled/data/models/client_account_model.dart';
import 'package:untitled/data/models/payment_model.dart';
import 'package:untitled/presentation/blocs/client/client_bloc.dart';
import 'package:untitled/presentation/blocs/client/client_event.dart';
import 'package:untitled/presentation/blocs/client/client_state.dart';
import 'package:untitled/presentation/blocs/farm/farm_bloc.dart';
import 'package:untitled/presentation/blocs/farm/farm_event.dart';
import 'package:untitled/presentation/blocs/farm/farm_state.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_bloc.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_event.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_state.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_bloc.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_event.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_state.dart';
import 'package:untitled/presentation/blocs/client_account/client_account_bloc.dart';
import 'package:untitled/presentation/blocs/client_account/client_account_event.dart';
import 'package:untitled/presentation/blocs/client_account/client_account_state.dart';
import 'package:untitled/presentation/blocs/payment/payment_bloc.dart';
import 'package:untitled/presentation/blocs/payment/payment_event.dart';
import 'package:untitled/presentation/blocs/payment/payment_state.dart';
import 'package:untitled/core/theme/app_theme.dart';
import 'package:untitled/services/account_statement_service.dart';
import 'package:untitled/services/pdf_service.dart';
import 'package:printing/printing.dart';
import 'package:untitled/core/call_stack_monitor.dart';

/// صفحة التقارير الشاملة المحسنة مع منع التعليق
class ComprehensiveReportsPage extends StatefulWidget {
  const ComprehensiveReportsPage({super.key});

  @override
  State<ComprehensiveReportsPage> createState() =>
      _ComprehensiveReportsPageState();
}

class _ComprehensiveReportsPageState extends State<ComprehensiveReportsPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final CallStackMonitor _callStackMonitor = CallStackMonitor();

  // البيانات
  List<ClientModel> _clients = [];
  List<FarmModel> _farms = [];
  List<IrrigationModel> _irrigations = [];
  List<CashboxModel> _cashboxes = [];
  List<ClientAccountModel> _accounts = [];
  List<PaymentModel> _payments = [];

  bool _isLoading = false;
  int _loadedCount = 0;
  final int _totalDataSources = 6;
  Timer? _loadingTimeout;
  bool _hasError = false;
  String _errorMessage = '';
  bool _isDataLoaded = false;

  // فلاتر التاريخ
  DateTime? _startDate;
  DateTime? _endDate;
  final DateFormat _dateFormat = DateFormat('yyyy-MM-dd');

  @override
  void initState() {
    super.initState();
    debugPrint('🔍 [ComprehensiveReportsPage] initState started');
    _tabController = TabController(length: 5, vsync: this);
    _initializeDateRange();

    // تأخير بسيط قبل بدء التحميل لتجنب التعليق
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted) {
        _loadAllDataWithTimeout();
      }
    });

    debugPrint('🔍 [ComprehensiveReportsPage] initState completed');
  }

  void _initializeDateRange() {
    debugPrint('🔍 [ComprehensiveReportsPage] _initializeDateRange started');
    final now = DateTime.now();
    _startDate = DateTime(now.year, now.month, 1); // بداية الشهر الحالي
    _endDate = now; // اليوم الحالي
    debugPrint('🔍 [ComprehensiveReportsPage] _initializeDateRange completed');
  }

  @override
  void dispose() {
    debugPrint('🔍 [ComprehensiveReportsPage] dispose started');
    _loadingTimeout?.cancel();
    _tabController.dispose();
    super.dispose();
    debugPrint('🔍 [ComprehensiveReportsPage] dispose completed');
  }

  void _loadAllDataWithTimeout() {
    debugPrint('🔍 [ComprehensiveReportsPage] _loadAllDataWithTimeout started');

    // التحقق من عدم وجود تحميل سابق
    if (_isLoading) {
      debugPrint('⚠️ [ComprehensiveReportsPage] Loading already in progress');
      return;
    }

    setState(() {
      _isLoading = true;
      _loadedCount = 0;
      _hasError = false;
      _errorMessage = '';
      _isDataLoaded = false;
    });

    // إلغاء أي timeout سابق
    _loadingTimeout?.cancel();

    // تعيين timeout جديد (20 ثانية بدلاً من 30)
    _loadingTimeout = Timer(const Duration(seconds: 20), () {
      if (mounted && _isLoading) {
        debugPrint('⏰ [ComprehensiveReportsPage] Loading timeout reached');
        setState(() {
          _isLoading = false;
          _hasError = true;
          _errorMessage = 'انتهت مهلة تحميل البيانات. يرجى المحاولة مرة أخرى.';
        });
      }
    });

    // تحميل البيانات بشكل متتابع مع مراقبة Call Stack
    _loadDataSequentially();
  }

  void _loadDataSequentially() async {
    debugPrint('🔍 [ComprehensiveReportsPage] _loadDataSequentially started');

    try {
      // تحميل العملاء أولاً
      _callStackMonitor.executeWithMonitoring('LoadClients', () {
        context.read<ClientBloc>().add(const LoadClients());
      });
      await Future.delayed(const Duration(milliseconds: 300));

      // تحميل المزارع
      _callStackMonitor.executeWithMonitoring('LoadFarms', () {
        context.read<FarmBloc>().add(const LoadFarms());
      });
      await Future.delayed(const Duration(milliseconds: 300));

      // تحميل التسقيات
      _callStackMonitor.executeWithMonitoring('LoadIrrigations', () {
        context.read<IrrigationBloc>().add(const LoadIrrigations());
      });
      await Future.delayed(const Duration(milliseconds: 300));

      // تحميل الصناديق
      _callStackMonitor.executeWithMonitoring('LoadCashboxes', () {
        context.read<CashboxBloc>().add(const LoadCashboxes());
      });
      await Future.delayed(const Duration(milliseconds: 300));

      // تحميل حسابات العملاء
      _callStackMonitor.executeWithMonitoring('LoadAllClientAccounts', () {
        context.read<ClientAccountBloc>().add(const LoadAllClientAccounts());
      });
      await Future.delayed(const Duration(milliseconds: 300));

      // تحميل المدفوعات
      _callStackMonitor.executeWithMonitoring('LoadPayments', () {
        context.read<PaymentBloc>().add(const LoadPayments());
      });

      debugPrint(
          '🔍 [ComprehensiveReportsPage] _loadDataSequentially completed');
    } catch (e) {
      debugPrint(
          '❌ [ComprehensiveReportsPage] _loadDataSequentially error: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
          _hasError = true;
          _errorMessage = 'خطأ في تحميل البيانات: ${e.toString()}';
        });
      }
    }
  }

  void _checkDataLoaded() {
    debugPrint(
        '🔍 [ComprehensiveReportsPage] _checkDataLoaded - count: $_loadedCount');
    _loadedCount++;
    if (_loadedCount >= _totalDataSources) {
      debugPrint(
          '🔍 [ComprehensiveReportsPage] All data loaded, setting isLoading to false');
      _loadingTimeout?.cancel();
      if (mounted) {
        setState(() {
          _isLoading = false;
          _isDataLoaded = true;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    debugPrint(
        '🔍 [ComprehensiveReportsPage] build started - isLoading: $_isLoading');

    return _callStackMonitor
        .executeWithMonitoring('ComprehensiveReportsPage.build', () {
      return Scaffold(
        backgroundColor: Colors.grey[50],
        appBar: _buildAppBar(),
        body: MultiBlocListener(
          listeners: _buildBlocListeners(),
          child: _isLoading
              ? _buildLoadingWidget()
              : _hasError
                  ? _buildErrorWidget()
                  : _isDataLoaded
                      ? _buildMainContent()
                      : _buildEmptyState('لا توجد بيانات', Icons.data_usage),
        ),
        floatingActionButton: _buildFloatingActionButton(),
      );
    });
  }

  Widget _buildMainContent() {
    return Column(
      children: [
        _buildDateFilterSection(),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildOverviewTab(),
              _buildClientsTab(),
              _buildFarmsTab(),
              _buildIrrigationsTab(),
              _buildFinancialTab(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildErrorWidget() {
    return Container(
      color: Colors.white,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          Text(
            'خطأ في تحميل البيانات',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade700,
            ),
          ),
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: Text(
              _errorMessage,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade600,
              ),
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _loadAllDataWithTimeout,
            icon: const Icon(Icons.refresh),
            label: const Text('إعادة المحاولة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    debugPrint('🔍 [ComprehensiveReportsPage] _buildAppBar started');
    return AppBar(
      title: const Text(
        'التقارير الشاملة',
        style: TextStyle(fontWeight: FontWeight.bold),
      ),
      backgroundColor: AppTheme.primaryColor,
      foregroundColor: Colors.white,
      elevation: 0,
      actions: [
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: _loadAllDataWithTimeout,
          tooltip: 'تحديث البيانات',
        ),
        IconButton(
          icon: const Icon(Icons.file_download),
          onPressed: _exportReport,
          tooltip: 'تصدير التقرير',
        ),
      ],
      bottom: TabBar(
        controller: _tabController,
        isScrollable: true,
        indicatorColor: Colors.white,
        indicatorWeight: 3,
        labelColor: Colors.white,
        unselectedLabelColor: Colors.white70,
        labelStyle: const TextStyle(fontWeight: FontWeight.bold),
        tabs: const [
          Tab(icon: Icon(Icons.dashboard), text: 'نظرة عامة'),
          Tab(icon: Icon(Icons.people), text: 'العملاء'),
          Tab(icon: Icon(Icons.landscape), text: 'المزارع'),
          Tab(icon: Icon(Icons.water_drop), text: 'التسقيات'),
          Tab(icon: Icon(Icons.account_balance), text: 'المالية'),
        ],
      ),
    );
    debugPrint('🔍 [ComprehensiveReportsPage] _buildAppBar completed');
  }

  List<BlocListener> _buildBlocListeners() {
    debugPrint('🔍 [ComprehensiveReportsPage] _buildBlocListeners started');
    return [
      BlocListener<ClientBloc, ClientState>(
        listener: (context, state) {
          debugPrint(
              '🔍 [ComprehensiveReportsPage] ClientBloc state: ${state.runtimeType}');
          if (state is ClientsLoaded) {
            debugPrint(
                '🔍 [ComprehensiveReportsPage] Clients loaded: ${state.clients.length}');
            setState(() => _clients = state.clients);
            _checkDataLoaded();
          }
        },
      ),
      BlocListener<FarmBloc, FarmState>(
        listener: (context, state) {
          debugPrint(
              '🔍 [ComprehensiveReportsPage] FarmBloc state: ${state.runtimeType}');
          if (state is FarmsLoaded) {
            debugPrint(
                '🔍 [ComprehensiveReportsPage] Farms loaded: ${state.farms.length}');
            setState(() => _farms = state.farms);
            _checkDataLoaded();
          }
        },
      ),
      BlocListener<IrrigationBloc, IrrigationState>(
        listener: (context, state) {
          debugPrint(
              '🔍 [ComprehensiveReportsPage] IrrigationBloc state: ${state.runtimeType}');
          if (state is IrrigationsLoaded) {
            debugPrint(
                '🔍 [ComprehensiveReportsPage] Irrigations loaded: ${state.irrigations.length}');
            setState(() => _irrigations = state.irrigations);
            _checkDataLoaded();
          }
        },
      ),
      BlocListener<CashboxBloc, CashboxState>(
        listener: (context, state) {
          debugPrint(
              '🔍 [ComprehensiveReportsPage] CashboxBloc state: ${state.runtimeType}');
          if (state is CashboxesLoaded) {
            debugPrint(
                '🔍 [ComprehensiveReportsPage] Cashboxes loaded: ${state.cashboxes.length}');
            setState(() => _cashboxes = state.cashboxes);
            _checkDataLoaded();
          }
        },
      ),
      BlocListener<ClientAccountBloc, ClientAccountState>(
        listener: (context, state) {
          debugPrint(
              '🔍 [ComprehensiveReportsPage] ClientAccountBloc state: ${state.runtimeType}');
          if (state is AllClientAccountsLoaded) {
            debugPrint(
                '🔍 [ComprehensiveReportsPage] ClientAccounts loaded: ${state.accounts.length}');
            setState(() => _accounts = state.accounts);
            _checkDataLoaded();
          } else if (state is ClientAccountError) {
            debugPrint(
                '❌ [ComprehensiveReportsPage] ClientAccount error: ${state.message}');
            // تجاهل خطأ حسابات العملاء والاستمرار
            _checkDataLoaded();
          }
        },
      ),
      BlocListener<PaymentBloc, PaymentState>(
        listener: (context, state) {
          debugPrint(
              '🔍 [ComprehensiveReportsPage] PaymentBloc state: ${state.runtimeType}');
          if (state is PaymentsLoaded) {
            debugPrint(
                '🔍 [ComprehensiveReportsPage] Payments loaded: ${state.payments.length}');
            setState(() => _payments = state.payments);
            _checkDataLoaded();
          }
        },
      ),
    ];
    debugPrint('🔍 [ComprehensiveReportsPage] _buildBlocListeners completed');
  }

  Widget _buildLoadingWidget() {
    debugPrint('🔍 [ComprehensiveReportsPage] _buildLoadingWidget started');
    return Container(
      color: Colors.white,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
          ),
          const SizedBox(height: 16),
          Text(
            'جاري تحميل البيانات...',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'تم تحميل $_loadedCount من $_totalDataSources',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
          const SizedBox(height: 16),
          // إضافة زر إلغاء التحميل
          TextButton(
            onPressed: () {
              _loadingTimeout?.cancel();
              setState(() {
                _isLoading = false;
                _hasError = true;
                _errorMessage = 'تم إلغاء التحميل من قبل المستخدم.';
              });
            },
            child: const Text('إلغاء التحميل'),
          ),
        ],
      ),
    );
    debugPrint('🔍 [ComprehensiveReportsPage] _buildLoadingWidget completed');
  }

  Widget _buildDateFilterSection() {
    debugPrint('🔍 [ComprehensiveReportsPage] _buildDateFilterSection started');
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          const Icon(Icons.date_range, color: AppTheme.primaryColor),
          const SizedBox(width: 8),
          const Text(
            'فترة التقرير:',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Row(
              children: [
                Expanded(
                  child: _buildDateButton(
                    'من: ${_dateFormat.format(_startDate!)}',
                    () => _selectStartDate(),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildDateButton(
                    'إلى: ${_dateFormat.format(_endDate!)}',
                    () => _selectEndDate(),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
    debugPrint(
        '🔍 [ComprehensiveReportsPage] _buildDateFilterSection completed');
  }

  Widget _buildDateButton(String text, VoidCallback onPressed) {
    debugPrint('🔍 [ComprehensiveReportsPage] _buildDateButton - text: $text');
    return OutlinedButton(
      onPressed: onPressed,
      style: OutlinedButton.styleFrom(
        side: const BorderSide(color: AppTheme.primaryColor),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
      child: Text(
        text,
        style: const TextStyle(color: AppTheme.primaryColor),
      ),
    );
  }

  Future<void> _selectStartDate() async {
    debugPrint('🔍 [ComprehensiveReportsPage] _selectStartDate started');
    final date = await showDatePicker(
      context: context,
      initialDate: _startDate!,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (date != null) {
      setState(() => _startDate = date);
      debugPrint(
          '🔍 [ComprehensiveReportsPage] _selectStartDate - selected: $date');
    }
    debugPrint('🔍 [ComprehensiveReportsPage] _selectStartDate completed');
  }

  Future<void> _selectEndDate() async {
    debugPrint('🔍 [ComprehensiveReportsPage] _selectEndDate started');
    final date = await showDatePicker(
      context: context,
      initialDate: _endDate!,
      firstDate: _startDate!,
      lastDate: DateTime.now(),
    );
    if (date != null) {
      setState(() => _endDate = date);
      debugPrint(
          '🔍 [ComprehensiveReportsPage] _selectEndDate - selected: $date');
    }
    debugPrint('🔍 [ComprehensiveReportsPage] _selectEndDate completed');
  }

  // تبويب النظرة العامة
  Widget _buildOverviewTab() {
    debugPrint('🔍 [ComprehensiveReportsPage] _buildOverviewTab started');
    return _callStackMonitor.executeWithMonitoring('_buildOverviewTab', () {
      return SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionHeader('الإحصائيات العامة', Icons.analytics),
            const SizedBox(height: 16),
            _buildOverviewStats(),
            const SizedBox(height: 24),
            _buildSectionHeader('الأنشطة الحديثة', Icons.history),
            const SizedBox(height: 16),
            _buildRecentActivities(),
            const SizedBox(height: 24),
            _buildSectionHeader('الرسوم البيانية', Icons.bar_chart),
            const SizedBox(height: 16),
            _buildChartsSection(),
          ],
        ),
      );
    });
    debugPrint('🔍 [ComprehensiveReportsPage] _buildOverviewTab completed');
  }

  Widget _buildOverviewStats() {
    debugPrint('🔍 [ComprehensiveReportsPage] _buildOverviewStats started');

    return _callStackMonitor.executeWithMonitoring('_buildOverviewStats', () {
      // حساب الإحصائيات بشكل محسن
      double totalRevenue = 0;
      double totalExpenses = 0;

      try {
        // حساب الإيرادات من المدفوعات
        for (final payment in _payments) {
          if (payment.type == 'income' || payment.type == 'revenue') {
            totalRevenue += payment.amount;
          } else {
            totalExpenses += payment.amount;
          }
        }
      } catch (e) {
        debugPrint('❌ خطأ في حساب الإحصائيات: $e');
      }

      final netProfit = totalRevenue - totalExpenses;
      debugPrint(
          '🔍 [ComprehensiveReportsPage] _buildOverviewStats - revenue: $totalRevenue, expenses: $totalExpenses, profit: $netProfit');

      return GridView.count(
        crossAxisCount: 2,
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 1.3,
        children: [
          _buildStatCard(
            'إجمالي العملاء',
            '${_clients.length}',
            Icons.people,
            Colors.blue,
          ),
          _buildStatCard(
            'إجمالي المزارع',
            '${_farms.length}',
            Icons.landscape,
            Colors.green,
          ),
          _buildStatCard(
            'إجمالي التسقيات',
            '${_irrigations.length}',
            Icons.water_drop,
            Colors.cyan,
          ),
          _buildStatCard(
            'إجمالي الإيرادات',
            '${totalRevenue.toStringAsFixed(0)} ريال',
            Icons.trending_up,
            Colors.green,
          ),
          _buildStatCard(
            'إجمالي المصروفات',
            '${totalExpenses.toStringAsFixed(0)} ريال',
            Icons.trending_down,
            Colors.red,
          ),
          _buildStatCard(
            'صافي الربح',
            '${netProfit.toStringAsFixed(0)} ريال',
            Icons.account_balance_wallet,
            netProfit >= 0 ? Colors.green : Colors.red,
          ),
        ],
      );
    });
    debugPrint('🔍 [ComprehensiveReportsPage] _buildOverviewStats completed');
  }

  Widget _buildRecentActivities() {
    debugPrint('🔍 [ComprehensiveReportsPage] _buildRecentActivities started');

    return _callStackMonitor.executeWithMonitoring('_buildRecentActivities',
        () {
      // تحسين عملية فلترة البيانات
      List<IrrigationModel> recentIrrigations = [];
      List<PaymentModel> recentPayments = [];

      try {
        final weekAgo = DateTime.now().subtract(const Duration(days: 7));

        // فلترة التسقيات الحديثة
        recentIrrigations = _irrigations
            .where((i) => i.createdAt.isAfter(weekAgo))
            .toList()
          ..sort((a, b) => b.createdAt.compareTo(a.createdAt));

        // فلترة المدفوعات الحديثة
        recentPayments = _payments
            .where((p) => p.paymentDate.isAfter(weekAgo))
            .toList()
          ..sort((a, b) => b.paymentDate.compareTo(a.paymentDate));

        // تحديد عدد العناصر لعرضها (تجنب التحميل الزائد)
        recentIrrigations = recentIrrigations.take(5).toList();
        recentPayments = recentPayments.take(5).toList();
      } catch (e) {
        debugPrint('❌ خطأ في فلترة الأنشطة الحديثة: $e');
      }

      return Column(
        children: [
          if (recentIrrigations.isNotEmpty) ...[
            _buildActivitySection('التسقيات الحديثة', recentIrrigations),
            const SizedBox(height: 16),
          ],
          if (recentPayments.isNotEmpty) ...[
            _buildActivitySection('المدفوعات الحديثة', recentPayments),
          ],
          if (recentIrrigations.isEmpty && recentPayments.isEmpty) ...[
            _buildEmptyActivitiesMessage(),
          ],
        ],
      );
    });
    debugPrint(
        '🔍 [ComprehensiveReportsPage] _buildRecentActivities completed');
  }

  Widget _buildActivitySection(String title, List<dynamic> activities) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        ...activities.map((activity) => _buildActivityItem(activity)),
      ],
    );
  }

  Widget _buildActivityItem(dynamic activity) {
    if (activity is IrrigationModel) {
      return Card(
        margin: const EdgeInsets.only(bottom: 8),
        child: ListTile(
          leading: const Icon(Icons.water_drop, color: Colors.blue),
          title: Text('تسقية مزرعة ${activity.farmId}'),
          subtitle: Text('التكلفة: ${activity.cost.toStringAsFixed(2)} ريال'),
          trailing: Text(
            DateFormat('dd/MM/yyyy').format(activity.createdAt),
            style: const TextStyle(fontSize: 12),
          ),
        ),
      );
    } else if (activity is PaymentModel) {
      return Card(
        margin: const EdgeInsets.only(bottom: 8),
        child: ListTile(
          leading: Icon(
            activity.type == 'income' ? Icons.trending_up : Icons.trending_down,
            color: activity.type == 'income' ? Colors.green : Colors.red,
          ),
          title: Text('دفعة ${activity.type == 'income' ? 'إيراد' : 'مصروف'}'),
          subtitle: Text('المبلغ: ${activity.amount.toStringAsFixed(2)} ريال'),
          trailing: Text(
            DateFormat('dd/MM/yyyy').format(activity.paymentDate),
            style: const TextStyle(fontSize: 12),
          ),
        ),
      );
    }
    return const SizedBox.shrink();
  }

  Widget _buildEmptyActivitiesMessage() {
    return Container(
      padding: const EdgeInsets.all(32),
      child: Column(
        children: [
          Icon(
            Icons.history,
            size: 48,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد أنشطة حديثة',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChartsSection() {
    debugPrint('🔍 [ComprehensiveReportsPage] _buildChartsSection started');
    return Container(
      height: 200,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          const Text(
            'إحصائيات شهرية',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: _buildMonthlyChart(),
          ),
        ],
      ),
    );
    debugPrint('🔍 [ComprehensiveReportsPage] _buildChartsSection completed');
  }

  Widget _buildMonthlyChart() {
    // تبسيط الرسم البياني لتجنب التعليق
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildChartBar('يناير', 0.3, Colors.blue),
              _buildChartBar('فبراير', 0.5, Colors.green),
              _buildChartBar('مارس', 0.7, Colors.orange),
              _buildChartBar('أبريل', 0.4, Colors.purple),
            ],
          ),
          const SizedBox(height: 16),
          const Text(
            'معدل النشاط الشهري',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChartBar(String label, double height, Color color) {
    return Column(
      children: [
        Container(
          width: 30,
          height: 100 * height,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(4),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: const TextStyle(fontSize: 10),
        ),
      ],
    );
  }

  // تبويب العملاء
  Widget _buildClientsTab() {
    debugPrint('🔍 [ComprehensiveReportsPage] _buildClientsTab started');
    return _callStackMonitor.executeWithMonitoring('_buildClientsTab', () {
      return SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionHeader('إحصائيات العملاء', Icons.people),
            const SizedBox(height: 16),
            _buildClientsStats(),
            const SizedBox(height: 24),
            _buildSectionHeader('قائمة العملاء', Icons.list),
            const SizedBox(height: 16),
            _buildClientsList(),
          ],
        ),
      );
    });
    debugPrint('🔍 [ComprehensiveReportsPage] _buildClientsTab completed');
  }

  Widget _buildClientsStats() {
    debugPrint('🔍 [ComprehensiveReportsPage] _buildClientsStats started');

    return _callStackMonitor.executeWithMonitoring('_buildClientsStats', () {
      int activeClients = 0;
      int clientsWithAccounts = 0;
      double averageFarmsPerClient = 0;

      try {
        // حساب العملاء النشطين
        activeClients = _clients
            .where((c) => _irrigations.any((i) => i.clientId == c.id))
            .length;

        // حساب العملاء ذوي الحسابات
        clientsWithAccounts = _accounts.length;

        // حساب متوسط المزارع للعميل
        averageFarmsPerClient =
            _clients.isNotEmpty ? _farms.length / _clients.length : 0;
      } catch (e) {
        debugPrint('❌ خطأ في حساب إحصائيات العملاء: $e');
      }

      debugPrint(
          '🔍 [ComprehensiveReportsPage] _buildClientsStats - activeClients: $activeClients, clientsWithAccounts: $clientsWithAccounts, averageFarmsPerClient: $averageFarmsPerClient');

      return GridView.count(
        crossAxisCount: 2,
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 1.3,
        children: [
          _buildStatCard(
            'إجمالي العملاء',
            '${_clients.length}',
            Icons.people,
            Colors.blue,
          ),
          _buildStatCard(
            'العملاء النشطون',
            '$activeClients',
            Icons.person_add,
            Colors.green,
          ),
          _buildStatCard(
            'العملاء ذوو الحسابات',
            '$clientsWithAccounts',
            Icons.account_balance,
            Colors.orange,
          ),
          _buildStatCard(
            'متوسط المزارع للعميل',
            averageFarmsPerClient.toStringAsFixed(1),
            Icons.landscape,
            Colors.purple,
          ),
        ],
      );
    });
    debugPrint('🔍 [ComprehensiveReportsPage] _buildClientsStats completed');
  }

  Widget _buildClientsList() {
    debugPrint('🔍 [ComprehensiveReportsPage] _buildClientsList started');

    return _callStackMonitor.executeWithMonitoring('_buildClientsList', () {
      if (_clients.isEmpty) {
        debugPrint(
            '🔍 [ComprehensiveReportsPage] _buildClientsList - no clients');
        return _buildEmptyState('لا يوجد عملاء', Icons.people);
      }
      debugPrint(
          '🔍 [ComprehensiveReportsPage] _buildClientsList - building list for ${_clients.length} clients');

      return Card(
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: _clients.length,
          separatorBuilder: (context, index) => const Divider(height: 1),
          itemBuilder: (context, index) {
            final client = _clients[index];

            // حساب الإحصائيات بشكل محسن
            int clientFarms = 0;
            int clientIrrigations = 0;
            bool hasAccount = false;

            try {
              clientFarms = _farms.where((f) => f.clientId == client.id).length;
              clientIrrigations =
                  _irrigations.where((i) => i.clientId == client.id).length;
              hasAccount = _accounts.any((a) => a.clientId == client.id);
            } catch (e) {
              debugPrint('❌ خطأ في حساب إحصائيات العميل: $e');
            }

            return ListTile(
              leading: CircleAvatar(
                backgroundColor: hasAccount
                    ? Colors.green.withOpacity(0.1)
                    : Colors.grey.withOpacity(0.1),
                child: Icon(
                  hasAccount ? Icons.person : Icons.person_outline,
                  color: hasAccount ? Colors.green : Colors.grey,
                ),
              ),
              title: Row(
                children: [
                  Expanded(
                    child: Text(
                      client.name,
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.print, color: AppTheme.primaryColor),
                    tooltip: 'طباعة كشف الحساب',
                    onPressed: () => _printClientStatement(client),
                  ),
                ],
              ),
              subtitle: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (client.phone != null) Text('الهاتف: ${client.phone}'),
                  Text('المزارع: $clientFarms • التسقيات: $clientIrrigations'),
                ],
              ),
              trailing: SizedBox(
                width: 70,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (hasAccount)
                      const Icon(Icons.verified, color: Colors.green, size: 16),
                    Text(
                      _dateFormat.format(client.createdAt),
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 12,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              onTap: () => _showClientDetails(client),
            );
          },
        ),
      );
    });
    debugPrint('🔍 [ComprehensiveReportsPage] _buildClientsList completed');
  }

  // تبويب المزارع
  Widget _buildFarmsTab() {
    debugPrint('🔍 [ComprehensiveReportsPage] _buildFarmsTab started');
    return _callStackMonitor.executeWithMonitoring('_buildFarmsTab', () {
      return SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionHeader('إحصائيات المزارع', Icons.landscape),
            const SizedBox(height: 16),
            _buildFarmsStats(),
            const SizedBox(height: 24),
            _buildSectionHeader('قائمة المزارع', Icons.list),
            const SizedBox(height: 16),
            _buildFarmsList(),
          ],
        ),
      );
    });
    debugPrint('🔍 [ComprehensiveReportsPage] _buildFarmsTab completed');
  }

  Widget _buildFarmsStats() {
    debugPrint('🔍 [ComprehensiveReportsPage] _buildFarmsStats started');

    return _callStackMonitor.executeWithMonitoring('_buildFarmsStats', () {
      int activeFarms = 0;
      double averageIrrigationsPerFarm = 0;

      try {
        // حساب المزارع النشطة
        activeFarms = _farms
            .where((f) => _irrigations.any((i) => i.farmId == f.id))
            .length;

        // حساب متوسط التسقيات للمزرعة
        averageIrrigationsPerFarm =
            _farms.isNotEmpty ? _irrigations.length / _farms.length : 0;
      } catch (e) {
        debugPrint('❌ خطأ في حساب إحصائيات المزارع: $e');
      }

      debugPrint(
          '🔍 [ComprehensiveReportsPage] _buildFarmsStats - activeFarms: $activeFarms, averageIrrigationsPerFarm: $averageIrrigationsPerFarm');

      return GridView.count(
        crossAxisCount: 2,
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 1.3,
        children: [
          _buildStatCard(
            'إجمالي المزارع',
            '${_farms.length}',
            Icons.landscape,
            Colors.green,
          ),
          _buildStatCard(
            'المزارع النشطة',
            '$activeFarms',
            Icons.agriculture,
            Colors.blue,
          ),
          _buildStatCard(
            'متوسط التسقيات للمزرعة',
            averageIrrigationsPerFarm.toStringAsFixed(1),
            Icons.water_drop,
            Colors.cyan,
          ),
          _buildStatCard(
            'إجمالي التسقيات',
            '${_irrigations.length}',
            Icons.timeline,
            Colors.orange,
          ),
        ],
      );
    });
    debugPrint('🔍 [ComprehensiveReportsPage] _buildFarmsStats completed');
  }

  Widget _buildFarmsList() {
    debugPrint('🔍 [ComprehensiveReportsPage] _buildFarmsList started');

    return _callStackMonitor.executeWithMonitoring('_buildFarmsList', () {
      if (_farms.isEmpty) {
        debugPrint('🔍 [ComprehensiveReportsPage] _buildFarmsList - no farms');
        return _buildEmptyState('لا توجد مزارع', Icons.landscape);
      }
      debugPrint(
          '🔍 [ComprehensiveReportsPage] _buildFarmsList - building list for ${_farms.length} farms');

      return Card(
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: _farms.length,
          separatorBuilder: (context, index) => const Divider(height: 1),
          itemBuilder: (context, index) {
            final farm = _farms[index];

            // حساب الإحصائيات بشكل محسن
            ClientModel? client;
            int farmIrrigations = 0;
            bool isActive = false;

            try {
              client = _clients.firstWhere(
                (c) => c.id == farm.clientId,
                orElse: () => ClientModel(
                  name: 'عميل غير معروف',
                  createdAt: DateTime.now(),
                  updatedAt: DateTime.now(),
                ),
              );
              farmIrrigations =
                  _irrigations.where((i) => i.farmId == farm.id).length;
              isActive = farmIrrigations > 0;
            } catch (e) {
              debugPrint('❌ خطأ في حساب إحصائيات المزرعة: $e');
              client = ClientModel(
                name: 'عميل غير معروف',
                createdAt: DateTime.now(),
                updatedAt: DateTime.now(),
              );
            }

            return ListTile(
              leading: CircleAvatar(
                backgroundColor: isActive
                    ? Colors.green.withValues(alpha: 0.1)
                    : Colors.grey.withValues(alpha: 0.1),
                child: Icon(
                  isActive ? Icons.eco : Icons.landscape,
                  color: isActive ? Colors.green : Colors.grey,
                ),
              ),
              title: Text(
                farm.name,
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              subtitle: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('العميل: ${client.name}'),
                  if (farm.location != null) Text('الموقع: ${farm.location}'),
                  Text('التسقيات: $farmIrrigations'),
                ],
              ),
              trailing: SizedBox(
                width: 70,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (isActive)
                      const Icon(Icons.check_circle,
                          color: Colors.green, size: 16),
                    Text(
                      _dateFormat.format(farm.createdAt),
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 12,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              onTap: () => _showFarmDetails(farm),
            );
          },
        ),
      );
    });
    debugPrint('🔍 [ComprehensiveReportsPage] _buildFarmsList completed');
  }

  // تبويب التسقيات
  Widget _buildIrrigationsTab() {
    debugPrint('🔍 [ComprehensiveReportsPage] _buildIrrigationsTab started');
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader('إحصائيات التسقيات', Icons.water_drop),
          const SizedBox(height: 16),
          _buildIrrigationsStats(),
          const SizedBox(height: 24),
          _buildSectionHeader('قائمة التسقيات', Icons.list),
          const SizedBox(height: 16),
          _buildIrrigationsList(),
        ],
      ),
    );
    debugPrint('🔍 [ComprehensiveReportsPage] _buildIrrigationsTab completed');
  }

  Widget _buildIrrigationsStats() {
    debugPrint('🔍 [ComprehensiveReportsPage] _buildIrrigationsStats started');

    double totalCost = 0.0;
    double totalDiesel = 0.0;
    int totalMinutes = 0;
    double averageCost = 0.0;

    try {
      // حساب الإحصائيات بشكل محسن
      for (final irrigation in _irrigations) {
        totalCost += irrigation.cost;
        totalDiesel += irrigation.dieselConsumption;
        totalMinutes += irrigation.duration;
      }

      averageCost =
          _irrigations.isNotEmpty ? totalCost / _irrigations.length : 0;
    } catch (e) {
      debugPrint('❌ خطأ في حساب إحصائيات التسقيات: $e');
    }

    debugPrint(
        '🔍 [ComprehensiveReportsPage] _buildIrrigationsStats - totalCost: $totalCost, totalDiesel: $totalDiesel, totalMinutes: $totalMinutes, averageCost: $averageCost');

    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 1.3,
      children: [
        _buildStatCard(
          'إجمالي التسقيات',
          '${_irrigations.length}',
          Icons.water_drop,
          Colors.blue,
        ),
        _buildStatCard(
          'إجمالي التكلفة',
          '${totalCost.toStringAsFixed(0)} ريال',
          Icons.attach_money,
          Colors.green,
        ),
        _buildStatCard(
          'إجمالي الديزل',
          '${totalDiesel.toStringAsFixed(1)} لتر',
          Icons.local_gas_station,
          Colors.orange,
        ),
        _buildStatCard(
          'متوسط التكلفة',
          '${averageCost.toStringAsFixed(0)} ريال',
          Icons.analytics,
          Colors.purple,
        ),
      ],
    );
    debugPrint(
        '🔍 [ComprehensiveReportsPage] _buildIrrigationsStats completed');
  }

  Widget _buildIrrigationsList() {
    debugPrint('🔍 [ComprehensiveReportsPage] _buildIrrigationsList started');
    if (_irrigations.isEmpty) {
      debugPrint(
          '🔍 [ComprehensiveReportsPage] _buildIrrigationsList - no irrigations');
      return _buildEmptyState('لا توجد تسقيات', Icons.water_drop);
    }
    debugPrint(
        '🔍 [ComprehensiveReportsPage] _buildIrrigationsList - building list for ${_irrigations.length} irrigations');

    // ترتيب التسقيات حسب التاريخ (الأحدث أولاً)
    final sortedIrrigations = List<IrrigationModel>.from(_irrigations)
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: sortedIrrigations.length,
        separatorBuilder: (context, index) => const Divider(height: 1),
        itemBuilder: (context, index) {
          final irrigation = sortedIrrigations[index];
          final client = _clients.firstWhere(
            (c) => c.id == irrigation.clientId,
            orElse: () => ClientModel(
              name: 'عميل غير معروف',
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
            ),
          );
          final farm = _farms.firstWhere(
            (f) => f.id == irrigation.farmId,
            orElse: () => FarmModel(
              clientId: irrigation.clientId,
              name: 'مزرعة غير معروفة',
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
            ),
          );

          return ListTile(
            leading: CircleAvatar(
              backgroundColor: Colors.blue.withValues(alpha: 0.1),
              child: const Icon(Icons.water_drop, color: Colors.blue),
            ),
            title: Text(
              '${client.name} - ${farm.name}',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('التكلفة: ${irrigation.cost.toStringAsFixed(0)} ريال'),
                Text(
                    'المدة: ${(irrigation.duration / 60).toStringAsFixed(1)} ساعة'),
                Text(
                    'الديزل: ${irrigation.dieselConsumption.toStringAsFixed(1)} لتر'),
              ],
            ),
            trailing: SizedBox(
              width: 70,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    _dateFormat.format(irrigation.startTime),
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  Text(
                    DateFormat('HH:mm').format(irrigation.startTime),
                    style: TextStyle(
                      color: Colors.grey[500],
                      fontSize: 10,
                    ),
                  ),
                ],
              ),
            ),
            onTap: () => _showIrrigationDetails(irrigation),
          );
        },
      ),
    );
    debugPrint('🔍 [ComprehensiveReportsPage] _buildIrrigationsList completed');
  }

  // تبويب المالية
  Widget _buildFinancialTab() {
    debugPrint('🔍 [ComprehensiveReportsPage] _buildFinancialTab started');
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader('الإحصائيات المالية', Icons.account_balance),
          const SizedBox(height: 16),
          _buildFinancialStats(),
          const SizedBox(height: 24),
          _buildSectionHeader('أرصدة الصناديق', Icons.account_balance_wallet),
          const SizedBox(height: 16),
          _buildCashboxesList(),
          const SizedBox(height: 24),
          _buildSectionHeader('أرصدة العملاء', Icons.people),
          const SizedBox(height: 16),
          _buildAccountsList(),
        ],
      ),
    );
    debugPrint('🔍 [ComprehensiveReportsPage] _buildFinancialTab completed');
  }

  Widget _buildFinancialStats() {
    debugPrint('🔍 [ComprehensiveReportsPage] _buildFinancialStats started');
    double totalCashBalance = 0.0;
    double totalDieselBalance = 0.0;

    for (final cashbox in _cashboxes) {
      if (cashbox.type == 'cash') {
        totalCashBalance += cashbox.balance;
      } else {
        totalDieselBalance += cashbox.balance;
      }
    }

    double totalClientCash = 0.0;
    double totalClientDiesel = 0.0;
    for (final account in _accounts) {
      totalClientCash += account.cashBalance;
      totalClientDiesel += account.dieselBalance;
    }

    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 1.3,
      children: [
        _buildStatCard(
          'إجمالي النقد',
          '${totalCashBalance.toStringAsFixed(0)} ريال',
          Icons.attach_money,
          Colors.green,
        ),
        _buildStatCard(
          'إجمالي الديزل',
          '${totalDieselBalance.toStringAsFixed(1)} لتر',
          Icons.local_gas_station,
          Colors.orange,
        ),
        _buildStatCard(
          'أرصدة العملاء النقدية',
          '${totalClientCash.toStringAsFixed(0)} ريال',
          Icons.account_circle,
          totalClientCash >= 0 ? Colors.green : Colors.red,
        ),
        _buildStatCard(
          'أرصدة العملاء الديزل',
          '${totalClientDiesel.toStringAsFixed(1)} لتر',
          Icons.local_gas_station,
          totalClientDiesel >= 0 ? Colors.green : Colors.red,
        ),
        _buildStatCard(
          'عدد الصناديق',
          '${_cashboxes.length}',
          Icons.account_balance_wallet,
          Colors.blue,
        ),
        _buildStatCard(
          'عدد الحسابات',
          '${_accounts.length}',
          Icons.people,
          Colors.purple,
        ),
      ],
    );
    debugPrint('🔍 [ComprehensiveReportsPage] _buildFinancialStats completed');
  }

  Widget _buildCashboxesList() {
    debugPrint('🔍 [ComprehensiveReportsPage] _buildCashboxesList started');
    if (_cashboxes.isEmpty) {
      debugPrint(
          '🔍 [ComprehensiveReportsPage] _buildCashboxesList - no cashboxes');
      return _buildEmptyState('لا توجد صناديق', Icons.account_balance_wallet);
    }
    debugPrint(
        '🔍 [ComprehensiveReportsPage] _buildCashboxesList - building list for ${_cashboxes.length} cashboxes');

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: _cashboxes.length,
        separatorBuilder: (context, index) => const Divider(height: 1),
        itemBuilder: (context, index) {
          final cashbox = _cashboxes[index];
          final isCash = cashbox.type == 'cash';
          final isPositive = cashbox.balance >= 0;

          return ListTile(
            leading: CircleAvatar(
              backgroundColor: isCash
                  ? Colors.green.withValues(alpha: 0.1)
                  : Colors.orange.withValues(alpha: 0.1),
              child: Icon(
                isCash ? Icons.attach_money : Icons.local_gas_station,
                color: isCash ? Colors.green : Colors.orange,
              ),
            ),
            title: Text(
              cashbox.name,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Text(isCash ? 'صندوق نقدي' : 'صندوق ديزل'),
            trailing: SizedBox(
              width: 90,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    '${cashbox.balance.toStringAsFixed(isCash ? 0 : 1)} ${isCash ? 'ريال' : 'لتر'}',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: isPositive ? Colors.green : Colors.red,
                      fontSize: 16,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  if (!isPositive)
                    const Icon(Icons.warning, color: Colors.red, size: 16),
                ],
              ),
            ),
            onTap: () => _showCashboxDetails(cashbox),
          );
        },
      ),
    );
    debugPrint('🔍 [ComprehensiveReportsPage] _buildCashboxesList completed');
  }

  Widget _buildAccountsList() {
    debugPrint('🔍 [ComprehensiveReportsPage] _buildAccountsList started');
    if (_accounts.isEmpty) {
      debugPrint(
          '🔍 [ComprehensiveReportsPage] _buildAccountsList - no accounts');
      return _buildEmptyState('لا توجد حسابات عملاء', Icons.people);
    }
    debugPrint(
        '🔍 [ComprehensiveReportsPage] _buildAccountsList - building list for ${_accounts.length} accounts');

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: _accounts.length,
        separatorBuilder: (context, index) => const Divider(height: 1),
        itemBuilder: (context, index) {
          final account = _accounts[index];
          final client = _clients.firstWhere(
            (c) => c.id == account.clientId,
            orElse: () => ClientModel(
              name: 'عميل غير معروف',
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
            ),
          );

          final hasCashDebt = account.cashBalance < 0;
          final hasDieselDebt = account.dieselBalance < 0;
          final hasDebt = hasCashDebt || hasDieselDebt;

          return ListTile(
            leading: CircleAvatar(
              backgroundColor: hasDebt
                  ? Colors.red.withValues(alpha: 0.1)
                  : Colors.green.withValues(alpha: 0.1),
              child: Icon(
                hasDebt ? Icons.warning : Icons.account_circle,
                color: hasDebt ? Colors.red : Colors.green,
              ),
            ),
            title: Text(
              client.name,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'نقدي: ${account.cashBalance.toStringAsFixed(0)} ريال',
                  style: TextStyle(
                    color: account.cashBalance >= 0 ? Colors.green : Colors.red,
                  ),
                ),
                Text(
                  'ديزل: ${account.dieselBalance.toStringAsFixed(1)} لتر',
                  style: TextStyle(
                    color:
                        account.dieselBalance >= 0 ? Colors.green : Colors.red,
                  ),
                ),
              ],
            ),
            trailing: hasDebt
                ? const Icon(Icons.priority_high, color: Colors.red)
                : const Icon(Icons.check_circle, color: Colors.green),
            onTap: () => _showAccountDetails(account),
          );
        },
      ),
    );
    debugPrint('🔍 [ComprehensiveReportsPage] _buildAccountsList completed');
  }

  // الأدوات المساعدة
  Widget _buildSectionHeader(String title, IconData icon) {
    debugPrint(
        '🔍 [ComprehensiveReportsPage] _buildSectionHeader - title: $title');
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: AppTheme.primaryColor, size: 20),
        ),
        const SizedBox(width: 12),
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryColor,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(
      String title, String value, IconData icon, Color color) {
    debugPrint(
        '🔍 [ComprehensiveReportsPage] _buildStatCard - title: $title, value: $value');
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.2)),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(icon, color: color, size: 24),
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(String message, IconData icon) {
    debugPrint(
        '🔍 [ComprehensiveReportsPage] _buildEmptyState - message: $message');
    return Container(
      padding: const EdgeInsets.all(32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    debugPrint(
        '🔍 [ComprehensiveReportsPage] _buildFloatingActionButton started');
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        FloatingActionButton.extended(
          onPressed: () {
            Navigator.pushNamed(context, '/custom-reports');
          },
          backgroundColor: Colors.green,
          icon: const Icon(Icons.analytics, color: Colors.white),
          label: const Text(
            'التقارير المخصصة',
            style: TextStyle(color: Colors.white),
          ),
        ),
        const SizedBox(width: 16),
        FloatingActionButton.extended(
          onPressed: _exportReport,
          backgroundColor: AppTheme.primaryColor,
          icon: const Icon(Icons.file_download, color: Colors.white),
          label: const Text(
            'تصدير التقرير',
            style: TextStyle(color: Colors.white),
          ),
        ),
      ],
    );
    debugPrint(
        '🔍 [ComprehensiveReportsPage] _buildFloatingActionButton completed');
  }

  // الحسابات والعمليات
  double _calculateTotalRevenue() {
    try {
      return _payments
          .where((p) => p.type == 'income' || p.type == 'revenue')
          .fold(0.0, (sum, payment) => sum + payment.amount);
    } catch (e) {
      debugPrint('❌ خطأ في حساب الإيرادات: $e');
      return 0.0;
    }
  }

  double _calculateTotalExpenses() {
    try {
      return _payments
          .where((p) => p.type != 'income' && p.type != 'revenue')
          .fold(0.0, (sum, payment) => sum + payment.amount);
    } catch (e) {
      debugPrint('❌ خطأ في حساب المصروفات: $e');
      return 0.0;
    }
  }

  // عرض التفاصيل
  void _showClientDetails(ClientModel client) {
    debugPrint(
        '🔍 [ComprehensiveReportsPage] _showClientDetails - client: ${client.name}');
    Navigator.pushNamed(context, '/client-details', arguments: client.id);
  }

  void _showFarmDetails(FarmModel farm) {
    debugPrint(
        '🔍 [ComprehensiveReportsPage] _showFarmDetails - farm: ${farm.name}');
    Navigator.pushNamed(context, '/farm-details', arguments: farm.id);
  }

  void _showIrrigationDetails(IrrigationModel irrigation) {
    debugPrint(
        '🔍 [ComprehensiveReportsPage] _showIrrigationDetails - irrigation: ${irrigation.id}');
    Navigator.pushNamed(context, '/irrigation-details',
        arguments: irrigation.id);
  }

  void _showCashboxDetails(CashboxModel cashbox) {
    debugPrint(
        '🔍 [ComprehensiveReportsPage] _showCashboxDetails - cashbox: ${cashbox.name}');
    Navigator.pushNamed(context, '/cashbox-details', arguments: cashbox.id);
  }

  void _showAccountDetails(ClientAccountModel account) {
    debugPrint(
        '🔍 [ComprehensiveReportsPage] _showAccountDetails - account: ${account.clientId}');
    Navigator.pushNamed(context, '/client-account-details',
        arguments: account.clientId);
  }

  // تصدير التقرير
  void _exportReport() {
    debugPrint('🔍 [ComprehensiveReportsPage] _exportReport started');
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('سيتم تطوير ميزة تصدير التقرير قريباً'),
        backgroundColor: AppTheme.primaryColor,
      ),
    );
    debugPrint('🔍 [ComprehensiveReportsPage] _exportReport completed');
  }

  // أضف دالة توليد وطباعة كشف الحساب PDF
  Future<void> _printClientStatement(ClientModel client) async {
    try {
      // جلب كشف الحساب من الخدمة
      final statement = await AccountStatementService().generateClientStatement(
        clientId: client.id.toString(),
        fromDate: _startDate!,
        toDate: _endDate!,
      );

      // توليد PDF احترافي بالعربية
      final pdfFile = await PdfService().createClientStatementPdf(
        statement: statement,
        logoAssetPath: 'assets/images/app_logo.png', // عدل المسار إذا لزم
      );

      // مشاركة أو عرض PDF مباشرة
      await Printing.sharePdf(
        bytes: await pdfFile.readAsBytes(),
        filename: 'كشف حساب ${client.name}.pdf',
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('حدث خطأ أثناء توليد كشف الحساب: $e')),
      );
    }
  }
}

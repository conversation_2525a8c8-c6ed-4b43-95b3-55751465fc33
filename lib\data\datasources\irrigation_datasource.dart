import 'package:flutter/foundation.dart';
import 'package:untitled/data/datasources/database_helper.dart';
import 'package:untitled/data/models/irrigation_model.dart';

class IrrigationDataSource {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // إضافة تسقية جديدة
  Future<int> addIrrigation(IrrigationModel irrigation) async {
    try {
      final db = await _databaseHelper.database;

      // التحقق من صحة البيانات قبل الإدراج
      if (irrigation.clientId <= 0) {
        throw Exception('معرف العميل غير صحيح');
      }
      if (irrigation.farmId <= 0) {
        throw Exception('معرف المزرعة غير صحيح');
      }
      if (irrigation.duration <= 0) {
        throw Exception('مدة التسقية يجب أن تكون أكبر من صفر');
      }

      final irrigationData = irrigation.toMap();
      debugPrint('💾 بيانات التسقية للحفظ: $irrigationData');

      final result = await db.insert('irrigations', irrigationData);
      debugPrint('✅ تم حفظ التسقية بنجاح! ID: $result');

      return result;
    } catch (e) {
      debugPrint('🚨 خطأ في حفظ التسقية: $e');
      rethrow;
    }
  }

  // الحصول على جميع التسقيات
  Future<List<IrrigationModel>> getAllIrrigations() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query('irrigations');
    return List.generate(maps.length, (i) {
      return IrrigationModel.fromMap(maps[i]);
    });
  }

  // الحصول على تسقية بواسطة المعرف
  Future<IrrigationModel?> getIrrigationById(int id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'irrigations',
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return IrrigationModel.fromMap(maps.first);
    }
    return null;
  }

  // الحصول على تسقيات عميل معين
  Future<List<IrrigationModel>> getIrrigationsByClientId(int clientId) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'irrigations',
      where: 'client_id = ?',
      whereArgs: [clientId],
    );
    return List.generate(maps.length, (i) {
      return IrrigationModel.fromMap(maps[i]);
    });
  }

  // الحصول على تسقيات مزرعة معينة
  Future<List<IrrigationModel>> getIrrigationsByFarmId(int farmId) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'irrigations',
      where: 'farm_id = ?',
      whereArgs: [farmId],
    );
    return List.generate(maps.length, (i) {
      return IrrigationModel.fromMap(maps[i]);
    });
  }

  // الحصول على تسقيات في فترة زمنية معينة
  Future<List<IrrigationModel>> getIrrigationsByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final db = await _databaseHelper.database;
    
    // تحديد بداية ونهاية اليوم للفلترة الصحيحة
    final startOfDay = DateTime(startDate.year, startDate.month, startDate.day);
    final endOfDay = DateTime(endDate.year, endDate.month, endDate.day, 23, 59, 59);
    
    debugPrint('🔍 البحث عن التسقيات من ${startOfDay.toIso8601String()} إلى ${endOfDay.toIso8601String()}');
    
    final List<Map<String, dynamic>> maps = await db.query(
      'irrigations',
      where: 'start_time BETWEEN ? AND ?',
      whereArgs: [startOfDay.toIso8601String(), endOfDay.toIso8601String()],
      orderBy: 'start_time DESC',
    );
    
    debugPrint('📊 تم العثور على ${maps.length} تسقية في الفترة المحددة');
    
    return List.generate(maps.length, (i) {
      return IrrigationModel.fromMap(maps[i]);
    });
  }

  // تحديث بيانات تسقية
  Future<int> updateIrrigation(IrrigationModel irrigation) async {
    final db = await _databaseHelper.database;
    return await db.update(
      'irrigations',
      irrigation.toMap(),
      where: 'id = ?',
      whereArgs: [irrigation.id],
    );
  }

  // حذف تسقية
  Future<int> deleteIrrigation(int id) async {
    final db = await _databaseHelper.database;
    return await db.delete(
      'irrigations',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // الحصول على عدد التسقيات
  Future<int> getIrrigationsCount() async {
    final db = await _databaseHelper.database;
    final result = await db.rawQuery('SELECT COUNT(*) FROM irrigations');
    return result.first.values.first as int? ?? 0;
  }

  // الحصول على عدد تسقيات اليوم
  Future<int> getTodayIrrigationsCount() async {
    final db = await _databaseHelper.database;
    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);
    final endOfDay = DateTime(today.year, today.month, today.day, 23, 59, 59);

    final result = await db.rawQuery(
      'SELECT COUNT(*) FROM irrigations WHERE start_time BETWEEN ? AND ?',
      [startOfDay.toIso8601String(), endOfDay.toIso8601String()],
    );
    return result.first.values.first as int? ?? 0;
  }

  // الحصول على إجمالي استهلاك الديزل
  Future<double> getTotalDieselConsumption() async {
    final db = await _databaseHelper.database;
    final result = await db.rawQuery(
      'SELECT SUM(diesel_consumption) as total FROM irrigations',
    );
    return result.first['total'] as double? ?? 0.0;
  }

  // الحصول على إجمالي التكلفة
  Future<double> getTotalCost() async {
    final db = await _databaseHelper.database;
    final result = await db.rawQuery(
      'SELECT SUM(cost) as total FROM irrigations',
    );
    return result.first['total'] as double? ?? 0.0;
  }
}

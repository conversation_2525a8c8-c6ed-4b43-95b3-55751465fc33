import 'dart:io';
import 'package:flutter/material.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:excel/excel.dart';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:open_file/open_file.dart';

/// خدمة تصدير التقارير - تدعم PDF و Excel فقط
class ReportExportService {
  static const String _appName = 'نظام إدارة التسقيات';

  /// تصدير البيانات إلى PDF
  static Future<void> exportToPDF({
    required String title,
    required List<Map<String, dynamic>> data,
    required List<String> headers,
    String? subtitle,
    Map<String, dynamic>? summary,
  }) async {
    try {
      // إنشاء مستند PDF
      final pdf = pw.Document();

      // إضافة صفحة العنوان
      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          build: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // العنوان الرئيسي
                pw.Header(
                  level: 0,
                  child: pw.Text(
                    title,
                    style: pw.TextStyle(
                      fontSize: 24,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                ),

                pw.SizedBox(height: 10),

                // اسم التطبيق
                pw.Text(
                  _appName,
                  style: pw.TextStyle(
                    fontSize: 14,
                    color: PdfColors.grey600,
                  ),
                ),

                pw.SizedBox(height: 5),

                // التاريخ والوقت
                pw.Text(
                  'تاريخ التصدير: ${DateFormat('yyyy/MM/dd HH:mm').format(DateTime.now())}',
                  style: pw.TextStyle(
                    fontSize: 12,
                    color: PdfColors.grey500,
                  ),
                ),

                if (subtitle != null) ...[
                  pw.SizedBox(height: 10),
                  pw.Text(
                    subtitle,
                    style: pw.TextStyle(
                      fontSize: 16,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                ],

                if (summary != null) ...[
                  pw.SizedBox(height: 20),
                  _buildSummarySection(summary),
                ],

                pw.SizedBox(height: 20),

                // جدول البيانات
                if (data.isNotEmpty) ...[
                  pw.Text(
                    'تفاصيل البيانات',
                    style: pw.TextStyle(
                      fontSize: 18,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                  pw.SizedBox(height: 10),
                  _buildDataTable(data, headers),
                ],
              ],
            );
          },
        ),
      );

      // حفظ الملف
      final fileName =
          '${title.replaceAll(' ', '_')}_${DateFormat('yyyyMMdd_HHmm').format(DateTime.now())}.pdf';
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/$fileName');
      await file.writeAsBytes(await pdf.save());

      // فتح الملف
      await OpenFile.open(file.path);

      debugPrint('✅ تم تصدير التقرير إلى PDF بنجاح: $fileName');
    } catch (e) {
      debugPrint('❌ خطأ في تصدير PDF: $e');
      rethrow;
    }
  }

  /// تصدير البيانات إلى Excel
  static Future<void> exportToExcel({
    required String title,
    required List<Map<String, dynamic>> data,
    required List<String> headers,
    String? subtitle,
    Map<String, dynamic>? summary,
  }) async {
    try {
      // إنشاء ملف Excel
      final excel = Excel.createExcel();
      final sheet = excel['التقرير'];

      // إعداد العنوان
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 0))
        ..value = title
        ..cellStyle = CellStyle(
          bold: true,
          fontSize: 16,
          horizontalAlign: HorizontalAlign.Center,
        );

      // دمج الخلايا للعنوان
      sheet.merge(
          CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 0),
          CellIndex.indexByColumnRow(
              columnIndex: headers.length - 1, rowIndex: 0));

      int currentRow = 2;

      // إضافة معلومات التقرير
      sheet.cell(
          CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
        ..value = 'اسم التطبيق: $_appName';
      currentRow++;

      sheet.cell(
          CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
        ..value =
            'تاريخ التصدير: ${DateFormat('yyyy/MM/dd HH:mm').format(DateTime.now())}';
      currentRow++;

      if (subtitle != null) {
        currentRow++;
        sheet.cell(
            CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
          ..value = subtitle
          ..cellStyle = CellStyle(bold: true, fontSize: 14);
        currentRow++;
      }

      // إضافة الملخص إذا وجد
      if (summary != null) {
        currentRow++;
        sheet.cell(
            CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow))
          ..value = 'ملخص التقرير'
          ..cellStyle = CellStyle(bold: true, fontSize: 14);
        currentRow++;

        for (final entry in summary.entries) {
          sheet
              .cell(CellIndex.indexByColumnRow(
                  columnIndex: 0, rowIndex: currentRow))
              .value = 'test';
          currentRow++;
        }
      }

      // إضافة رؤوس الجدول
      currentRow++;
      for (int i = 0; i < headers.length; i++) {
        sheet.cell(
            CellIndex.indexByColumnRow(columnIndex: i, rowIndex: currentRow))
          ..value = headers[i]
          ..cellStyle = CellStyle(
            bold: true,
            backgroundColorHex: '#E0E0E0',
            horizontalAlign: HorizontalAlign.Center,
          );
      }
      currentRow++;

      // إضافة البيانات
      for (final row in data) {
        for (int i = 0; i < headers.length; i++) {
          final value = row[headers[i]] ?? '';
          sheet.cell(
              CellIndex.indexByColumnRow(columnIndex: i, rowIndex: currentRow))
            ..value = value.toString();
        }
        currentRow++;
      }

      // ضبط عرض الأعمدة (إزالة هذا السطر لأنه غير مدعوم في مكتبة excel)
      // sheet.setColumnWidth(i, 20);

      // حفظ الملف
      final fileName =
          '${title.replaceAll(' ', '_')}_${DateFormat('yyyyMMdd_HHmm').format(DateTime.now())}.xlsx';
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/$fileName');
      await file.writeAsBytes(excel.encode()!);

      // فتح الملف
      await OpenFile.open(file.path);

      debugPrint('✅ تم تصدير التقرير إلى Excel بنجاح: $fileName');
    } catch (e) {
      debugPrint('❌ خطأ في تصدير Excel: $e');
      rethrow;
    }
  }

  /// بناء قسم الملخص في PDF
  static pw.Widget _buildSummarySection(Map<String, dynamic> summary) {
    return pw.Container(
      padding: pw.EdgeInsets.all(10),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey300),
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'ملخص التقرير',
            style: pw.TextStyle(
              fontSize: 16,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
          pw.SizedBox(height: 10),
          ...summary.entries
              .map((entry) => pw.Padding(
                    padding: pw.EdgeInsets.only(bottom: 5),
                    child: pw.Text(
                      '${entry.key}: ${entry.value.toString()}',
                      style: pw.TextStyle(fontSize: 12),
                    ),
                  ))
              .toList(),
        ],
      ),
    );
  }

  /// بناء جدول البيانات في PDF
  static pw.Widget _buildDataTable(
      List<Map<String, dynamic>> data, List<String> headers) {
    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey300),
      children: [
        // رؤوس الجدول
        pw.TableRow(
          decoration: pw.BoxDecoration(color: PdfColors.grey200),
          children: headers
              .map((header) => pw.Padding(
                    padding: pw.EdgeInsets.all(8),
                    child: pw.Text(
                      header,
                      style: pw.TextStyle(
                        fontWeight: pw.FontWeight.bold,
                        fontSize: 12,
                      ),
                      textAlign: pw.TextAlign.center,
                    ),
                  ))
              .toList(),
        ),
        // بيانات الجدول
        ...data.map((row) => pw.TableRow(
              children: headers
                  .map((header) => pw.Padding(
                        padding: pw.EdgeInsets.all(6),
                        child: pw.Text(
                          (row[header] ?? '').toString(),
                          style: pw.TextStyle(fontSize: 10),
                          textAlign: pw.TextAlign.center,
                        ),
                      ))
                  .toList(),
            )),
      ],
    );
  }

  /// عرض حوار اختيار نوع التصدير
  static Future<void> showExportDialog({
    required BuildContext context,
    required String title,
    required List<Map<String, dynamic>> data,
    required List<String> headers,
    String? subtitle,
    Map<String, dynamic>? summary,
  }) async {
    return showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.file_download, color: Colors.blue),
            SizedBox(width: 8),
            Text('تصدير التقرير'),
          ],
        ),
        content: const Text('اختر نوع التصدير المطلوب:'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              exportToPDF(
                title: title,
                data: data,
                headers: headers,
                subtitle: subtitle,
                summary: summary,
              );
            },
            child: const Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.picture_as_pdf, color: Colors.red),
                SizedBox(width: 4),
                Text('PDF'),
              ],
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              exportToExcel(
                title: title,
                data: data,
                headers: headers,
                subtitle: subtitle,
                summary: summary,
              );
            },
            child: const Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.table_chart, color: Colors.green),
                SizedBox(width: 4),
                Text('Excel'),
              ],
            ),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }
}
